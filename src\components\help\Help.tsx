import React, { useState, useMemo } from 'react';
import { Search, X } from 'lucide-react';
import { <PERSON><PERSON><PERSON>r, PageHeader } from '../ui/Layout';
import HelpNavigation from './HelpNavigation';
import HelpContent from './HelpContent';
import { helpCategories, contactInfo } from './data/helpContent.enhanced';
import type { HelpNavigation as HelpNavigationType, SearchResult } from '../../types/help';
import { helpDataService } from '../../services/helpDataService';
import './Help.css';

const Help: React.FC = () => {
  const [navigation, setNavigation] = useState<HelpNavigationType>({
    currentCategory: 'quick-start',
    currentSection: 'getting-started',
    currentItem: 'welcome'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  
  // 新增：搜索历史和最近浏览功能
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [recentViewed, setRecentViewed] = useState<{
    categoryId: string;
    sectionId: string;
    itemId: string;
    title: string;
    timestamp: number;
  }[]>([]);

  // 从数据库恢复数据
  React.useEffect(() => {
    const loadData = async () => {
      try {
        const history = await helpDataService.getSearchHistory();
        const recent = await helpDataService.getRecentViewed();
        
        setSearchHistory(history);
        // 转换最近查看的数据格式
        const formattedRecent = recent.map(item => ({
          categoryId: 'quick-start', // 默认值，实际应该根据数据确定
          sectionId: 'getting-started',
          itemId: 'welcome',
          title: item.title,
          timestamp: new Date(item.viewedAt).getTime()
        }));
        setRecentViewed(formattedRecent);
      } catch (error) {
        console.error('加载帮助数据失败:', error);
      }
    };
    
    loadData();
  }, []);

  // 搜索功能
  const performSearch = (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    const results: SearchResult[] = [];
    const searchTerm = query.toLowerCase().trim();

    helpCategories.forEach(category => {
      category.sections.forEach((section: any) => {
        section.children.forEach((item: any) => {
          let score = 0;
          let matchType: 'title' | 'content' | 'tags' = 'content';

          // 标题匹配 (最高权重)
          if (item.title.toLowerCase().includes(searchTerm)) {
            score += 10;
            matchType = 'title';
          }

          // 标签匹配
          if (item.tags?.some((tag: any) => tag.toLowerCase().includes(searchTerm))) {
            score += 8;
            if (matchType === 'content') matchType = 'tags';
          }

          // 内容匹配
          if (typeof item.content === 'string' && item.content.toLowerCase().includes(searchTerm)) {
            score += 5;
          }

          // 步骤内容匹配
          if (item.steps) {
            item.steps.forEach((step: any) => {
              if (step.title.toLowerCase().includes(searchTerm) || 
                  step.description.toLowerCase().includes(searchTerm)) {
                score += 3;
              }
            });
          }

          if (score > 0) {
            results.push({
              item,
              section,
              category,
              matchType,
              score
            });
          }
        });
      });
    });

    // 按分数排序
    results.sort((a, b) => b.score - a.score);
    setSearchResults(results.slice(0, 20)); // 最多显示20个结果
    setShowSearchResults(true);
  };

  // 处理搜索
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    performSearch(query);
  };

  // 改进的搜索功能，添加搜索历史
  const performSearchWithHistory = async (query: string) => {
    performSearch(query);
    
    if (query.trim()) {
      try {
        await helpDataService.addSearchHistory(query.trim());
        const updatedHistory = await helpDataService.getSearchHistory();
        setSearchHistory(updatedHistory);
      } catch (error) {
        console.error('保存搜索历史失败:', error);
      }
    }
  };

  // 清除搜索
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowSearchResults(false);
  };

  // 改进的导航函数，添加最近浏览记录
  const navigateToItem = async (categoryId: string, sectionId: string, itemId: string) => {
    setNavigation({
      currentCategory: categoryId,
      currentSection: sectionId,
      currentItem: itemId
    });
    
    // 添加到最近浏览记录 - 支持概览页面导航
    const category = helpCategories.find(c => c.id === categoryId);
    const section = category?.sections.find((s: any) => s.id === sectionId);
    const item = section?.children.find((i: any) => i.id === itemId);
    
    // 确定要显示的标题
    let title = '';
    let categoryTitle = '';
    if (item) {
      title = item.title;
      categoryTitle = category?.title || '';
    } else if (section && itemId === '') {
      title = section.title; // 章节概览
      categoryTitle = category?.title || '';
    } else if (category && sectionId === '' && itemId === '') {
      title = category.title; // 分类概览
      categoryTitle = category.title;
    }
    
    if (title) {
      try {
        await helpDataService.addRecentViewed(title, categoryTitle);
        const updatedRecent = await helpDataService.getRecentViewed();
        // 转换数据格式
        const formattedRecent = updatedRecent.map(item => ({
          categoryId,
          sectionId,
          itemId,
          title: item.title,
          timestamp: new Date(item.viewedAt).getTime()
        }));
        setRecentViewed(formattedRecent);
      } catch (error) {
        console.error('保存最近查看失败:', error);
      }
    }
    
    clearSearch();
    setIsMobileNavOpen(false); // 在移动设备上关闭导航
  };

  // 获取当前显示的内容
  const currentContent = useMemo(() => {
    if (showSearchResults) {
      return searchResults;
    }

    const category = helpCategories.find(c => c.id === navigation.currentCategory);
    const section = category?.sections.find((s: any) => s.id === navigation.currentSection);
    const item = section?.children.find((i: any) => i.id === navigation.currentItem);

    return { category, section, item };
  }, [navigation, searchResults, showSearchResults]);

  // 滚动到顶部
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <PageContainer>
      <PageHeader
        title="帮助中心"
        subtitle="了解如何使用沙盘管理软件的各项功能"
      />

      <div className="help-container">
        {/* 搜索栏 */}
        <div className="help-search-bar">
          <div className="help-search-input">
            <Search size={20} className="search-icon" />
            <input
              type="text"
              placeholder="搜索帮助内容..."
              value={searchQuery}
              onChange={handleSearch}
              className="form-input"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="search-clear"
                aria-label="清除搜索"
              >
                ×
              </button>
            )}
          </div>
          
          {/* 搜索建议和热门搜索 */}
          {!showSearchResults && searchQuery === '' && (
            <div className="search-suggestions">
              {/* 最近浏览 */}
              {recentViewed.length > 0 && (
                <div className="recent-viewed">
                  <span className="suggestions-label">最近浏览：</span>
                  <div className="recent-items">
                    {recentViewed.slice(0, 3).map((recent, index) => (
                      <button
                        key={index}
                        className="recent-item"
                        onClick={() => navigateToItem(recent.categoryId, recent.sectionId, recent.itemId)}
                      >
                        {recent.title}
                      </button>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 搜索历史 */}
              {searchHistory.length > 0 && (
                <div className="search-history">
                  <span className="suggestions-label">搜索历史：</span>
                  <div className="history-items">
                    {searchHistory.slice(0, 3).map((term, index) => (
                      <button
                        key={index}
                        className="history-item"
                        onClick={() => {
                          setSearchQuery(term);
                          performSearch(term);
                        }}
                      >
                        {term}
                      </button>
                    ))}
                  </div>
                </div>
              )}

            </div>
          )}
          
          {showSearchResults && (
            <div className="search-results-summary">
              找到 {searchResults.length} 个相关结果
            </div>
          )}
        </div>

        <div className="help-layout">
          {/* 左侧导航 - 在移动设备上可折叠 */}
          <div className={`help-sidebar ${isMobileNavOpen ? 'mobile-open' : ''}`}>
            <div className="help-sidebar-header">
              <h3 className="sidebar-title">
                {showSearchResults ? `搜索结果 (${searchResults.length})` : '导航目录'}
              </h3>
              <button 
                className="mobile-nav-toggle"
                onClick={() => setIsMobileNavOpen(!isMobileNavOpen)}
                aria-label={isMobileNavOpen ? "关闭导航" : "打开导航"}
              >
                <X size={20} />
              </button>
            </div>

            {/* 搜索结果在导航区域显示，而不是覆盖主内容 */}
            {showSearchResults ? (
              <div className="help-search-navigation">
                <div className="search-results-actions">
                  <button onClick={clearSearch} className="btn btn-secondary btn-sm">
                    返回导航
                  </button>
                </div>
                <div className="search-results-nav-list">
                  {searchResults.map((result, index) => (
                    <div 
                      key={`${result.section.id}-${result.item.id}-${index}`}
                      className="search-result-nav-item"
                      onClick={() => navigateToItem(
                        result.category.id,
                        result.section.id,
                        result.item.id
                      )}
                    >
                      <div className="result-nav-header">
                        <h5 className="result-nav-title">{result.item.title}</h5>
                        <span className={`result-match-badge ${result.matchType}`}>
                          {result.matchType === 'title' ? '标题' : 
                           result.matchType === 'tags' ? '标签' : '内容'}
                        </span>
                      </div>
                      <div className="result-nav-breadcrumb">
                        {result.category.title} &gt; {result.section.title}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <HelpNavigation
                categories={helpCategories}
                currentNavigation={navigation}
                onNavigate={navigateToItem}
                searchQuery={searchQuery}
              />
            )}
          </div>

          {/* 右侧内容 */}
          <div className="help-content">
            {showSearchResults ? (
              // 搜索时，右侧显示选中的内容或提示
              <div className="help-search-content-area">
                {/* 获取当前显示的内容，而不是空白 */}
                <HelpContent
                  content={currentContent}
                  navigation={navigation}
                  onNavigate={navigateToItem}
                  contactInfo={contactInfo}
                />
              </div>
            ) : (
              <HelpContent
                content={currentContent}
                navigation={navigation}
                onNavigate={navigateToItem}
                contactInfo={contactInfo}
              />
            )}
          </div>
        </div>
      </div>

      {/* 移动端导航开关按钮 */}
      <button
        className="mobile-nav-open-btn"
        onClick={() => setIsMobileNavOpen(true)}
        aria-label="打开导航"
      >
        <Search size={20} />
      </button>

      {/* 返回顶部按钮 */}
      {/* <button
        onClick={scrollToTop}
        className="help-back-to-top"
        aria-label="返回顶部"
      >
        <ArrowUp size={20} />
      </button> */}

      {/* 快速开始引导按钮 */}
      {/* <button
        onClick={() => navigateToItem('quick-start', 'getting-started', 'welcome')}
        className="help-quick-start"
        aria-label="快速开始"
        title="新手快速入门指南"
      >
        <span className="quick-start-text">快速开始</span>
      </button> */}
    </PageContainer>
  );
};

export default Help;
