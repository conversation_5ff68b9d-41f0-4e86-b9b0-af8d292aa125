/* FilterBar 组件专用样式 - 确保完美对齐 */
.filter-bar {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-bar-search {
  flex: 1;
  min-width: 300px;
}

.filter-bar-search .form-group {
  margin-bottom: 0 !important;
}

.filter-bar-search-input {
  height: 44px; /* 固定高度确保对齐 */
}

.filter-bar-controls {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  align-items: flex-end;
}

.filter-bar-controls .form-group {
  margin-bottom: 0 !important;
}

.filter-bar-select {
  height: 44px; /* 与搜索框相同高度 */
  min-width: 140px;
}

/* 确保所有表单元素高度一致 */
.filter-bar .form-input,
.filter-bar .form-select {
  height: 44px !important;
  padding: 0 var(--spacing-md) !important;
  box-sizing: border-box;
  line-height: normal;
}

.filter-bar .form-input {
  padding-left: 44px !important; /* 为图标留出空间 */
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .filter-bar-search {
    min-width: auto;
  }
  
  .filter-bar-controls {
    justify-content: space-between;
  }
  
  .filter-bar-controls .form-group {
    flex: 1;
    min-width: auto;
  }
  
  .filter-bar-select {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .filter-bar-controls {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .filter-bar-controls .form-group {
    width: 100%;
  }
}
