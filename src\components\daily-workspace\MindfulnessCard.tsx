import React, { useState, useEffect } from 'react';
import { <PERSON>, Play, Pause, RotateCcw, Clock } from 'lucide-react';

interface MindfulnessSession {
  type: 'breathing' | 'meditation' | 'gratitude';
  duration: number;
  completedAt: string;
}

export const MindfulnessCard: React.FC = () => {
  const [activeSession, setActiveSession] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [breathingPhase, setBreathingPhase] = useState<'inhale' | 'hold' | 'exhale'>('inhale');
  const [todaySessions, setTodaySessions] = useState<MindfulnessSession[]>([]);

  const mindfulnessOptions = [
    {
      id: 'breathing',
      title: '深呼吸',
      description: '4-7-8呼吸法，缓解压力',
      duration: 120, // 2分钟
      color: '#3b82f6'
    },
    {
      id: 'meditation',
      title: '冥想片刻',
      description: '专注当下，放松身心',
      duration: 300, // 5分钟
      color: '#8b5cf6'
    },
    {
      id: 'gratitude',
      title: '感恩时刻',
      description: '回想今日美好瞬间',
      duration: 180, // 3分钟
      color: '#10b981'
    }
  ];

  // 加载今日会话记录
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    const savedSessions = localStorage.getItem(`mindfulness_${today}`);
    if (savedSessions) {
      setTodaySessions(JSON.parse(savedSessions));
    }
  }, []);

  // 计时器逻辑
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            handleSessionComplete();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isRunning, timeLeft]);

  // 呼吸引导逻辑
  useEffect(() => {
    if (activeSession === 'breathing' && isRunning) {
      const breathingInterval = setInterval(() => {
        setBreathingPhase(prev => {
          switch (prev) {
            case 'inhale': return 'hold';
            case 'hold': return 'exhale';
            case 'exhale': return 'inhale';
            default: return 'inhale';
          }
        });
      }, 4000); // 每4秒切换一次

      return () => clearInterval(breathingInterval);
    }
  }, [activeSession, isRunning]);

  const startSession = (sessionId: string) => {
    const session = mindfulnessOptions.find(opt => opt.id === sessionId);
    if (session) {
      setActiveSession(sessionId);
      setTimeLeft(session.duration);
      setIsRunning(true);
      setBreathingPhase('inhale');
    }
  };

  const pauseSession = () => {
    setIsRunning(!isRunning);
  };

  const stopSession = () => {
    setActiveSession(null);
    setIsRunning(false);
    setTimeLeft(0);
  };

  const handleSessionComplete = () => {
    if (activeSession) {
      const session: MindfulnessSession = {
        type: activeSession as MindfulnessSession['type'],
        duration: mindfulnessOptions.find(opt => opt.id === activeSession)?.duration || 0,
        completedAt: new Date().toISOString()
      };

      const today = new Date().toISOString().split('T')[0];
      const updatedSessions = [...todaySessions, session];
      setTodaySessions(updatedSessions);
      localStorage.setItem(`mindfulness_${today}`, JSON.stringify(updatedSessions));
    }
    
    setActiveSession(null);
    setIsRunning(false);
    setTimeLeft(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getBreathingInstruction = () => {
    switch (breathingPhase) {
      case 'inhale': return '慢慢吸气...';
      case 'hold': return '屏住呼吸...';
      case 'exhale': return '缓缓呼气...';
    }
  };

  const currentSession = mindfulnessOptions.find(opt => opt.id === activeSession);

  return (
    <div className="workspace-card mindfulness-card">
      <div className="workspace-card-header">
        <div className="workspace-card-icon mindfulness-icon">
          <Brain size={20} />
        </div>
        <h3 className="workspace-card-title">正念时刻</h3>
        {todaySessions.length > 0 && (
          <div className="session-count">
            今日已完成 {todaySessions.length} 次
          </div>
        )}
      </div>
      
      <div className="workspace-card-content">
        {activeSession ? (
          <div className="active-session">
            <div className="session-header">
              <h4 style={{ color: currentSession?.color }}>
                {currentSession?.title}
              </h4>
              <div className="session-timer">
                <Clock size={16} />
                <span>{formatTime(timeLeft)}</span>
              </div>
            </div>
            
            {activeSession === 'breathing' && (
              <div className="breathing-guide">
                <div 
                  className={`breathing-circle ${breathingPhase}`}
                  style={{ borderColor: currentSession?.color }}
                >
                  <span className="breathing-text">
                    {getBreathingInstruction()}
                  </span>
                </div>
              </div>
            )}
            
            {activeSession === 'meditation' && (
              <div className="meditation-guide">
                <div className="meditation-text">
                  <p>专注于你的呼吸</p>
                  <p>让思绪自然流淌</p>
                  <p>感受当下的宁静</p>
                </div>
              </div>
            )}
            
            {activeSession === 'gratitude' && (
              <div className="gratitude-guide">
                <div className="gratitude-text">
                  <p>想想今天让你感恩的事情</p>
                  <p>可能是一个微笑</p>
                  <p>一杯温暖的茶</p>
                  <p>或是内心的平静</p>
                </div>
              </div>
            )}
            
            <div className="session-controls">
              <button className="control-btn" onClick={pauseSession}>
                {isRunning ? <Pause size={16} /> : <Play size={16} />}
                {isRunning ? '暂停' : '继续'}
              </button>
              <button className="control-btn stop-btn" onClick={stopSession}>
                <RotateCcw size={16} />
                结束
              </button>
            </div>
          </div>
        ) : (
          <div className="session-options">
            <p className="mindfulness-prompt">选择一个正念练习开始吧</p>
            <div className="mindfulness-options">
              {mindfulnessOptions.map((option) => (
                <button
                  key={option.id}
                  className="mindfulness-option"
                  onClick={() => startSession(option.id)}
                  style={{ borderColor: option.color }}
                >
                  <div className="option-header">
                    <h4 style={{ color: option.color }}>{option.title}</h4>
                    <span className="option-duration">
                      {Math.floor(option.duration / 60)}分钟
                    </span>
                  </div>
                  <p className="option-description">{option.description}</p>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};