/* 数据统计页面样式 - 与主体风格保持一致 */

/* 页面头部操作区域 */

.filter-group {
  display: flex;
  gap: 16px; /* 统一间隙，给按钮更多空间 */
  align-items: center;
  height: 40px; /* 统一容器高度 */
}

.date-range-select,
.report-type-select {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.15s ease;
  font-weight: 500;
  height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  line-height: 1;
  min-width: 120px; /* 确保选择器有足够宽度 */
}

/* 确保按钮与选择器完美对齐 */
.filter-group .btn,
.filter-group button {
  height: 40px !important;
  box-sizing: border-box !important;
  padding: 8px 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  font-size: 14px !important;
  gap: 8px !important;
  margin: 0 !important;
}

.date-range-select:hover,
.report-type-select:hover {
  border-color: #d1d5db;
}

.date-range-select:focus,
.report-type-select:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.generate-report-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  height: 40px;
  box-sizing: border-box;
  line-height: 1;
  vertical-align: top;
}

.generate-report-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
}

.generate-report-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 统计卡片 - 与主体风格保持一致 */
.statistics-stats-overview {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
  flex-wrap: nowrap;
}

.statistics-stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  border: 1px solid #e5e7eb;
  transition: all 0.15s ease;
  flex: 1;
  min-width: 0;
}

.statistics-stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
}

.statistics-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.statistics-stat-content {
  flex: 1;
}

.statistics-stat-content h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.statistics-stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.statistics-stat-detail {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-top: 4px;
}

/* 卡片特定样式 - 使用简单的单色背景 */
.statistics-stat-card:nth-child(1) .statistics-stat-icon {
  background-color: #4F9CF9;
}

.statistics-stat-card:nth-child(2) .statistics-stat-icon {
  background-color: #10B981;
}

.statistics-stat-card:nth-child(3) .statistics-stat-icon {
  background-color: #F59E0B;
}

.statistics-stat-card:nth-child(4) .statistics-stat-icon {
  background-color: #8B5CF6;
}

/* 图表区域 - 简洁的设计风格 */
.statistics-charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 32px;
}

.statistics-chart-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  transition: all 0.15s ease;
}

.statistics-chart-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
}

.statistics-chart-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.statistics-chart-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.statistics-chart-icon {
  width: 24px;
  height: 24px;
  background-color: #3B82F6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.chart-container {
  height: 300px;
  position: relative;
}

/* 表格区域 - 简洁的数据展示 */
.statistics-data-table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  transition: all 0.15s ease;
}

.statistics-data-table:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
}

.table-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.table-icon {
  width: 24px;
  height: 24px;
  background-color: #F59E0B;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.data-table th {
  background-color: #f9fafb;
  color: #374151;
  font-weight: 600;
  padding: 16px 20px;
  text-align: left;
  font-size: 14px;
  border-bottom: 1px solid #e5e7eb;
}

.data-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.data-table tbody tr:hover {
  background-color: #f9fafb;
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}

/* 加载状态 - 简洁的加载动画 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-left: 3px solid #3B82F6;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
  margin-bottom: 8px;
}

.loading-subtitle {
  font-size: 14px;
  color: #9ca3af;
  text-align: center;
  max-width: 320px;
  line-height: 1.5;
}

/* 响应式设计 - 保持简洁的美感 */
@media (max-width: 1024px) {
  .statistics-charts-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .statistics-stats-overview {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .statistics-container {
    padding: 16px;
  }
  
  .statistics-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-group {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .filter-group .date-range-select,
  .filter-group .report-type-select {
    flex: 1;
    min-width: 140px;
  }
  
  .filter-group button {
    width: 100%;
    margin-top: 8px;
  }
  
  .statistics-stats-overview {
    flex-direction: column;
    gap: 16px;
  }
  
  .statistics-stat-card {
    padding: 16px;
  }
  
  .statistics-chart-card {
    padding: 20px;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .data-table th,
  .data-table td {
    padding: 12px 16px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .header-left h1 {
    font-size: 18px;
  }
  
  .statistics-stat-value {
    font-size: 24px;
  }
  
  .statistics-chart-title h3 {
    font-size: 16px;
  }
  
  .chart-container {
    height: 220px;
  }
}
