// 生成测试来访者数据的脚本
// 使用方法: node generate-test-data.js

const { createTestVisitors } = require('./dist/utils/generateTestVisitors.js');

async function main() {
  try {
    console.log('开始生成100个测试来访者数据...');
    
    const result = await createTestVisitors(100);
    
    console.log('\n=== 生成结果 ===');
    console.log(`成功创建: ${result.success} 个来访者`);
    console.log(`创建失败: ${result.failed} 个来访者`);
    
    if (result.failed > 0) {
      console.log('\n失败原因:');
      result.details.failed.forEach((item, index) => {
        console.log(`${index + 1}. ${item.data.name || '未知'}: ${item.error}`);
      });
    }
    
    console.log('\n测试数据生成完成！');
    console.log('现在可以在来访者管理页面查看分页效果。');
    
  } catch (error) {
    console.error('生成测试数据时发生错误:', error);
    process.exit(1);
  }
}

// 检查是否直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { main };