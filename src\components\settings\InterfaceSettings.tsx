import React from 'react';
import { Card } from '../ui/Card';
import { Input, Select } from '../ui/Form';
import type { AppSettings } from '../../types/settings';

interface InterfaceSettingsProps {
  settings: AppSettings;
  updateSettings: (path: string, value: any) => void;
  saving: boolean;
}

const InterfaceSettings: React.FC<InterfaceSettingsProps> = ({ settings, updateSettings }) => {
  const { interface: interfaceSettings } = settings;

  return (
    <div className="settings-form">
      {/* 主题设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">主题设置</h3>
          <p className="card-subtitle">个性化界面外观</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Select
              label="主题模式"
              value={interfaceSettings.theme.mode}
              onChange={(e) => updateSettings('interface.theme.mode', e.target.value)}
              options={[
                { value: 'light', label: '浅色模式' },
                { value: 'dark', label: '深色模式' },
                { value: 'auto', label: '跟随系统' }
              ]}
            />
            
            <Input
              label="主题色"
              type="color"
              value={interfaceSettings.theme.primaryColor}
              onChange={(e) => updateSettings('interface.theme.primaryColor', e.target.value)}
            />
            
            <Select
              label="字体大小"
              value={interfaceSettings.theme.fontSize}
              onChange={(e) => updateSettings('interface.theme.fontSize', e.target.value)}
              options={[
                { value: 'small', label: '小号字体' },
                { value: 'medium', label: '中号字体' },
                { value: 'large', label: '大号字体' }
              ]}
            />
          </div>
        </div>
      </Card>

      {/* 布局设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">布局设置</h3>
          <p className="card-subtitle">界面布局和显示偏好</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Select
              label="界面密度"
              value={interfaceSettings.layout.density}
              onChange={(e) => updateSettings('interface.layout.density', e.target.value)}
              options={[
                { value: 'comfortable', label: '舒适' },
                { value: 'compact', label: '紧凑' }
              ]}
            />
            
            <Select
              label="系统语言"
              value={interfaceSettings.language}
              onChange={(e) => updateSettings('interface.language', e.target.value)}
              options={[
                { value: 'zh-CN', label: '简体中文' },
                { value: 'en-US', label: 'English' }
              ]}
            />
          </div>
          
          <div className="mt-lg">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={interfaceSettings.layout.sidebarCollapsed}
                onChange={(e) => updateSettings('interface.layout.sidebarCollapsed', e.target.checked)}
              />
              <span className="checkmark"></span>
              默认折叠侧边栏
            </label>
          </div>
          
          <div className="mt-md">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={interfaceSettings.layout.animation}
                onChange={(e) => updateSettings('interface.layout.animation', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用界面动画效果
            </label>
          </div>
        </div>
      </Card>

      {/* 其他设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">其他设置</h3>
          <p className="card-subtitle">其他界面相关配置</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <div className="form-group">
              <label className="form-label">预览</label>
              <div className="theme-preview">
                <div className="preview-card">
                  <div className="preview-header" style={{ backgroundColor: interfaceSettings.theme.primaryColor }}>
                    <div className="preview-title">预览卡片</div>
                  </div>
                  <div className="preview-content">
                    <p>这是一个样式预览</p>
                    <div className="preview-button" style={{ backgroundColor: interfaceSettings.theme.primaryColor }}>
                      按钮样式
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default InterfaceSettings;
