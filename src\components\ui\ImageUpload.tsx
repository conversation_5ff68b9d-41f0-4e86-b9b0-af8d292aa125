import React, { useCallback, useState } from 'react';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import './ImageUpload.css';

interface ImageUploadProps {
  value?: string; // Base64 图片数据
  onChange: (imageData: string | null) => void;
  placeholder?: string;
  maxSize?: number; // 最大文件大小（MB）
  className?: string;
  disabled?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  placeholder = "点击选择图片或拖拽图片到此处",
  maxSize = 5,
  className = '',
  disabled = false
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);

  // 压缩图片
  const compressImage = useCallback((file: File, maxWidth = 800, quality = 0.8): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // 计算新尺寸
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // 绘制压缩后的图片
        ctx?.drawImage(img, 0, 0, width, height);
        
        // 转换为 Base64
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
        resolve(compressedDataUrl);
      };
      
      img.src = URL.createObjectURL(file);
    });
  }, []);

  // 处理文件上传
  const handleFileUpload = useCallback(async (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    if (file.size > maxSize * 1024 * 1024) {
      alert(`图片大小不能超过 ${maxSize}MB`);
      return;
    }

    setUploading(true);
    
    try {
      // 压缩图片
      const compressedImage = await compressImage(file);
      onChange(compressedImage);
    } catch (error) {
      console.error('图片上传失败:', error);
      alert('图片上传失败，请重试');
    } finally {
      setUploading(false);
    }
  }, [maxSize, compressImage, onChange]);

  // 拖拽处理
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, [disabled, handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  // 点击选择文件
  const handleClick = useCallback(() => {
    if (disabled || uploading) return;
    
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileUpload(file);
      }
    };
    input.click();
  }, [disabled, uploading, handleFileUpload]);

  // 删除图片
  const handleRemove = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(null);
  }, [onChange]);

  return (
    <div className={`image-upload ${className}`}>
      <div 
        className={`upload-area ${dragOver ? 'drag-over' : ''} ${disabled ? 'disabled' : ''} ${value ? 'has-image' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        {value ? (
          <div className="image-preview">
            <img src={value} alt="预览图" className="preview-image" />
            <div className="image-overlay">
              <button
                type="button"
                className="remove-btn"
                onClick={handleRemove}
                title="删除图片"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        ) : (
          <div className="upload-placeholder">
            {uploading ? (
              <div className="upload-loading">
                <div className="loading-spinner"></div>
                <p>上传中...</p>
              </div>
            ) : (
              <>
                <div className="upload-icon">
                  {dragOver ? <ImageIcon size={32} /> : <Upload size={32} />}
                </div>
                <p className="upload-text">{placeholder}</p>
                <p className="upload-hint">支持 JPG、PNG、GIF 格式，最大 {maxSize}MB</p>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageUpload;
