// 沙具管理相关类型定义

export interface SandTool {
  id: string;
  name: string;
  category: SandToolCategory;
  subcategory?: string; // 自定义子类别
  description?: string;
  material: string;
  size: SandToolSize;
  color?: string;
  quantity: number;
  available: number;
  condition: SandToolCondition;
  location: string;
  imageData?: string; // Base64编码的图片数据
  notes?: string;
  tags?: string[];
  usageCount?: number;
  lastUsed?: string;
  isFragile?: boolean;
  needsCare?: boolean;
  replacementNeeded?: boolean;
}

export type SandToolCategory = 
  | '容大天成原创' 
  | '人物类' 
  | '动物类' 
  | '植物类' 
  | '建筑类' 
  | '生活类' 
  | '交通类' 
  | '食物类' 
  | '自然物质类' 
  | '其它类';

export type SandToolSize = '微型' | '小型' | '中型' | '大型' | '超大型';

export type SandToolCondition = '全新' | '良好' | '一般' | '损坏' | '报废';

export interface SandToolUsageRecord {
  id: string;
  toolId: string;
  toolName: string;
  sessionId?: string;
  sessionType: '个案' | '团体';
  caseId?: string; // 个案ID（如果是个案使用）
  groupSessionId?: string; // 团体活动ID（如果是团体使用）
  clientName?: string;
  therapistName: string;
  usageDate: string;
  duration?: number; // 使用时长（分钟）
  notes?: string;
  damageReport?: string;
  returnCondition: SandToolCondition;
}

export interface SandToolInventory {
  id: string;
  toolId: string;
  quantity: number;
  location: string;
  lastChecked: string;
  checkedBy: string;
  notes?: string;
}

export interface SandToolMaintenanceRecord {
  id: string;
  toolId: string;
  maintenanceType: '清洁' | '修复' | '更换' | '检查';
  date: string;
  description: string;
  cost?: number;
  performedBy: string;
  nextScheduled?: string;
}

export interface SandToolCategory_Detail {
  id: SandToolCategory;
  name: string;
  description: string;
  icon: string;
  subcategories?: string[];
  suggestedTags?: string[];
}

export interface SandToolFilters {
  category?: SandToolCategory | 'all';
  condition?: SandToolCondition | 'all';
  availability?: 'available' | 'unavailable' | 'low-stock' | 'all';
  location?: string | 'all';
  size?: SandToolSize | 'all';
  needsMaintenance?: boolean;
}

export interface SandToolStats {
  totalTools: number;
  totalQuantity: number;
  availableQuantity: number;
  lowStockItems: number;
  needsMaintenanceItems: number;
  damagedItems: number;
  categoryCounts: Record<SandToolCategory, number>;
  locationCounts: Record<string, number>;
  conditionCounts: Record<SandToolCondition, number>;
}
