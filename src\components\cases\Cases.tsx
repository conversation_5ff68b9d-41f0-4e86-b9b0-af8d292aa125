import React, { useState, useEffect } from 'react';
import { caseService } from '../../services/caseService';
import type { SimpleCase, CrisisLevel, Progress } from '../../types/case';
import {
  Card,
  CardContent,
  Button,
  FilterBar,
  Pa<PERSON>ation,
  PageContainer,
  PageHeader,
  Badge,
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  LoadingSpinner
} from '../ui';
import { Plus, Search, Download, Eye, Edit, Trash2, Star } from 'lucide-react';
import { CaseModal } from './CaseModal';
import { CaseExportModal } from './CaseExportModal';
import './Cases.css';

interface CaseFilters {
  search: string;
  crisis: string;
  progress: string;
  therapyMethod: string;
  star: string;
}

const Cases: React.FC = () => {
  const [cases, setCases] = useState<SimpleCase[]>([]);
  const [filteredCases, setFilteredCases] = useState<SimpleCase[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCaseModal, setShowCaseModal] = useState(false);
  const [caseModalMode, setCaseModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedCase, setSelectedCase] = useState<SimpleCase | null>(null);
  const [selectedCaseIds, setSelectedCaseIds] = useState<Set<string>>(new Set());
  
  // 导出模态框状态
  const [showExportModal, setShowExportModal] = useState(false);

  // 筛选状态
  const [filters, setFilters] = useState<CaseFilters>({
    search: '',
    crisis: '',
    progress: '',
    therapyMethod: '',
    star: ''
  });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    highRisk: 0,
    improving: 0,
    starred: 0
  });

  useEffect(() => {
    loadCases();
  }, []);

  useEffect(() => {
    filterCases();
  }, [cases, filters]);

  const loadCases = async () => {
    try {
      setLoading(true);
      const data = await caseService.getAllCases();
      setCases(data);
      
      // 计算统计数据
      const newStats = {
        total: data.length,
        highRisk: data.filter(c => c.crisis === '⚡').length,
        improving: data.filter(c => c.progress === '⬆️').length,
        starred: data.filter(c => c.star).length
      };
      setStats(newStats);
    } catch (error) {
      console.error('加载个案列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterCases = () => {
    let filtered = [...cases];

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(c => 
        c.name?.toLowerCase().includes(searchTerm) ||
        c.summary?.toLowerCase().includes(searchTerm)
      );
    }

    if (filters.crisis) {
      filtered = filtered.filter(c => c.crisis === filters.crisis);
    }

    if (filters.progress) {
      filtered = filtered.filter(c => c.progress === filters.progress);
    }

    if (filters.therapyMethod) {
      filtered = filtered.filter(c => c.therapyMethod === filters.therapyMethod);
    }

    if (filters.star) {
      if (filters.star === 'true') {
        filtered = filtered.filter(c => c.star);
      } else if (filters.star === 'false') {
        filtered = filtered.filter(c => !c.star);
      }
    }

    setFilteredCases(filtered);
    setCurrentPage(1);
  };

  const handleCreateCase = () => {
    setSelectedCase(null);
    setCaseModalMode('create');
    setShowCaseModal(true);
  };

  const handleEditCase = (caseItem: SimpleCase) => {
    setSelectedCase(caseItem);
    setCaseModalMode('edit');
    setShowCaseModal(true);
  };

  const handleViewCase = (caseItem: SimpleCase) => {
    setSelectedCase(caseItem);
    setCaseModalMode('view');
    setShowCaseModal(true);
  };

  const handleDeleteCase = async (caseItem: SimpleCase) => {
    if (confirm(`确定要删除个案"${caseItem.name}"吗？此操作不可恢复。`)) {
      try {
        const success = await caseService.deleteCase(caseItem.id);
        if (success) {
          await loadCases();
          alert('删除成功');
        } else {
          alert('删除失败');
        }
      } catch (error) {
        console.error('删除个案失败:', error);
        alert('删除失败，请稍后重试');
      }
    }
  };

  const handleToggleStar = async (caseItem: SimpleCase) => {
    try {
      const success = await caseService.updateCase(caseItem.id, { star: !caseItem.star });
      if (success) {
        await loadCases();
      }
    } catch (error) {
      console.error('更新个案失败:', error);
    }
  };

  const handleSaveCase = async (caseData: Partial<SimpleCase>) => {
    try {
      if (caseModalMode === 'create') {
        const success = await caseService.createCase(caseData as Omit<SimpleCase, 'id'>);
        if (success) {
          await loadCases();
          setShowCaseModal(false);
          alert('个案创建成功');
        } else {
          alert('个案创建失败');
        }
      } else {
        const caseId = (caseData as SimpleCase).id;
        const success = await caseService.updateCase(caseId, caseData);
        if (success) {
          await loadCases();
          setShowCaseModal(false);
          alert('个案更新成功');
        } else {
          alert('个案更新失败');
        }
      }
    } catch (error) {
      console.error('保存个案失败:', error);
      alert('保存失败，请稍后重试');
    }
  };

  const handleBatchDelete = async () => {
    const selected = cases.filter(c => selectedCaseIds.has(c.id));
    const names = selected.map(c => c.name).join('、');
    
    if (confirm(`确定要删除以下 ${selected.length} 个个案吗？\n${names}\n\n此操作不可恢复。`)) {
      try {
        let successCount = 0;
        let failedCount = 0;
        
        for (const caseItem of selected) {
          const success = await caseService.deleteCase(caseItem.id);
          if (success) {
            successCount++;
          } else {
            failedCount++;
          }
        }
        
        setSelectedCaseIds(new Set());
        
        if (failedCount === 0) {
          alert(`成功删除 ${successCount} 个个案`);
        } else {
          alert(`成功删除 ${successCount} 个个案，${failedCount} 个删除失败`);
        }
        
        await loadCases();
      } catch (error) {
        console.error('批量删除失败:', error);
        alert('批量删除失败，请稍后重试');
      }
    }
  };

  const toggleSelectCase = (caseId: string) => {
    const newSelected = new Set(selectedCaseIds);
    if (newSelected.has(caseId)) {
      newSelected.delete(caseId);
    } else {
      newSelected.add(caseId);
    }
    setSelectedCaseIds(newSelected);
  };

  const getCurrentPageCases = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredCases.slice(startIndex, endIndex);
  };

  const getCrisisBadge = (crisis?: CrisisLevel) => {
    switch (crisis) {
      case '✅':
        return <Badge variant="success">安全</Badge>;
      case '⚠️':
        return <Badge variant="warning">需要关注</Badge>;
      case '⚡':
        return <Badge variant="danger">高危</Badge>;
      default:
        return <Badge variant="gray">未知</Badge>;
    }
  };

  const getProgressBadge = (progress?: Progress) => {
    switch (progress) {
      case '⬆️':
        return <Badge variant="success">好转</Badge>;
      case '➡️':
        return <Badge variant="gray">维持</Badge>;
      case '⬇️':
        return <Badge variant="danger">恶化</Badge>;
      default:
        return <Badge variant="gray">未知</Badge>;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const currentPageCases = getCurrentPageCases();
  const totalPages = Math.ceil(filteredCases.length / pageSize);
  
  // 检测是否有筛选条件
  const isFiltered = filters.search !== '' || 
    filters.crisis !== '' || 
    filters.progress !== '' || 
    filters.therapyMethod !== '' || 
    filters.star !== '';

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <PageContainer>
      <PageHeader 
        title="个案管理"
        subtitle="管理和跟踪心理健康个案信息"
        actions={
          <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
            <Button
              variant="secondary"
              leftIcon={<Download size={16} />}
              onClick={() => setShowExportModal(true)}
              className="btn btn-secondary"
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '8px 16px',
                fontSize: '14px',
                fontWeight: '500',
                borderRadius: '6px',
                cursor: 'pointer',
                transition: '0.12s',
                textDecoration: 'none',
                whiteSpace: 'nowrap',
                border: '1px solid rgb(209, 213, 219)',
                lineHeight: '1',
                height: '40px',
                boxSizing: 'border-box',
                background: 'white',
                color: 'rgb(55, 65, 81)'
              }}
            >
              导出个案
            </Button>
            <Button
              leftIcon={<Plus size={16} />}
              onClick={handleCreateCase}
            >
              新建个案
            </Button>
          </div>
        }
      />

      {/* 搜索和筛选区域 */}
      <Card className="mb-xl">
        <CardContent>
          <FilterBar
            searchProps={{
              value: filters.search,
              onChange: (e) => setFilters({ ...filters, search: e.target.value }),
              placeholder: "搜索姓名、摘要或关键词...",
              leftIcon: <Search size={16} />
            }}
            filters={[
              {
                value: filters.crisis,
                onChange: (e) => setFilters({ ...filters, crisis: e.target.value }),
                options: [
                  { value: '', label: '全部危机等级' },
                  { value: '✅', label: '安全' },
                  { value: '⚠️', label: '需要关注' },
                  { value: '⚡', label: '高危' }
                ]
              },
              {
                value: filters.progress,
                onChange: (e) => setFilters({ ...filters, progress: e.target.value }),
                options: [
                  { value: '', label: '全部进展' },
                  { value: '⬆️', label: '好转' },
                  { value: '➡️', label: '维持' },
                  { value: '⬇️', label: '恶化' }
                ]
              },
              {
                value: filters.therapyMethod,
                onChange: (e) => setFilters({ ...filters, therapyMethod: e.target.value }),
                options: [
                  { value: '', label: '全部治疗方法' },
                  { value: '认知行为疗法', label: '认知行为疗法' },
                  { value: '沙盘疗法', label: '沙盘疗法' },
                  { value: '精神分析', label: '精神分析' },
                  { value: '人本主义疗法', label: '人本主义疗法' },
                  { value: '家庭治疗', label: '家庭治疗' },
                  { value: '其他', label: '其他' }
                ]
              },
              {
                value: filters.star,
                onChange: (e) => setFilters({ ...filters, star: e.target.value }),
                options: [
                  { value: '', label: '全部' },
                  { value: 'true', label: '已关注' },
                  { value: 'false', label: '未关注' }
                ]
              }
            ]}
          />
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="stats-row mb-xl">
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.total}</div>
              <div className="stat-label">总个案数</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.highRisk}</div>
              <div className="stat-label">高危个案</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.improving}</div>
              <div className="stat-label">好转个案</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.starred}</div>
              <div className="stat-label">重点关注</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 个案列表 */}
      <Card>
        <CardContent>
          {currentPageCases.length === 0 ? (
            <div className="empty-state">
              <p>暂无个案数据</p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableCell style={{ width: '40px' }}>
                      <input
                        type="checkbox"
                        checked={currentPageCases.length > 0 && currentPageCases.every(c => selectedCaseIds.has(c.id))}
                        onChange={(e) => {
                          if (e.target.checked) {
                            const newSelected = new Set(selectedCaseIds);
                            currentPageCases.forEach(c => newSelected.add(c.id));
                            setSelectedCaseIds(newSelected);
                          } else {
                            const newSelected = new Set(selectedCaseIds);
                            currentPageCases.forEach(c => newSelected.delete(c.id));
                            setSelectedCaseIds(newSelected);
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>姓名</TableCell>
                    <TableCell>危机等级</TableCell>
                    <TableCell>治疗进展</TableCell>
                    <TableCell>治疗方法</TableCell>
                    <TableCell>创建时间</TableCell>
                    <TableCell>更新时间</TableCell>
                    <TableCell>操作</TableCell>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentPageCases.map((caseItem) => (
                    <TableRow key={caseItem.id}>
                      <TableCell>
                        <input
                          type="checkbox"
                          checked={selectedCaseIds.has(caseItem.id)}
                          onChange={() => toggleSelectCase(caseItem.id)}
                        />
                      </TableCell>
                      <TableCell>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <span>{caseItem.name || '-'}</span>
                          {caseItem.star && (
                            <Star size={14} style={{ color: '#f59e0b', fill: '#f59e0b' }} />
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{getCrisisBadge(caseItem.crisis)}</TableCell>
                      <TableCell>{getProgressBadge(caseItem.progress)}</TableCell>
                      <TableCell>{caseItem.therapyMethod || '-'}</TableCell>
                      <TableCell>{formatDate(caseItem.createdAt)}</TableCell>
                      <TableCell>{formatDate(caseItem.updatedAt)}</TableCell>
                      <TableCell>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewCase(caseItem)}
                            title="查看详情"
                          >
                            <Eye size={14} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCase(caseItem)}
                            title="编辑"
                          >
                            <Edit size={14} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStar(caseItem)}
                            title={caseItem.star ? "取消关注" : "添加关注"}
                            style={{ color: caseItem.star ? '#f59e0b' : undefined }}
                          >
                            <Star size={14} fill={caseItem.star ? '#f59e0b' : 'none'} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCase(caseItem)}
                            title="删除"
                            style={{ color: '#ef4444' }}
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页和批量操作 */}
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                marginTop: '16px',
                paddingTop: '16px',
                borderTop: '1px solid #e5e7eb'
              }}>
                <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                  {selectedCaseIds.size > 0 && (
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={handleBatchDelete}
                    >
                      删除选中 ({selectedCaseIds.size})
                    </Button>
                  )}
                  <span style={{ fontSize: '14px', color: '#6b7280' }}>
                    共 {filteredCases.length} 个个案
                  </span>
                </div>
                
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  showTotal={true}
                  total={filteredCases.length}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 个案模态框 */}
      <CaseModal
        isOpen={showCaseModal}
        mode={caseModalMode}
        case={selectedCase}
        onClose={() => setShowCaseModal(false)}
        onSave={handleSaveCase}
      />

      {/* 导出模态框 */}
      <CaseExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        cases={currentPageCases}
        totalCases={cases.length}
        isFiltered={isFiltered}
      />
    </PageContainer>
  );
};

export default Cases;
