import type { SandTool, SandToolCategory_Detail, SandToolUsageRecord, SandToolMaintenanceRecord } from '../types/sandtool';

// 示例图片数据（简单的1像素透明图片，实际使用时会被真实图片替换）
const sampleImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

// 沙具类别详细信息
export const sandToolCategories: SandToolCategory_Detail[] = [
  {
    id: '容大天成原创',
    name: '容大天成原创',
    description: '容大天成公司自主研发的专业沙盘治疗沙具',
    icon: '✨',
    subcategories: ['原创人物', '原创场景', '原创道具', '原创套装'],
    suggestedTags: ['专业', '原创', '治疗', '创新']
  },
  {
    id: '人物类',
    name: '人物类',
    description: '各种人物形象，包括不同年龄、职业、文化背景的人物',
    icon: '👥',
    subcategories: ['成人男性', '成人女性', '儿童', '老人', '职业人物', '文化人物'],
    suggestedTags: ['家庭', '职场', '成长', '关系']
  },
  {
    id: '动物类',
    name: '动物类',
    description: '各种动物模型，包括野生动物、家畜、海洋生物等',
    icon: '🐾',
    subcategories: ['哺乳动物', '鸟类', '水生动物', '昆虫', '家畜', '野生动物'],
    suggestedTags: ['本能', '自然', '力量', '温顺']
  },
  {
    id: '植物类',
    name: '植物类',
    description: '各种植物模型，包括花卉、树木、蔬菜水果等',
    icon: '🌿',
    subcategories: ['花卉', '树木', '果蔬', '盆栽', '草本', '灌木'],
    suggestedTags: ['生长', '生命', '美丽', '季节']
  },
  {
    id: '建筑类',
    name: '建筑类',
    description: '各种建筑物模型，包括住宅、公共建筑、桥梁等',
    icon: '🏠',
    subcategories: ['住宅', '学校', '医院', '商店', '桥梁', '塔楼'],
    suggestedTags: ['安全', '归属', '成长', '社会']
  },
  {
    id: '生活类',
    name: '生活类',
    description: '日常生活用品，包括家具、器具、装饰品等',
    icon: '🏮',
    subcategories: ['家具', '厨具', '装饰品', '工具', '电器', '文具'],
    suggestedTags: ['日常', '舒适', '功能', '个人']
  },
  {
    id: '交通类',
    name: '交通类',
    description: '各种交通工具模型，包括汽车、船只、飞机等',
    icon: '🚗',
    subcategories: ['汽车', '火车', '飞机', '船只', '自行车', '摩托车'],
    suggestedTags: ['移动', '旅行', '速度', '自由']
  },
  {
    id: '食物类',
    name: '食物类',
    description: '各种食物模型，包括水果、蔬菜、主食、零食等',
    icon: '🍎',
    subcategories: ['水果', '蔬菜', '主食', '零食', '饮品', '调料'],
    suggestedTags: ['营养', '美味', '分享', '满足']
  },
  {
    id: '自然物质类',
    name: '自然物质类',
    description: '自然界的各种物质，包括石头、水、沙土等',
    icon: '🌍',
    subcategories: ['石头', '水体', '沙土', '矿物', '化石', '贝壳'],
    suggestedTags: ['自然', '原始', '坚固', '时间']
  },
  {
    id: '其它类',
    name: '其它类',
    description: '其他无法归类的物品和抽象概念',
    icon: '📦',
    subcategories: ['抽象物', '符号', '装饰', '配件', '特殊', '未分类'],
    suggestedTags: ['特殊', '独特', '神秘', '其他']
  }
];

// 模拟沙具数据
export const mockSandTools: SandTool[] = [
  {
    id: '1',
    name: '治疗师沙盘',
    category: '容大天成原创',
    subcategory: '原创套装',
    description: '专业治疗师专用沙盘套装，包含多种治疗工具',
    material: '优质木材',
    size: '大型',
    color: '原木色',
    quantity: 5,
    available: 5,
    condition: '全新',
    location: 'A区-1号柜',
    imageData: sampleImageData,
    notes: '专业治疗用具，需谨慎使用',
    tags: ['专业', '治疗', '套装'],
    usageCount: 0,
    lastUsed: undefined,
    isFragile: false,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '2',
    name: '小女孩',
    category: '人物类',
    subcategory: '儿童',
    description: '可爱的小女孩模型，适合家庭主题沙盘',
    material: '树脂',
    size: '小型',
    color: '彩色',
    quantity: 10,
    available: 8,
    condition: '良好',
    location: 'B区-2号柜',
    imageData: sampleImageData,
    notes: '常用于家庭关系主题',
    tags: ['儿童', '家庭', '女性'],
    usageCount: 15,
    lastUsed: '2024-08-25',
    isFragile: true,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '3',
    name: '小猫咪',
    category: '动物类',
    subcategory: '家畜',
    description: '温顺的小猫模型，代表陪伴和温暖',
    material: '塑料',
    size: '微型',
    color: '橙白色',
    quantity: 15,
    available: 12,
    condition: '良好',
    location: 'C区-1号柜',
    imageData: sampleImageData,
    notes: '儿童喜爱的沙具',
    tags: ['动物', '温顺', '陪伴'],
    usageCount: 28,
    lastUsed: '2024-08-28',
    isFragile: false,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '4',
    name: '玫瑰花',
    category: '植物类',
    subcategory: '花卉',
    description: '美丽的玫瑰花模型，象征爱情和美好',
    material: '绢布',
    size: '小型',
    color: '红色',
    quantity: 20,
    available: 18,
    condition: '良好',
    location: 'D区-1号柜',
    imageData: sampleImageData,
    notes: '适合情感表达主题',
    tags: ['花卉', '爱情', '美好'],
    usageCount: 12,
    lastUsed: '2024-08-20',
    isFragile: true,
    needsCare: true,
    replacementNeeded: false
  },
  {
    id: '5',
    name: '小房子',
    category: '建筑类',
    subcategory: '住宅',
    description: '温馨的小房子模型，代表家庭和安全感',
    material: '木材',
    size: '中型',
    color: '彩色',
    quantity: 8,
    available: 6,
    condition: '良好',
    location: 'E区-1号柜',
    imageData: sampleImageData,
    notes: '家庭主题常用沙具',
    tags: ['建筑', '家庭', '安全'],
    usageCount: 22,
    lastUsed: '2024-08-26',
    isFragile: false,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '6',
    name: '餐具套装',
    category: '生活类',
    subcategory: '厨具',
    description: '迷你餐具套装，包含盘子、杯子、勺子等',
    material: '塑料',
    size: '微型',
    color: '彩色',
    quantity: 25,
    available: 20,
    condition: '良好',
    location: 'F区-1号柜',
    imageData: sampleImageData,
    notes: '生活场景必备道具',
    tags: ['生活', '餐具', '日常'],
    usageCount: 18,
    lastUsed: '2024-08-24',
    isFragile: true,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '7',
    name: '小汽车',
    category: '交通类',
    subcategory: '汽车',
    description: '精致的小汽车模型，代表自由和移动',
    material: '金属',
    size: '小型',
    color: '红色',
    quantity: 12,
    available: 10,
    condition: '良好',
    location: 'G区-1号柜',
    imageData: sampleImageData,
    notes: '男孩子特别喜欢的沙具',
    tags: ['交通', '汽车', '自由'],
    usageCount: 35,
    lastUsed: '2024-08-29',
    isFragile: false,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '8',
    name: '苹果',
    category: '食物类',
    subcategory: '水果',
    description: '新鲜的苹果模型，象征健康和营养',
    material: '泡沫',
    size: '微型',
    color: '红色',
    quantity: 30,
    available: 25,
    condition: '良好',
    location: 'H区-1号柜',
    imageData: sampleImageData,
    notes: '健康主题常用道具',
    tags: ['食物', '水果', '健康'],
    usageCount: 8,
    lastUsed: '2024-08-15',
    isFragile: false,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '9',
    name: '小石头',
    category: '自然物质类',
    subcategory: '石头',
    description: '天然小石头，代表坚固和永恒',
    material: '天然石材',
    size: '微型',
    color: '灰色',
    quantity: 50,
    available: 45,
    condition: '良好',
    location: 'I区-1号柜',
    imageData: sampleImageData,
    notes: '自然主题基础道具',
    tags: ['自然', '石头', '坚固'],
    usageCount: 42,
    lastUsed: '2024-08-27',
    isFragile: false,
    needsCare: false,
    replacementNeeded: false
  },
  {
    id: '10',
    name: '神秘宝盒',
    category: '其它类',
    subcategory: '特殊',
    description: '神秘的小宝盒，激发想象力',
    material: '木材',
    size: '小型',
    color: '棕色',
    quantity: 3,
    available: 2,
    condition: '一般',
    location: 'J区-1号柜',
    imageData: sampleImageData,
    notes: '特殊主题使用',
    tags: ['神秘', '宝盒', '想象'],
    usageCount: 5,
    lastUsed: '2024-08-10',
    isFragile: false,
    needsCare: true,
    replacementNeeded: false
  }
];

// 使用记录
export const mockUsageRecords: SandToolUsageRecord[] = [
  {
    id: '1',
    toolId: '2',
    toolName: '小女孩',
    sessionType: '个案',
    clientName: '张**',
    therapistName: '李老师',
    usageDate: '2024-08-25',
    duration: 60,
    notes: '用于家庭关系探索',
    returnCondition: '良好'
  },
  {
    id: '2',
    toolId: '3',
    toolName: '小猫咪',
    sessionType: '团体',
    clientName: '团体A',
    therapistName: '王老师',
    usageDate: '2024-08-28',
    duration: 90,
    notes: '团体互动活动',
    returnCondition: '良好'
  }
];

// 维护记录
export const mockMaintenanceRecords: SandToolMaintenanceRecord[] = [
  {
    id: '1',
    toolId: '4',
    maintenanceType: '清洁',
    date: '2024-08-20',
    description: '深度清洁处理',
    performedBy: '张师傅',
    cost: 0,
    nextScheduled: '2024-09-20'
  }
];

// 获取统计数据的辅助函数
export const getStatsData = () => {
  const totalTools = mockSandTools.reduce((sum, tool) => sum + tool.quantity, 0);
  const availableTools = mockSandTools.reduce((sum, tool) => sum + tool.available, 0);
  const lowStockTools = mockSandTools.filter(tool => tool.available <= tool.quantity * 0.2).length;
  const needsMaintenanceTools = mockSandTools.filter(tool => tool.needsCare || tool.replacementNeeded).length;

  return {
    totalTools,
    availableTools,
    lowStockTools,
    needsMaintenanceTools,
    categoriesCount: sandToolCategories.length,
    usageCount: mockUsageRecords.length
  };
};

// 根据筛选条件过滤沙具
export const filterTools = (tools: SandTool[], filters: any) => {
  return tools.filter(tool => {
    if (filters.search && !tool.name.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    if (filters.category && filters.category !== 'all' && tool.category !== filters.category) {
      return false;
    }
    if (filters.condition && filters.condition !== 'all' && tool.condition !== filters.condition) {
      return false;
    }
    if (filters.availability) {
      if (filters.availability === 'available' && tool.available === 0) return false;
      if (filters.availability === 'unavailable' && tool.available > 0) return false;
      if (filters.availability === 'low-stock' && tool.available > tool.quantity * 0.2) return false;
    }
    if (filters.needsMaintenance && !tool.needsCare && !tool.replacementNeeded) {
      return false;
    }
    return true;
  });
};
