/* Header组件样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  gap: 16px;
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.menu-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.15s ease;
  margin-left: -14px; /* 调整左边距，使按钮更靠左 */
  width: 36px; /* 固定宽度确保对齐 */
  height: 36px; /* 固定高度确保对齐 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-toggle:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.brand-info {
  display: flex;
  align-items: center;
  gap: 12px; /* 增大与徽章间距，更显呼吸感 */
}

/* 圆形徽章容器，承载 logo，增强品牌识别 */
.logo-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #FCD34D, #F59E0B);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  flex-shrink: 0;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.brand-info:hover .logo-badge {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 让徽章内的图片更协调 */
.logo-badge .logo-image {
  height: 22px;
  width: auto;
  object-fit: contain;
}

.brand-text {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.app-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  letter-spacing: 0.2px;
}

.app-subtitle {
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  line-height: 1;
  margin-top: 2px;
}

.header .logo-image {
  height: 36px; /* 根据需求调整大小 */
  width: auto; /* 保持宽高比例 */
  object-fit: contain;
}

/* 中间搜索区域 */
.header-center {
  flex: 1;
  min-width: 200px;
  max-width: 600px;
  margin: 0 24px;
  display: flex;
  align-items: center;
}

.search-form {
  width: 100%;
  position: relative;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 36px 0 36px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  font-size: 14px;
  background-color: #f9fafb;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.search-input:focus {
  outline: none;
  border-color: #4f9cf9;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(79, 156, 249, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
  z-index: 1;
}

.search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #9ca3af;
  padding: 4px;
  border-radius: 4px;
  z-index: 1;
}

.search-clear:hover {
  color: #6b7280;
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 通知区域 */
.notification-container {
  position: relative;
}

.notification-btn {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.notification-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ef4444;
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  margin-top: 8px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.notification-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.notification-count {
  font-size: 12px;
  color: #6b7280;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.notification-list::-webkit-scrollbar {
  display: none;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background-color: #f9fafb;
}

.notification-item.unread {
  background-color: #eff6ff;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.notification-content p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #9ca3af;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  margin-top: 6px;
}

.notification-footer {
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
}

.view-all-btn {
  width: 100%;
  background: none;
  border: none;
  color: #4f9cf9;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.view-all-btn:hover {
  background-color: #f3f4f6;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.user-info:hover {
  background-color: #f3f4f6;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #4f9cf9, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-center {
    max-width: 400px;
    margin: 0 20px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .header-center {
    margin: 0 12px;
    max-width: 300px;
  }
  
  .search-input {
    font-size: 13px;
    padding: 0 32px 0 32px;
  }
  
  .search-icon {
    left: 10px;
  }
  
  .search-clear {
    right: 10px;
  }
  
  .menu-toggle {
    margin-left: 0;
  }
  
  .app-title {
    font-size: 16px;
  }
  
  .app-subtitle {
    display: none;
  }
  
  .user-name {
    display: none;
  }
  
  .notification-dropdown {
    width: 280px;
    right: -12px;
  }
  
  .header-right {
    gap: 12px;
  }
}

@media (max-width: 640px) {
  .header {
    padding: 0 8px;
  }
  
  .header-center {
    margin: 0 8px;
    max-width: 200px;
  }
  
  .brand-info {
    display: none;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .header-right {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .header-center {
    max-width: 150px;
  }
  
  .search-input {
    font-size: 12px;
    padding: 0 28px 0 28px;
  }
  
  .search-input::placeholder {
    font-size: 12px;
  }
  
  .search-icon {
    left: 8px;
    width: 14px;
    height: 14px;
  }
  
  .search-clear {
    right: 8px;
    width: 14px;
    height: 14px;
  }
}
