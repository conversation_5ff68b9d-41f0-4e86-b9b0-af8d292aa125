import type { SimpleCase } from '../types/case';

export const mockCases: SimpleCase[] = [
  {
    id: '1',
    visitorId: '1',
    name: '张小明',
    summary: '学习焦虑，考试紧张，需要情绪调节训练',
    therapyMethod: '认知行为疗法',
    selectedSandTools: [],
    lastDate: '2024-01-15',
    nextDate: '2024-01-22',
    total: 5,
    star: true,
    duration: 50,
    crisis: '✅',
    homework: '✅',
    progress: '⬆️',
    keywords: ['焦虑', '学习压力', '认知调节'],
    supervision: '建议继续认知重构训练',
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    visitorId: '2',
    name: '李美丽',
    summary: '人际关系困扰，职场沟通问题',
    therapyMethod: '人本主义疗法',
    selectedSandTools: [],
    lastDate: '2024-01-10',
    nextDate: '2024-01-17',
    total: 3,
    star: true,
    duration: 50,
    crisis: '⚠️',
    homework: '📋',
    progress: '➡️',
    keywords: ['人际关系', '沟通', '职场适应'],
    supervision: '需要加强自我表达训练',
    createdAt: '2023-12-15T14:00:00Z',
    updatedAt: '2024-01-10T14:00:00Z'
  },
  {
    id: '3',
    visitorId: '3',
    name: '王强',
    summary: '情绪管理问题，易怒，家庭冲突',
    therapyMethod: '箱庭疗法',
    selectedSandTools: ['sand-1', 'sand-5', 'sand-12'],
    lastDate: '2024-01-12',
    nextDate: '2024-01-19',
    total: 8,
    star: true,
    duration: 75,
    crisis: '⚡',
    homework: '❌',
    progress: '⬇️',
    keywords: ['愤怒管理', '家庭冲突', '情绪调节'],
    supervision: '需要危机干预，建议转介',
    createdAt: '2023-11-20T09:00:00Z',
    updatedAt: '2024-01-12T09:00:00Z'
  },
  {
    id: '4',
    visitorId: '4',
    name: '陈静',
    summary: '抑郁情绪，自我价值感低，社交回避',
    therapyMethod: '认知行为疗法',
    selectedSandTools: [],
    lastDate: '2024-01-08',
    total: 12,
    star: false,
    duration: 50,
    crisis: '✅',
    homework: '✅',
    progress: '⬆️',
    keywords: ['抑郁', '自我价值', '社交回避'],
    supervision: '治疗效果良好，可考虑结案',
    createdAt: '2023-10-01T11:00:00Z',
    updatedAt: '2024-01-08T11:00:00Z'
  }
];

// 获取距离上次会话的天数
export const getDaysSinceLastSession = (lastDate?: string): number => {
  if (!lastDate) return 0;
  const last = new Date(lastDate);
  const now = new Date();
  const diffTime = now.getTime() - last.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

// 会话记录接口
interface SessionNote {
  id: string;
  date: string;
  time: string;
  duration: number;
  mood: string;
  topic: string;
  note: string;
  keyQuote?: string;
  keywords?: string[];
}

// 获取个案的会话记录
export const getNotesForCase = (caseId: string): SessionNote[] => {
  const noteMap: Record<string, SessionNote[]> = {
    '1': [
      {
        id: '1-1',
        date: '2024-01-15',
        time: '14:00',
        duration: 50,
        mood: '😟',
        topic: '考试焦虑',
        note: '来访者表现出明显的考试焦虑症状，通过认知重构技术帮助其识别和调整非理性信念。',
        keyQuote: '我总是担心自己会考不好',
        keywords: ['焦虑', '认知重构', '考试']
      },
      {
        id: '1-2', 
        date: '2024-01-08',
        time: '14:00',
        duration: 50,
        mood: '😊',
        topic: '放松训练',
        note: '教授渐进性肌肉放松技术，来访者学习效果良好，焦虑水平有所下降。',
        keywords: ['放松训练', '肌肉放松']
      }
    ],
    '2': [
      {
        id: '2-1',
        date: '2024-01-10',
        time: '10:00', 
        duration: 50,
        mood: '😐',
        topic: '职场人际关系',
        note: '探讨职场人际关系困扰，通过角色扮演练习沟通技巧。',
        keyQuote: '我不知道怎么和同事相处',
        keywords: ['人际关系', '沟通技巧', '职场']
      }
    ],
    '3': [
      {
        id: '3-1',
        date: '2024-01-12',
        time: '09:00',
        duration: 75,
        mood: '😡',
        topic: '愤怒管理',
        note: '来访者情绪激动，表现出明显的愤怒情绪。进行了情绪调节技术训练。',
        keyQuote: '我控制不住自己的脾气',
        keywords: ['愤怒管理', '情绪调节', '危机干预']
      }
    ],
    '4': [
      {
        id: '4-1',
        date: '2024-01-08',
        time: '11:00',
        duration: 50,
        mood: '😌',
        topic: '自我价值探索',
        note: '通过认知行为技术帮助来访者重新构建自我价值体系，抑郁症状有明显缓解。',
        keywords: ['自我价值', '认知行为', '抑郁']
      }
    ]
  };
  
  return noteMap[caseId] || [];
};
