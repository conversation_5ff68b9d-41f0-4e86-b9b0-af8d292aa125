import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Clock, MapPin, User } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import type { Appointment } from '../../types/schedule';
import './Calendar.css';

interface CalendarProps {
  appointments?: Appointment[];
  onDateSelect?: (date: Date) => void;
  onAppointmentClick?: (appointment: Appointment) => void;
  className?: string;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  appointments: Appointment[];
}

const Calendar: React.FC<CalendarProps> = ({
  appointments = [],
  onDateSelect,
  onAppointmentClick,
  className = ''
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);

  // 获取当前月份的所有日期
  const getCalendarDays = (): CalendarDay[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // 获取第一周的开始日期（周一开始）
    const startDate = new Date(firstDay);
    const dayOfWeek = firstDay.getDay();
    startDate.setDate(firstDay.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    
    // 生成42天的日历网格（6周 × 7天）
    const days: CalendarDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const dateStr = date.toISOString().split('T')[0];
      const dayAppointments = appointments.filter(apt => apt.date === dateStr);
      
      days.push({
        date: new Date(date),
        isCurrentMonth: date.getMonth() === month,
        isToday: date.getTime() === today.getTime(),
        appointments: dayAppointments
      });
    }
    
    return days;
  };

  // 导航到上个月
  const goToPreviousMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() - 1);
      return newDate;
    });
  };

  // 导航到下个月
  const goToNextMonth = () => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + 1);
      return newDate;
    });
  };

  // 处理日期点击
  const handleDateClick = (day: CalendarDay) => {
    setSelectedDate(day.date);
    onDateSelect?.(day.date);
  };

  // 处理预约点击
  const handleAppointmentClick = (appointment: Appointment, e: React.MouseEvent) => {
    e.stopPropagation();
    onAppointmentClick?.(appointment);
  };

  // 格式化月份年份
  const formatMonthYear = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月`;
  };

  // 获取预约状态的颜色类名
  const getAppointmentStatusClass = (status: string) => {
    switch (status) {
      case '已确认': return 'status-confirmed';
      case '待确认': return 'status-pending';
      case '进行中': return 'status-ongoing';
      case '已完成': return 'status-completed';
      case '已取消': return 'status-cancelled';
      case '缺席': return 'status-absent';
      default: return 'status-default';
    }
  };

  const renderSelectedDateAppointments = () => {
    if (!selectedDate) return null;
    
    const dateStr = selectedDate.toISOString().split('T')[0];
    const dayAppointments = appointments.filter(apt => apt.date === dateStr);
    
    if (dayAppointments.length === 0) {
      return (
        <motion.p 
          className="no-appointments"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          今日无安排
        </motion.p>
      );
    }
    
    return (
      <div className="appointments-list">
        {dayAppointments.map((appointment, index) => (
          <motion.div 
            key={appointment.id} 
            className={`appointment-item ${getAppointmentStatusClass(appointment.status)}`}
            onClick={() => onAppointmentClick?.(appointment)}
            initial={{ opacity: 0, x: -20, scale: 0.95 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            transition={{ 
              delay: index * 0.1,
              duration: 0.3,
              type: "spring",
              stiffness: 300,
              damping: 25
            }}
            // whileHover={{ 
            //   scale: 1.02,
            //   x: 4,
            //   transition: { duration: 0.2 }
            // }}
            // whileTap={{ scale: 0.98 }}
          >
            <motion.div 
              className="appointment-time-info"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 + 0.1 }}
            >
              <Clock size={14} />
              <span>{appointment.startTime} - {appointment.endTime}</span>
            </motion.div>
            <motion.div 
              className="appointment-details"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 + 0.15 }}
            >
              <div className="appointment-subject">{appointment.subject}</div>
              <div className="appointment-visitor">{appointment.visitorName}</div>
              {appointment.room && (
                <motion.div 
                  className="appointment-room"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 + 0.2 }}
                >
                  <MapPin size={12} />
                  <span>{appointment.room}</span>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        ))}
      </div>
    );
  };

  const calendarDays = getCalendarDays();
  const weekDays = ['一', '二', '三', '四', '五', '六', '日'];

  return (
    <motion.div 
      className={`liquid-calendar ${className}`}
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        ease: [0.4, 0, 0.2, 1],
        staggerChildren: 0.1
      }}
      // whileHover={{ 
      //   scale: 1.02,
      //   transition: { duration: 0.3 }
      // }}
    >
      {/* 日历头部 */}
      <motion.div 
        className="calendar-header"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <div className="calendar-title">
          <motion.div
            // whileHover={{ rotate: 360 }}
            // transition={{ duration: 0.6 }}
          >
            <CalendarIcon className="calendar-icon" />
          </motion.div>
          <h3>日程安排</h3>
        </div>
        <div className="calendar-navigation">
          <motion.button 
            className="nav-btn prev-btn"
            onClick={goToPreviousMonth}
            aria-label="上个月"
            // whileHover={{ scale: 1.1, x: -2 }}
            // whileTap={{ scale: 0.95 }}
            // transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <ChevronLeft size={18} />
          </motion.button>
          <motion.span 
            className="current-month"
            key={`${currentDate.getFullYear()}-${currentDate.getMonth()}`}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {formatMonthYear(currentDate)}
          </motion.span>
          <motion.button 
            className="nav-btn next-btn"
            onClick={goToNextMonth}
            aria-label="下个月"
            // whileHover={{ scale: 1.1, x: 2 }}
            // whileTap={{ scale: 0.95 }}
            // transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <ChevronRight size={18} />
          </motion.button>
        </div>
      </motion.div>

      {/* 星期标题 */}
      <div className="calendar-weekdays">
        {weekDays.map(day => (
          <div key={day} className="weekday">
            {day}
          </div>
        ))}
      </div>

      {/* 日历网格 */}
      <motion.div 
        className="calendar-grid"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.5 }}
      >
        {calendarDays.map((day, index) => {
          const isSelected = selectedDate && day.date.getTime() === selectedDate.getTime();
          const isHovered = hoveredDate && day.date.getTime() === hoveredDate.getTime();
          
          return (
            <motion.div
              key={index}
              className={`calendar-day ${
                day.isCurrentMonth ? 'current-month' : 'other-month'
              } ${
                day.isToday ? 'today' : ''
              } ${
                isSelected ? 'selected' : ''
              } ${
                isHovered ? 'hovered' : ''
              } ${
                day.appointments.length > 0 ? 'has-appointments' : ''
              }`}
              onClick={() => handleDateClick(day)}
              onMouseEnter={() => setHoveredDate(day.date)}
              onMouseLeave={() => setHoveredDate(null)}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ 
                delay: 0.6 + (index % 7) * 0.05 + Math.floor(index / 7) * 0.1,
                duration: 0.3,
                type: "spring",
                stiffness: 300,
                damping: 20
              }}
              // whileHover={{ 
              //   scale: 1.05,
              //   transition: { duration: 0.2 }
              // }}
              // whileTap={{ scale: 0.95 }}
            >
              <span className="day-number">{day.date.getDate()}</span>
              
              {/* 预约指示器 */}
              <AnimatePresence>
                {day.appointments.length > 0 && (
                  <motion.div 
                    className="appointments-container"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    {day.appointments.slice(0, 3).map((appointment, aptIndex) => (
                      <motion.div
                        key={appointment.id}
                        className={`appointment-indicator ${getAppointmentStatusClass(appointment.status)}`}
                        onClick={(e) => handleAppointmentClick(appointment, e)}
                        title={`${appointment.startTime} - ${appointment.subject}`}
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: aptIndex * 0.1 }}
                        // whileHover={{ scale: 1.1 }}
                      >
                        <Clock size={10} />
                        <span className="appointment-time">{appointment.startTime}</span>
                      </motion.div>
                    ))}
                    {day.appointments.length > 3 && (
                      <motion.div 
                        className="more-appointments"
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.3 }}
                      >
                        +{day.appointments.length - 3}
                      </motion.div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </motion.div>

      {/* 选中日期的详细信息 */}
      <AnimatePresence>
        {selectedDate && (
          <motion.div 
            className="selected-date-info"
            initial={{ opacity: 0, y: 20, height: 0 }}
            animate={{ opacity: 1, y: 0, height: "auto" }}
            exit={{ opacity: 0, y: -20, height: 0 }}
            transition={{ 
              duration: 0.4,
              ease: [0.4, 0, 0.2, 1]
            }}
          >
            <motion.h4
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              {selectedDate.toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
              })}
            </motion.h4>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              {renderSelectedDateAppointments()}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default Calendar;