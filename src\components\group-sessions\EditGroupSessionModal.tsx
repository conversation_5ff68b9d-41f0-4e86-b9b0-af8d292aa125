import React, { useState, useEffect } from 'react';
import { FormModal } from '../ui/FormModal';
import { Input, Select, Textarea } from '../ui';
import { Users, Calendar, Clock, MapPin, FileText, Settings } from 'lucide-react';
import type { GroupSession } from '../../types/groupSession';

interface EditGroupSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  session: GroupSession | null;
  onUpdate: (session: GroupSession) => void;
}

interface EditGroupSessionForm {
  title: string;
  description: string;
  sessionType: string;
  targetAge: string;
  maxParticipants: string;
  duration: string;
  frequency: string;
  totalSessions: string;
  startDate: string;
  endDate: string;
  meetingTime: string;
  location: string;
  therapistName: string;
  requirements: string;
  notes: string;
  status: string;
}

export const EditGroupSessionModal: React.FC<EditGroupSessionModalProps> = ({
  isOpen,
  onClose,
  session,
  onUpdate
}) => {
  const [formData, setFormData] = useState<EditGroupSessionForm>({
    title: '',
    description: '',
    sessionType: '',
    targetAge: '',
    maxParticipants: '',
    duration: '',
    frequency: '',
    totalSessions: '',
    startDate: '',
    endDate: '',
    meetingTime: '',
    location: '',
    therapistName: '',
    requirements: '',
    notes: '',
    status: ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof EditGroupSessionForm, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 当session变化时更新表单数据
  useEffect(() => {
    if (session && isOpen) {
      setFormData({
        title: session.title || '',
        description: session.description || '',
        sessionType: session.sessionType || '',
        targetAge: session.targetAge || '',
        maxParticipants: session.maxParticipants.toString(),
        duration: session.duration.toString(),
        frequency: session.frequency || '',
        totalSessions: session.totalSessions?.toString() || '',
        startDate: session.startDate || '',
        endDate: session.endDate || '',
        meetingTime: session.meetingTime || '',
        location: session.location || '',
        therapistName: session.therapistName || '',
        requirements: session.requirements || '',
        notes: session.notes || '',
        status: session.status || ''
      });
      setErrors({});
    }
  }, [session, isOpen]);

  const handleInputChange = (field: keyof EditGroupSessionForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof EditGroupSessionForm, string>> = {};

    if (!formData.title.trim()) {
      newErrors.title = '请输入团体名称';
    }

    if (!formData.sessionType) {
      newErrors.sessionType = '请选择团体类型';
    }

    if (!formData.targetAge) {
      newErrors.targetAge = '请选择目标年龄';
    }

    if (!formData.maxParticipants.trim()) {
      newErrors.maxParticipants = '请输入最大参与人数';
    } else if (parseInt(formData.maxParticipants) <= 0) {
      newErrors.maxParticipants = '参与人数必须大于0';
    }

    if (!formData.duration.trim()) {
      newErrors.duration = '请输入活动时长';
    } else if (parseInt(formData.duration) <= 0) {
      newErrors.duration = '活动时长必须大于0';
    }

    if (!formData.frequency) {
      newErrors.frequency = '请选择活动频率';
    }

    if (!formData.startDate) {
      newErrors.startDate = '请选择开始日期';
    }

    if (!formData.meetingTime) {
      newErrors.meetingTime = '请设置会议时间';
    }

    if (!formData.location.trim()) {
      newErrors.location = '请输入活动地点';
    }

    if (!formData.therapistName.trim()) {
      newErrors.therapistName = '请输入治疗师姓名';
    }

    if (!formData.status) {
      newErrors.status = '请选择状态';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !session) return;

    setIsSubmitting(true);
    try {
      const updatedSession: GroupSession = {
        ...session,
        title: formData.title,
        description: formData.description || undefined,
        sessionType: formData.sessionType as GroupSession['sessionType'],
        targetAge: formData.targetAge as GroupSession['targetAge'],
        maxParticipants: parseInt(formData.maxParticipants),
        duration: parseInt(formData.duration),
        frequency: formData.frequency as GroupSession['frequency'],
        totalSessions: formData.totalSessions ? parseInt(formData.totalSessions) : undefined,
        startDate: formData.startDate,
        endDate: formData.endDate || undefined,
        meetingTime: formData.meetingTime,
        location: formData.location,
        therapistName: formData.therapistName,
        requirements: formData.requirements || undefined,
        notes: formData.notes || undefined,
        status: formData.status as GroupSession['status'],
        updatedAt: new Date().toISOString()
      };

      onUpdate(updatedSession);
      onClose();
    } catch (error) {
      console.error('更新团体活动失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!session) return null;

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="编辑团体活动"
      subtitle={`编辑团体活动：${session.title}`}
      size="lg"
      onSubmit={handleSubmit}
      submitText="保存更改"
      isSubmitting={isSubmitting}
    >
      {/* 基本信息 */}
      <div className="form-section">
        <div className="form-section-header">
          <FileText size={16} />
          <h4 className="form-section-title">基本信息</h4>
        </div>
        
        <div className="form-grid-1">
          <Input
            label="团体名称"
            required
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            error={errors.title}
            placeholder="请输入团体名称"
          />
        </div>
        
        <div className="form-grid-1">
          <Textarea
            label="团体描述"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="请输入团体活动的详细描述"
            rows={3}
          />
        </div>

        <div className="form-grid-1">
          <Select
            label="活动状态"
            required
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            error={errors.status}
            options={[
              { value: '', label: '请选择状态' },
              { value: '计划中', label: '计划中' },
              { value: '进行中', label: '进行中' },
              { value: '已完成', label: '已完成' },
              { value: '已取消', label: '已取消' },
              { value: '暂停', label: '暂停' }
            ]}
          />
        </div>
      </div>

      {/* 团体设置 */}
      <div className="form-section">
        <div className="form-section-header">
          <Settings size={16} />
          <h4 className="form-section-title">团体设置</h4>
        </div>
        
        <div className="form-grid-2">
          <Select
            label="团体类型"
            required
            value={formData.sessionType}
            onChange={(e) => handleInputChange('sessionType', e.target.value)}
            error={errors.sessionType}
            options={[
              { value: '', label: '请选择团体类型' },
              { value: '开放式团体', label: '开放式团体' },
              { value: '封闭式团体', label: '封闭式团体' },
              { value: '主题团体', label: '主题团体' },
              { value: '发展性团体', label: '发展性团体' }
            ]}
          />
          
          <Select
            label="目标年龄"
            required
            value={formData.targetAge}
            onChange={(e) => handleInputChange('targetAge', e.target.value)}
            error={errors.targetAge}
            options={[
              { value: '', label: '请选择目标年龄' },
              { value: '儿童', label: '儿童（6-12岁）' },
              { value: '青少年', label: '青少年（13-17岁）' },
              { value: '成人', label: '成人（18-65岁）' },
              { value: '老年', label: '老年（65岁以上）' },
              { value: '混合', label: '混合年龄段' }
            ]}
          />
        </div>

        <div className="form-grid-3">
          <Input
            label="最大参与人数"
            type="number"
            required
            value={formData.maxParticipants}
            onChange={(e) => handleInputChange('maxParticipants', e.target.value)}
            error={errors.maxParticipants}
            placeholder="请输入人数"
            min="1"
            max="20"
          />
          
          <Input
            label="活动时长（分钟）"
            type="number"
            required
            value={formData.duration}
            onChange={(e) => handleInputChange('duration', e.target.value)}
            error={errors.duration}
            placeholder="请输入时长"
            min="30"
            max="480"
          />
          
          <Select
            label="活动频率"
            required
            value={formData.frequency}
            onChange={(e) => handleInputChange('frequency', e.target.value)}
            error={errors.frequency}
            options={[
              { value: '', label: '请选择频率' },
              { value: '单次', label: '单次活动' },
              { value: '每周', label: '每周一次' },
              { value: '每两周', label: '每两周一次' },
              { value: '每月', label: '每月一次' }
            ]}
          />
        </div>

        {formData.frequency !== '单次' && (
          <div className="form-grid-1">
            <Input
              label="总次数"
              type="number"
              value={formData.totalSessions}
              onChange={(e) => handleInputChange('totalSessions', e.target.value)}
              placeholder="请输入总次数"
              min="1"
              max="52"
            />
          </div>
        )}
      </div>

      {/* 时间安排 */}
      <div className="form-section">
        <div className="form-section-header">
          <Calendar size={16} />
          <h4 className="form-section-title">时间安排</h4>
        </div>
        
        <div className="form-grid-2">
          <Input
            label="开始日期"
            type="date"
            required
            value={formData.startDate}
            onChange={(e) => handleInputChange('startDate', e.target.value)}
            error={errors.startDate}
          />
          
          <Input
            label="结束日期"
            type="date"
            value={formData.endDate}
            onChange={(e) => handleInputChange('endDate', e.target.value)}
            min={formData.startDate}
          />
        </div>
        
        <div className="form-grid-1">
          <Input
            label="会议时间"
            type="time"
            required
            value={formData.meetingTime}
            onChange={(e) => handleInputChange('meetingTime', e.target.value)}
            error={errors.meetingTime}
          />
        </div>
      </div>

      {/* 地点和人员 */}
      <div className="form-section">
        <div className="form-section-header">
          <MapPin size={16} />
          <h4 className="form-section-title">地点和人员</h4>
        </div>
        
        <div className="form-grid-2">
          <Input
            label="活动地点"
            required
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            error={errors.location}
            placeholder="请输入活动地点"
          />
          
          <Input
            label="治疗师姓名"
            required
            value={formData.therapistName}
            onChange={(e) => handleInputChange('therapistName', e.target.value)}
            error={errors.therapistName}
            placeholder="请输入治疗师姓名"
          />
        </div>
      </div>

      {/* 其他信息 */}
      <div className="form-section">
        <div className="form-section-header">
          <Users size={16} />
          <h4 className="form-section-title">其他信息</h4>
        </div>
        
        <div className="form-grid-1">
          <Textarea
            label="参与要求"
            value={formData.requirements}
            onChange={(e) => handleInputChange('requirements', e.target.value)}
            placeholder="请输入参与者的要求和条件"
            rows={2}
          />
        </div>
        
        <div className="form-grid-1">
          <Textarea
            label="备注"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="请输入其他备注信息"
            rows={3}
          />
        </div>
      </div>
    </FormModal>
  );
};

export default EditGroupSessionModal;
