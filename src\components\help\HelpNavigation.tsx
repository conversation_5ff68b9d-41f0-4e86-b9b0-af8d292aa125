import React from 'react';
import { 
  Book, 
  Users, 
  FileText, 
  Calendar, 
  Wrench, 
  UsersRound, 
  BarChart3, 
  HelpCircle,
  MessageCircle,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import type { HelpCategory, HelpNavigation } from '../../types/help';

interface HelpNavigationProps {
  categories: HelpCategory[];
  currentNavigation: HelpNavigation;
  onNavigate: (categoryId: string, sectionId: string, itemId: string) => void;
  searchQuery?: string;
}

interface NavigationItemProps {
  item: any;
  section: any;
  category: HelpCategory;
  isActive: boolean;
  onNavigate: (categoryId: string, sectionId: string, itemId: string) => void;
  searchQuery?: string;
}

// 图标映射
const iconMap = {
  'book': Book,
  'users': Users,
  'file-text': FileText,
  'calendar': Calendar,
  'wrench': Wrench,
  'users-round': UsersRound,
  'bar-chart-3': BarChart3,
  'help-circle': HelpCircle,
  'message-circle': MessageCircle
};

// 根据章节ID获取图标
const getSectionIcon = (sectionId: string) => {
  const iconName = {
    'getting-started': 'book',
    'visitor-management': 'users',
    'case-management': 'file-text',
    'schedule-management': 'calendar',
    'sandtool-management': 'wrench',
    'group-sessions': 'users-round',
    'statistics': 'bar-chart-3',
    'common-issues': 'help-circle',
    'support': 'message-circle'
  }[sectionId] || 'help-circle';

  const IconComponent = iconMap[iconName as keyof typeof iconMap];
  return <IconComponent size={16} />;
};

const NavigationItem: React.FC<NavigationItemProps> = ({
  item,
  section,
  category,
  isActive,
  onNavigate,
  searchQuery
}) => {
  const handleClick = () => {
    onNavigate(category.id, section.id, item.id);
  };

  // 高亮搜索关键词
  const highlightText = (text: string) => {
    if (!searchQuery) return text;
    
    const regex = new RegExp(`(${searchQuery})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="search-highlight">{part}</span>
      ) : (
        part
      )
    );
  };

  return (
    <button
      className={`navigation-item ${isActive ? 'active' : ''}`}
      onClick={handleClick}
    >
      <span className="item-title">{highlightText(item.title)}</span>
      {item.tags && (
        <div className="item-tags">
          {item.tags.slice(0, 2).map((tag: string, index: number) => (
            <span key={index} className="item-tag">
              {highlightText(tag)}
            </span>
          ))}
        </div>
      )}
    </button>
  );
};

const HelpNavigation: React.FC<HelpNavigationProps> = ({
  categories,
  currentNavigation,
  onNavigate,
  searchQuery
}) => {
  // 智能展开策略：只展开当前活跃的分类，减少导航长度
  const [expandedCategories, setExpandedCategories] = React.useState<Set<string>>(
    new Set([currentNavigation.currentCategory])
  );

  // 切换分类展开状态 - 智能展开策略
  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      // 展开新分类时，可选择性关闭其他分类（保持界面简洁）
      newExpanded.clear();
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // 当导航到新分类时，自动展开对应分类
  React.useEffect(() => {
    setExpandedCategories(new Set([currentNavigation.currentCategory]));
  }, [currentNavigation.currentCategory]);

  return (
    <nav className="help-navigation">
      {categories.map(category => {
        const isCategoryExpanded = expandedCategories.has(category.id);
        const isCategoryActive = currentNavigation.currentCategory === category.id;

        return (
          <div key={category.id} className="navigation-category">
            <button
              className={`category-header ${isCategoryActive ? 'active' : ''}`}
              onClick={() => toggleCategory(category.id)}
            >
              <span className="category-title">{category.title}</span>
              <div className="category-meta">
                <span className="category-count">
                  {category.sections.reduce((total, section) => total + section.children.length, 0)} 项
                </span>
                {isCategoryExpanded ? (
                  <ChevronDown size={16} className="toggle-icon" />
                ) : (
                  <ChevronRight size={16} className="toggle-icon" />
                )}
              </div>
            </button>

            {isCategoryExpanded && (
              <div className="category-content">
                {category.sections.map(section => (
                  <div key={section.id} className="navigation-section-simplified">
                    {/* 将章节作为分组标题，不需要额外的展开/折叠 */}
                    <div className="section-header-simplified">
                      <div className="section-info">
                        <div className="section-icon">
                          {getSectionIcon(section.id)}
                        </div>
                        <div className="section-text">
                          <span className="section-title">{section.title}</span>
                          <span className="section-description">{section.description}</span>
                        </div>
                      </div>
                    </div>

                    {/* 直接显示章节下的所有项目，不需要额外的展开步骤 */}
                    <div className="section-content-direct">
                      {section.children.map(item => (
                        <NavigationItem
                          key={item.id}
                          item={item}
                          section={section}
                          category={category}
                          isActive={currentNavigation.currentItem === item.id && currentNavigation.currentSection === section.id}
                          onNavigate={onNavigate}
                          searchQuery={searchQuery}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </nav>
  );
};

export default HelpNavigation;
