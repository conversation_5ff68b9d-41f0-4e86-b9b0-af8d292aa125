import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: './', // 使用相对路径，适合Electron应用
  server: {
    port: 5173,
    open: false, // 不自动打开浏览器，因为是桌面应用
    strictPort: true // 如果端口被占用则失败
  },
  build: {
    outDir: 'dist', // 构建输出目录
    assetsDir: 'assets',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          charts: ['recharts'],
          icons: ['lucide-react']
        }
      }
    }
  }
})
