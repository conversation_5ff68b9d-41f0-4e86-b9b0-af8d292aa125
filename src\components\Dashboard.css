/* Dashboard组件样式 - 优化竖向比例 */
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: transparent;
  min-height: calc(100vh - 60px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard::-webkit-scrollbar {
  display: none;
}

/* 欢迎横幅 - 移除渐变色，优化高度 */
.welcome-banner {
  background: #3b82f6;
  border-radius: 12px;
  padding: 24px;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.12);
  flex-shrink: 0;
  height: auto;
  min-height: 120px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 6px 0;
}

.welcome-text p {
  margin: 4px 0;
  opacity: 0.9;
  font-size: 16px;
}

.welcome-icon {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.logo-image {
  width: 64px;
  height: 64px;
  object-fit: contain;
}

/* 统计卡片网格 - 移除margin，使用gap */
.stats-grid {
  display: flex;
  gap: 20px;
  flex-wrap: nowrap;
  flex-shrink: 0;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  border: 1px solid #e5e7eb;
  transition: all 0.15s ease;
  flex: 1;
  min-width: 0;
}

.stat-card:hover {
  /* transform: translateY(-1px); */
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-content h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

/* 轮播图区域 - 固定高度避免重叠 */
.carousel-section {
  padding: 0;
  flex-shrink: 0;
  height: 280px;
  margin-bottom: 32px;
  overflow: hidden;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .welcome-banner {
    padding: 24px 20px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .welcome-text h1 {
    font-size: 24px;
  }
  
  .stats-grid {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
  }
  

  

}

@media (max-width: 1024px) {
  .stats-grid {
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
    gap: 12px;
  }
  
  .stat-value {
    font-size: 28px;
  }
}
