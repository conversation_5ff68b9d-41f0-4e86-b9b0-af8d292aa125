/* 预约详情弹窗样式 */

/* 模态框基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--spacing-lg);
}

.modal-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.appointment-detail-modal {
  max-width: 700px;
  max-height: 90vh;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius);
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 头部样式 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  flex-shrink: 0;
}

.header-info {
  flex: 1;
}

.header-info h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.header-badges {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  margin-top: var(--spacing-sm);
}

.status-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.urgency-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: white;
}

.urgency-badge.urgent {
  background: var(--error);
}

.urgency-badge.high {
  background: var(--warning);
}

.urgency-badge.normal {
  background: var(--gray-500);
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
  flex-shrink: 0;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  border-bottom: 2px solid transparent;
  white-space: nowrap;
}

.tab-btn:hover {
  color: var(--text-primary);
  background: var(--bg-primary);
}

.tab-btn.active {
  color: var(--primary-blue);
  background: var(--bg-primary);
  border-bottom-color: var(--primary-blue);
}

/* 内容区域 */
.tab-content {
  padding: var(--spacing-xl);
  overflow-y: auto;
}

/* 信息卡片样式 */
.info-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
}

.info-card-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.info-card-content {
  padding: var(--spacing-xl);
}

/* 信息行样式 */
.info-row {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-light);
  align-items: center;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.info-value {
  font-size: var(--text-sm);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.info-value.highlight {
  font-weight: var(--font-semibold);
  color: var(--primary-blue);
}

/* 操作按钮区域 */
.action-section {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
  flex-shrink: 0;
}

.action-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
}

.action-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-blue);
  color: var(--primary-blue);
}

.action-btn.primary {
  background: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.action-btn.primary:hover {
  background: var(--primary-blue-dark);
  border-color: var(--primary-blue-dark);
}

.action-btn.danger {
  color: var(--error);
  border-color: var(--error);
}

.action-btn.danger:hover {
  background: rgb(239 68 68 / 0.1);
}

/* 时间线样式 */
.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-light);
}

.timeline-item {
  position: relative;
  padding-left: var(--spacing-3xl);
  margin-bottom: var(--spacing-xl);
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 3px;
  top: 2px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--primary-blue);
  border: 2px solid var(--bg-primary);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-content {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius);
  border: 1px solid var(--border-light);
}

.timeline-date {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-sm);
}

.timeline-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
  margin: 0;
}

/* 备注区域 */
.notes-section {
  margin-top: var(--spacing-xl);
}

.notes-textarea {
  width: 100%;
  min-height: 100px;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background: var(--bg-primary);
  resize: vertical;
}

.notes-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgb(79 156 249 / 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: var(--spacing-md);
  }
  
  .appointment-detail-modal {
    max-width: 100%;
    max-height: 100vh;
    margin: 0;
    border-radius: var(--radius);
  }
  
  .info-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .info-label {
    font-weight: var(--font-semibold);
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    justify-content: center;
  }
}
