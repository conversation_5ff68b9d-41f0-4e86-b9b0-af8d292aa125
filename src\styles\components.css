/* 通用组件样式 */
@import './variables.css';

/* 卡片组件 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0;
}

.card-content {
  padding: var(--spacing-xl);
}

.card-footer {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* 按钮组件 */
button.btn,
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 10px 16px;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.12s ease;
  text-decoration: none;
  white-space: nowrap;
}

button.btn:disabled,
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn .btn-icon,
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 按钮尺寸 */
button.btn-sm,
.btn-sm {
  padding: 8px 12px;
  font-size: var(--text-xs);
}

button.btn-lg,
.btn-lg {
  padding: 12px 20px;
  font-size: var(--text-base);
}

/* 按钮变体 */
button.btn-primary,
.btn-primary {
  background: var(--primary-blue);
  color: var(--text-inverse);
  border-color: var(--primary-blue);
}

button.btn-primary:hover:not(:disabled),
.btn-primary:hover:not(:disabled) {
  background: var(--primary-blue-dark);
  border-color: var(--primary-blue-dark);
}

button.btn-secondary,
.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

button.btn-secondary:hover:not(:disabled),
.btn-secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
}

button.btn-success,
.btn-success {
  background: var(--success);
  color: var(--text-inverse);
  border-color: var(--success);
}

button.btn-success:hover:not(:disabled),
.btn-success:hover:not(:disabled) {
  background: var(--success-dark);
  border-color: var(--success-dark);
}

button.btn-warning,
.btn-warning {
  background: var(--warning);
  color: var(--text-inverse);
  border-color: var(--warning);
}

button.btn-warning:hover:not(:disabled),
.btn-warning:hover:not(:disabled) {
  background: var(--warning-dark);
  border-color: var(--warning-dark);
}

button.btn-danger,
.btn-danger {
  background: var(--error);
  color: var(--text-inverse);
  border-color: var(--error);
}

button.btn-danger:hover:not(:disabled),
.btn-danger:hover:not(:disabled) {
  background: var(--error-dark);
  border-color: var(--error-dark);
}

button.btn-ghost,
.btn-ghost {
  background: transparent;
  color: var(--primary-blue);
  border-color: transparent;
}

button.btn-ghost:hover:not(:disabled),
.btn-ghost:hover:not(:disabled) {
  background: var(--bg-secondary);
}

/* 表单组件 */
.form-group {
  margin-bottom: var(--spacing-xl);
}

/* FilterBar中的表单组件不需要margin */
.filter-bar .form-group {
  margin-bottom: 0 !important;
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-label.required::after {
  content: " *";
  color: var(--error);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: 6px;
  transition: border-color 0.12s ease, box-shadow 0.12s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgb(59 130 246 / 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--error);
}

.form-input.error:focus,
.form-select.error:focus,
.form-textarea.error:focus {
  box-shadow: 0 0 0 2px rgb(239 68 68 / 0.1);
}

.form-help {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

.form-error {
  font-size: var(--text-xs);
  color: var(--error);
  margin-top: var(--spacing-xs);
}

/* 自定义复选框样式 */
.checkbox-container {
  display: flex !important;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
  position: relative;
  padding-left: 24px;
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.checkbox-container input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 2px;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: var(--bg-primary);
  border: 2px solid var(--border-medium);
  border-radius: 3px;
  transition: all 0.2s;
}

.checkbox-container:hover input ~ .checkmark {
  border-color: var(--primary-blue);
}

.checkbox-container input:checked ~ .checkmark {
  background-color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* 表格组件 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  table-layout: fixed;
}

.table thead {
  background: var(--bg-secondary);
}

.table th {
  padding: var(--spacing-lg);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
  white-space: nowrap;
}

.table td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-primary);
  vertical-align: middle;
  line-height: 1.4;
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: #f1f5f9 !important;
}

.table tbody tr:nth-child(even) {
  background-color: #f8fafc !important;
}

.table tbody tr:nth-child(even):hover {
  background-color: #f1f5f9 !important;
}

/* 表格行选择样式 - 统一的选中状态 */
.table tbody tr.selected {
  background-color: #eff6ff !important;
  border-color: #3b82f6;
}

.table tbody tr.selected:hover {
  background-color: #dbeafe !important;
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 统一页头样式 - 删除特定边框 */
.table tbody tr:nth-child(1) td {
  border-top: none;
}

.table th:nth-child(1),
.table td:nth-child(1) {
  border-right: none;
}

/* 选择按钮样式 - 统一样式 */
.table button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.table button:hover {
  background-color: #f3f4f6;
}

/* 操作列特殊处理 */
.table th:last-child,
.table td:last-child {
  text-align: center;
  width: 120px;
  min-width: 120px;
}

/* 状态列样式 - 统一状态列显示 */
.table .status-cell {
  text-align: center;
  padding: 8px 12px;
}

.table .status-cell .badge {
  font-size: var(--text-xs);
  font-weight: 500;
  min-width: 60px;
  justify-content: center;
}

/* 操作列按钮布局 */
.table td:last-child .flex {
  justify-content: center;
  align-items: center;
}

/* 通用样式 - 星标图标 */
.star-icon {
  color: #fbbf24;
  margin-left: 4px;
}

/* 通用样式 - 危机和进展标签 */
.crisis-badge,
.progress-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  background: rgb(79 156 249 / 0.1);
  color: var(--primary-blue-dark);
}

.badge-success {
  background: rgb(16 185 129 / 0.1);
  color: var(--success-dark);
}

.badge-warning {
  background: rgb(245 158 11 / 0.1);
  color: var(--warning-dark);
}

.badge-danger {
  background: rgb(239 68 68 / 0.1);
  color: var(--error-dark);
}

.badge-gray {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* 页面容器 */
.page-container {
  padding: var(--spacing-3xl);
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-4xl);
  padding-bottom: var(--spacing-3xl);
  border-bottom: 1px solid var(--border-light);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  min-height: 40px; /* 确保基础高度 */
}

/* 统一控件对齐样式 */
.page-actions > *,
.header-actions > * {
  vertical-align: top;
  line-height: 1;
}

.page-actions select,
.header-actions select,
.page-actions .form-select,
.header-actions .form-select {
  height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.page-actions button,
.header-actions button,
.page-actions .btn,
.header-actions .btn {
  height: 40px;
  box-sizing: border-box;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.gap-xl {
  gap: var(--spacing-xl);
}

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.text-center { text-align: center; }
.text-right { text-align: right; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* 响应式工具类 */
@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-xl);
  }
  
  .page-header {
    margin-bottom: var(--spacing-3xl);
    padding-bottom: var(--spacing-xl);
  }
  
  .page-title {
    font-size: var(--text-2xl);
  }
  
  .page-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
