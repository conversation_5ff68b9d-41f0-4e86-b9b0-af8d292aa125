// 帮助中心类型定义

export interface HelpItem {
  id: string;
  title: string;
  content: string | React.ReactNode;
  type: 'guide' | 'faq' | 'video' | 'contact';
  tags?: string[];
  steps?: HelpStep[];
  images?: string[];
}

export interface HelpStep {
  id: string;
  title: string;
  description: string;
  image?: string;
  tips?: string[];
  warning?: string;
}

export interface HelpSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  description?: string;
  children: HelpItem[];
}

export interface HelpCategory {
  id: string;
  title: string;
  sections: HelpSection[];
}

export interface SearchResult {
  item: HelpItem;
  section: HelpSection;
  category: HelpCategory;
  matchType: 'title' | 'content' | 'tags';
  score: number;
}

export interface HelpNavigation {
  currentCategory: string;
  currentSection: string;
  currentItem: string;
}

export interface ContactInfo {
  type: 'wechat' | 'email' | 'phone' | 'website';
  label: string;
  value: string;
  qrCode?: string;
  description?: string;
}
