import React, { useState, useEffect } from 'react';
import { FormModal } from '../ui/FormModal';
import { Input, Select, Textarea } from '../ui';
import { Calendar, Clock, User, MapPin, FileText } from 'lucide-react';
import { caseService, visitorService } from '../../services';
import { reminderService } from '../../services/reminderService';
import type { Appointment, AppointmentType, UrgencyLevel } from '../../types/schedule';
import type { SimpleCase } from '../../types/case';
import type { Visitor } from '../../types/visitor';

interface CreateAppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (appointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialDate?: string;
  initialTime?: string;
}

interface FormData {
  // 关联信息
  caseId: string;
  visitorId: string;
  
  // 来访者信息
  visitorName: string;
  visitorPhone: string;
  visitorAge: string;
  visitorGender: '' | '男' | '女';
  
  // 时间信息
  date: string;
  startTime: string;
  duration: string;
  customDuration: string; // 自定义时长
  
  // 预约详情
  type: string;
  customType: string; // 自定义预约类型
  urgency: UrgencyLevel;
  room: string;
  customRoom: string; // 自定义咨询室
  therapistName: string;
  subject: string;
  description: string;
  notes: string;
  
  // 提醒设置
  reminderEnabled: boolean;
  reminderTime: number;
  
  // 其他设置
  isFirstSession: boolean;
}

export const CreateAppointmentModal: React.FC<CreateAppointmentModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialDate = '',
  initialTime = ''
}) => {
  const [formData, setFormData] = useState<FormData>({
    caseId: '',
    visitorId: '',
    visitorName: '',
    visitorPhone: '',
    visitorAge: '',
    visitorGender: '',
    date: initialDate || new Date().toISOString().split('T')[0],
    startTime: initialTime || '09:00',
    duration: '60',
    customDuration: '',
    type: '',
    customType: '',
    urgency: '普通',
    room: '',
    customRoom: '',
    therapistName: '',
    subject: '',
    description: '',
    notes: '',
    reminderEnabled: true, // 默认启用提醒
    reminderTime: 15,
    isFirstSession: false,
  });

  const [errors, setErrors] = useState<Partial<Record<keyof FormData, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // 数据状态
  const [cases, setCases] = useState<SimpleCase[]>([]);
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  
  // 搜索状态
  const [caseSearchQuery, setCaseSearchQuery] = useState('');
  const [visitorSearchQuery, setVisitorSearchQuery] = useState('');

  // 过滤个案列表
  const filteredCases = cases.filter(caseItem => {
    const query = caseSearchQuery.toLowerCase().trim();
    return !query || 
           caseItem.summary.toLowerCase().includes(query) ||
           caseItem.name.toLowerCase().includes(query) ||
           caseItem.therapyMethod.toLowerCase().includes(query);
  });

  // 过滤来访者列表
  const filteredVisitors = visitors.filter(visitor => {
    const query = visitorSearchQuery.toLowerCase().trim();
    return !query ||
           visitor.name.toLowerCase().includes(query) ||
           visitor.phone.includes(query) ||
           (visitor.gender && visitor.gender.toLowerCase().includes(query));
  });

  // 预设咨询室列表
  const presetRooms = [
    '沙盘治疗室1号',
    '沙盘治疗室2号',
    '个体咨询室1号',
    '个体咨询室2号',
    '团体咨询室',
    '游戏治疗室'
  ];

  // 预设时长选项（分钟）
  const presetDurations = [
    { value: '30', label: '30分钟' },
    { value: '45', label: '45分钟' },
    { value: '60', label: '60分钟' },
    { value: '90', label: '90分钟' },
    { value: '120', label: '120分钟' },
    { value: 'custom', label: '自定义' }
  ];

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      try {
        const [casesData, visitorsData] = await Promise.all([
          caseService.getAllCases(),
          visitorService.getAllVisitors()
        ]);
        setCases(casesData);
        setVisitors(visitorsData);
      } catch (error) {
        console.error('加载数据失败:', error);
      }
    };
    
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 当选择个案时，自动填充来访者信息
    if (field === 'caseId' && value) {
      const selectedCase = cases.find(c => c.id === value);
      if (selectedCase) {
        const visitor = visitors.find(v => v.id === selectedCase.visitorId);
        if (visitor) {
          setFormData(prev => ({
            ...prev,
            visitorId: visitor.id,
            visitorName: visitor.name,
            visitorPhone: visitor.phone,
            visitorAge: visitor.age.toString(),
            visitorGender: visitor.gender,
            subject: selectedCase.summary, // 使用个案简介作为默认主题
            type: selectedCase.therapyMethod === '箱庭疗法' ? '沙盘疗法' : '个体咨询'
          }));
        }
      }
    }
    
    // 当选择来访者时，清空个案选择（如果不匹配）
    if (field === 'visitorId' && value) {
      const visitor = visitors.find(v => v.id === value);
      if (visitor) {
        setFormData(prev => ({
          ...prev,
          visitorName: visitor.name,
          visitorPhone: visitor.phone,
          visitorAge: visitor.age.toString(),
          visitorGender: visitor.gender
        }));
        
        // 检查当前选择的个案是否属于这个来访者
        const currentCase = cases.find(c => c.id === formData.caseId);
        if (currentCase && currentCase.visitorId !== value) {
          setFormData(prev => ({ ...prev, caseId: '' }));
        }
      }
    }
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof FormData, string>> = {};

    if (!formData.visitorName.trim()) {
      newErrors.visitorName = '请输入来访者姓名';
    }

    if (!formData.date) {
      newErrors.date = '请选择预约日期';
    }

    if (!formData.startTime) {
      newErrors.startTime = '请选择开始时间';
    }

    // 验证时长
    if (formData.duration === 'custom') {
      if (!formData.customDuration || parseInt(formData.customDuration) < 15) {
        newErrors.customDuration = '自定义时长不能少于15分钟';
      }
    } else if (!formData.duration || parseInt(formData.duration) < 15) {
      newErrors.duration = '请选择有效的预约时长';
    }

    if (!formData.type) {
      newErrors.type = '请选择预约类型';
    } else if (formData.type === '自定义') {
      if (!formData.customType.trim()) {
        newErrors.customType = '请输入自定义预约类型';
      }
    }

    // 验证咨询室
    if (!formData.room) {
      newErrors.room = '请选择咨询室';
    } else if (formData.room === 'custom') {
      if (!formData.customRoom.trim()) {
        newErrors.customRoom = '请输入自定义咨询室名称';
      }
    }

    if (!formData.therapistName.trim()) {
      newErrors.therapistName = '请输入咨询师姓名';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = '请输入咨询主题';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateEndTime = (): string => {
    const [hours, minutes] = formData.startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    
    // 获取实际时长
    const duration = formData.duration === 'custom' 
      ? parseInt(formData.customDuration || '60')
      : parseInt(formData.duration);
    
    const endMinutes = startMinutes + duration;
    const endHours = Math.floor(endMinutes / 60);
    const remainingMinutes = endMinutes % 60;
    return `${endHours.toString().padStart(2, '0')}:${remainingMinutes.toString().padStart(2, '0')}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const endTime = calculateEndTime();

      // 获取实际时长和咨询室
      const actualDuration = formData.duration === 'custom' 
        ? parseInt(formData.customDuration)
        : parseInt(formData.duration);
      
      const actualRoom = formData.room === 'custom'
        ? formData.customRoom.trim()
        : formData.room;

      const actualType = formData.type === '自定义'
        ? formData.customType.trim()
        : formData.type;

      const appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'> = {
        caseId: formData.caseId || undefined,
        visitorId: formData.visitorId || undefined,
        visitorName: formData.visitorName.trim(),
        visitorPhone: formData.visitorPhone.trim(),
        visitorAge: formData.visitorAge ? parseInt(formData.visitorAge) : undefined,
        visitorGender: formData.visitorGender || undefined,
        date: formData.date,
        startTime: formData.startTime,
        endTime,
        duration: actualDuration,
        type: actualType as AppointmentType,
        urgency: formData.urgency,
        status: '已确认',
        room: actualRoom,
        therapistId: 'default-therapist',
        therapistName: formData.therapistName.trim(),
        subject: formData.subject.trim(),
        description: formData.description.trim() || undefined,
        notes: formData.notes.trim() || undefined,
        reminderEnabled: formData.reminderEnabled,
        reminderTime: formData.reminderTime,
        isFirstSession: formData.isFirstSession,
        createdBy: 'current-user'
      };

      onSubmit(appointmentData);
      
      // 如果启用了提醒，设置提醒
      if (appointmentData.reminderEnabled) {
        const fullAppointment = {
          ...appointmentData,
          id: `temp_${Date.now()}`, // 临时ID，实际应用中应该用真实的ID
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        reminderService.setReminder(fullAppointment);
      }
    } catch (error) {
      console.error('创建预约失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="创建新预约"
      subtitle="请填写预约的详细信息"
      size="xl"
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      submitText="创建预约"
      cancelText="取消"
    >
      {/* 关联信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <FileText size={16} />
          关联信息
        </h4>
        
        <div className="form-grid form-grid-2">
          <div className="form-field">
            <Input
              label="搜索个案"
              placeholder="搜索个案（姓名、摘要、治疗方法）"
              value={caseSearchQuery}
              onChange={(e) => setCaseSearchQuery(e.target.value)}
              className="form-search"
            />
            <Select
              label="关联个案"
              value={formData.caseId}
              onChange={(e) => handleInputChange('caseId', e.target.value)}
              options={[
                { value: '', label: '不关联个案（一次性咨询）' },
                ...filteredCases.map(c => ({
                  value: c.id,
                  label: `${c.name} - ${c.summary}`
                }))
              ]}
            />
          </div>
          
          <div className="form-field">
            <Input
              label="搜索来访者"
              placeholder="搜索来访者（姓名、电话、性别）"
              value={visitorSearchQuery}
              onChange={(e) => setVisitorSearchQuery(e.target.value)}
              className="form-search"
            />
            <Select
              label="选择来访者"
              value={formData.visitorId}
              onChange={(e) => handleInputChange('visitorId', e.target.value)}
              options={[
                { value: '', label: '请选择来访者' },
                ...filteredVisitors
                  .filter(v => !formData.caseId || cases.find(c => c.id === formData.caseId)?.visitorId === v.id)
                  .map(v => ({
                    value: v.id,
                    label: `${v.name} (${v.phone})`
                  }))
              ]}
            />
          </div>
        </div>
      </div>

      {/* 来访者信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <User size={16} />
          来访者信息
        </h4>
        
        <div className="form-grid form-grid-2">
          <Input
            label="姓名"
            value={formData.visitorName}
            onChange={(e) => handleInputChange('visitorName', e.target.value)}
            placeholder="请输入来访者姓名"
            required
            error={errors.visitorName}
          />
          
          <Input
            label="手机号码"
            value={formData.visitorPhone}
            onChange={(e) => handleInputChange('visitorPhone', e.target.value)}
            placeholder="请输入手机号码"
          />
        </div>

        <div className="form-grid form-grid-2">
          <Input
            label="年龄"
            type="number"
            value={formData.visitorAge}
            onChange={(e) => handleInputChange('visitorAge', e.target.value)}
            placeholder="请输入年龄"
            min="1"
            max="120"
          />
          
          <Select
            label="性别"
            value={formData.visitorGender}
            onChange={(e) => handleInputChange('visitorGender', e.target.value)}
            options={[
              { value: '', label: '请选择性别' },
              { value: '男', label: '男' },
              { value: '女', label: '女' }
            ]}
          />
        </div>
      </div>

      {/* 预约信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Calendar size={16} />
          预约信息
        </h4>
        
        <div className="form-grid form-grid-3">
          <Input
            label="日期"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
            required
            error={errors.date}
            min={new Date().toISOString().split('T')[0]}
          />
          
          <Input
            label="开始时间"
            type="time"
            value={formData.startTime}
            onChange={(e) => handleInputChange('startTime', e.target.value)}
            required
            error={errors.startTime}
          />
          
          <Select
            label="时长（分钟）"
            value={formData.duration}
            onChange={(e) => handleInputChange('duration', e.target.value)}
            required
            error={errors.duration}
            options={presetDurations}
          />
        </div>
        
        {formData.duration === 'custom' && (
          <div className="form-grid form-grid-3">
            <div></div>
            <div></div>
            <Input
              placeholder="请输入自定义时长（分钟）"
              value={formData.customDuration}
              onChange={(e) => handleInputChange('customDuration', e.target.value)}
              type="number"
              min="1"
              max="480"
              error={errors.customDuration}
            />
          </div>
        )}

        <div className="form-grid form-grid-2">
          <div className="form-field">
            <Select
              label="预约类型"
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
              required
              error={errors.type}
              options={[
                { value: '', label: '请选择预约类型' },
                { value: '个体咨询', label: '个体咨询' },
                { value: '团体咨询', label: '团体咨询' },
                { value: '家庭咨询', label: '家庭咨询' },
                { value: '沙盘疗法', label: '沙盘疗法' },
                { value: '评估', label: '评估' },
                { value: '督导', label: '督导' },
                { value: '自定义', label: '自定义' }
              ]}
            />
            {formData.type === '自定义' && (
              <Input
                placeholder="请输入自定义预约类型"
                value={formData.customType}
                onChange={(e) => handleInputChange('customType', e.target.value)}
                error={errors.customType}
                className="mt-2"
              />
            )}
          </div>
          
          <Select
            label="紧急程度"
            value={formData.urgency}
            onChange={(e) => handleInputChange('urgency', e.target.value)}
            options={[
              { value: '普通', label: '普通' },
              { value: '紧急', label: '紧急' },
              { value: '危机干预', label: '危机干预' }
            ]}
          />
        </div>
      </div>

      {/* 咨询详情 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <MapPin size={16} />
          咨询详情
        </h4>
        
        <div className="form-grid form-grid-2">
          <div className="form-field">
            <Select
              label="咨询室"
              value={formData.room}
              onChange={(e) => handleInputChange('room', e.target.value)}
              required
              error={errors.room}
              options={[
                { value: '', label: '请选择咨询室' },
                ...presetRooms.map(room => ({
                  value: room,
                  label: room
                })),
                { value: 'custom', label: '自定义咨询室' }
              ]}
            />
            {formData.room === 'custom' && (
              <Input
                placeholder="请输入自定义咨询室名称"
                value={formData.customRoom}
                onChange={(e) => handleInputChange('customRoom', e.target.value)}
                error={errors.customRoom}
                className="mt-2"
              />
            )}
          </div>
          
          <Input
            label="咨询师姓名"
            value={formData.therapistName}
            onChange={(e) => handleInputChange('therapistName', e.target.value)}
            placeholder="请输入咨询师姓名"
            required
            error={errors.therapistName}
          />
        </div>

        <div className="form-grid form-grid-1">
          <Input
            label="主题"
            value={formData.subject}
            onChange={(e) => handleInputChange('subject', e.target.value)}
            placeholder="请输入咨询主题"
            required
            error={errors.subject}
          />
        </div>

        <div className="form-grid form-grid-1">
          <Textarea
            label="详细描述"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="请详细描述咨询的具体内容或问题"
            rows={3}
          />
        </div>

        <div className="form-grid form-grid-1">
          <Textarea
            label="备注"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="其他需要说明的信息"
            rows={3}
          />
        </div>
      </div>

      {/* 设置选项 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Clock size={16} />
          设置选项
        </h4>
        
        <div className="form-grid form-grid-1">
          <div className="form-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={formData.reminderEnabled}
                onChange={(e) => handleInputChange('reminderEnabled', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用提醒
            </label>
          </div>
        </div>

        {formData.reminderEnabled && (
          <div className="form-grid form-grid-2">
            <Select
              label="提醒时间"
              value={formData.reminderTime.toString()}
              onChange={(e) => handleInputChange('reminderTime', parseInt(e.target.value))}
              options={[
                { value: '5', label: '5分钟前' },
                { value: '10', label: '10分钟前' },
                { value: '15', label: '15分钟前' },
                { value: '30', label: '30分钟前' },
                { value: '60', label: '1小时前' },
                { value: '120', label: '2小时前' },
                { value: '1440', label: '1天前' }
              ]}
            />
          </div>
        )}
        
        <div className="form-grid form-grid-1">
          <div className="form-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={formData.isFirstSession}
                onChange={(e) => handleInputChange('isFirstSession', e.target.checked)}
              />
              <span className="checkmark"></span>
              首次咨询
            </label>
          </div>
        </div>
      </div>
    </FormModal>
  );
};

export default CreateAppointmentModal;
