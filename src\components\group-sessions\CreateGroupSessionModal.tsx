import React, { useState } from 'react';
import {
  FormModal,
  Input,
  Select,
  Textarea
} from '../ui/index';
import type { GroupSession } from '../../types/groupSession';

interface CreateGroupSessionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (session: GroupSession) => void;
}

interface CreateGroupSessionForm {
  title: string;
  description: string;
  sessionType: string;
  targetAge: string;
  maxParticipants: string;
  duration: string;
  frequency: string;
  totalSessions: string;
  startDate: string;
  endDate: string;
  meetingTime: string;
  location: string;
  therapistName: string;
  requirements: string;
  selectedSandTools: string[];
  notes: string;
}

export const CreateGroupSessionModal: React.FC<CreateGroupSessionModalProps> = ({
  isOpen,
  onClose,
  onSubmit
}) => {
  const [formData, setFormData] = useState<CreateGroupSessionForm>({
    title: '',
    description: '',
    sessionType: '',
    targetAge: '',
    maxParticipants: '',
    duration: '',
    frequency: '',
    totalSessions: '',
    startDate: '',
    endDate: '',
    meetingTime: '',
    location: '',
    therapistName: '',
    requirements: '',
    selectedSandTools: [],
    notes: ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof CreateGroupSessionForm, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof CreateGroupSessionForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof CreateGroupSessionForm, string>> = {};

    if (!formData.title.trim()) {
      newErrors.title = '请输入团体名称';
    }
    if (!formData.sessionType) {
      newErrors.sessionType = '请选择团体类型';
    }
    if (!formData.targetAge) {
      newErrors.targetAge = '请选择目标年龄';
    }
    if (!formData.maxParticipants || parseInt(formData.maxParticipants) < 1) {
      newErrors.maxParticipants = '请输入有效的最大参与人数';
    }
    if (!formData.duration || parseInt(formData.duration) < 1) {
      newErrors.duration = '请输入有效的时长';
    }
    if (!formData.frequency) {
      newErrors.frequency = '请选择频率';
    }
    if (!formData.startDate) {
      newErrors.startDate = '请选择开始日期';
    }
    if (!formData.meetingTime) {
      newErrors.meetingTime = '请输入会议时间';
    }
    if (!formData.location.trim()) {
      newErrors.location = '请输入活动地点';
    }
    if (!formData.therapistName.trim()) {
      newErrors.therapistName = '请输入负责治疗师';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newSession: GroupSession = {
        id: Date.now().toString(),
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        therapistId: 'therapist-1', // 实际应用中应该从登录用户获取
        therapistName: formData.therapistName.trim(),
        maxParticipants: parseInt(formData.maxParticipants),
        currentParticipants: 0,
        participants: [],
        sessionType: formData.sessionType as GroupSession['sessionType'],
        targetAge: formData.targetAge as GroupSession['targetAge'],
        duration: parseInt(formData.duration),
        frequency: formData.frequency as GroupSession['frequency'],
        totalSessions: formData.totalSessions ? parseInt(formData.totalSessions) : undefined,
        currentSession: 0,
        startDate: formData.startDate,
        endDate: formData.endDate || undefined,
        meetingTime: formData.meetingTime,
        location: formData.location.trim(),
        status: '计划中',
        requirements: formData.requirements.trim() || undefined,
        notes: formData.notes.trim() || undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      onSubmit(newSession);
      onClose();

      // 重置表单
      setFormData({
        title: '',
        description: '',
        sessionType: '',
        targetAge: '',
        maxParticipants: '',
        duration: '',
        frequency: '',
        totalSessions: '',
        startDate: '',
        endDate: '',
        meetingTime: '',
        location: '',
        therapistName: '',
        requirements: '',
        selectedSandTools: [],
        notes: ''
      });
      setErrors({});
    } catch (error) {
      console.error('创建团体活动失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="创建团体活动"
      subtitle="请填写团体活动的详细信息"
      size="lg"
      onSubmit={handleSubmit}
      submitText="创建团体活动"
      isSubmitting={isSubmitting}
    >
      {/* 基本信息 */}
      <div className="form-section">
        <h4 className="form-section-title">基本信息</h4>
        
        <div className="form-grid-1">
          <Input
            label="团体名称"
            required
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            error={errors.title}
            placeholder="请输入团体名称"
          />
        </div>
        
        <div className="form-grid-1">
          <Textarea
            label="团体描述"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="请输入团体活动的详细描述"
            rows={3}
          />
        </div>
      </div>

      {/* 团体设置 */}
      <div className="form-section">
        <h4 className="form-section-title">团体设置</h4>
        
        <div className="form-grid-2">
          <Select
            label="团体类型"
            required
            value={formData.sessionType}
            onChange={(e) => handleInputChange('sessionType', e.target.value)}
            error={errors.sessionType}
            options={[
              { value: '', label: '请选择团体类型' },
              { value: '开放式团体', label: '开放式团体' },
              { value: '封闭式团体', label: '封闭式团体' },
              { value: '主题团体', label: '主题团体' },
              { value: '发展性团体', label: '发展性团体' }
            ]}
          />
          
          <Select
            label="目标年龄"
            required
            value={formData.targetAge}
            onChange={(e) => handleInputChange('targetAge', e.target.value)}
            error={errors.targetAge}
            options={[
              { value: '', label: '请选择目标年龄' },
              { value: '儿童', label: '儿童（6-12岁）' },
              { value: '青少年', label: '青少年（13-17岁）' },
              { value: '成人', label: '成人（18-65岁）' },
              { value: '老年', label: '老年（65岁以上）' },
              { value: '混合', label: '混合年龄段' }
            ]}
          />
        </div>

        <div className="form-grid-3">
          <Input
            label="最大参与人数"
            type="number"
            required
            value={formData.maxParticipants}
            onChange={(e) => handleInputChange('maxParticipants', e.target.value)}
            error={errors.maxParticipants}
            placeholder="请输入人数"
            min="1"
            max="50"
          />
          
          <Input
            label="单次时长（分钟）"
            type="number"
            required
            value={formData.duration}
            onChange={(e) => handleInputChange('duration', e.target.value)}
            error={errors.duration}
            placeholder="请输入时长"
            min="15"
            max="300"
          />
          
          <Select
            label="活动频率"
            required
            value={formData.frequency}
            onChange={(e) => handleInputChange('frequency', e.target.value)}
            error={errors.frequency}
            options={[
              { value: '', label: '请选择频率' },
              { value: '单次', label: '单次活动' },
              { value: '每周', label: '每周一次' },
              { value: '每两周', label: '每两周一次' },
              { value: '每月', label: '每月一次' }
            ]}
          />
        </div>
      </div>

      {/* 时间安排 */}
      <div className="form-section">
        <h4 className="form-section-title">时间安排</h4>
        
        <div className="form-grid-3">
          <Input
            label="开始日期"
            type="date"
            required
            value={formData.startDate}
            onChange={(e) => handleInputChange('startDate', e.target.value)}
            error={errors.startDate}
            min={new Date().toISOString().split('T')[0]}
          />
          
          <Input
            label="结束日期"
            type="date"
            value={formData.endDate}
            onChange={(e) => handleInputChange('endDate', e.target.value)}
            placeholder="可选"
          />
          
          <Input
            label="会议时间"
            type="time"
            required
            value={formData.meetingTime}
            onChange={(e) => handleInputChange('meetingTime', e.target.value)}
            error={errors.meetingTime}
          />
        </div>

        <div className="form-grid-2">
          <Input
            label="活动地点"
            required
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            error={errors.location}
            placeholder="如：团体咨询室A"
          />
          
          <Input
            label="总场次"
            type="number"
            value={formData.totalSessions}
            onChange={(e) => handleInputChange('totalSessions', e.target.value)}
            placeholder="可选，留空为持续进行"
            min="1"
            max="100"
          />
        </div>
      </div>

      {/* 专业信息 */}
      <div className="form-section">
        <h4 className="form-section-title">专业信息</h4>
        
        <div className="form-grid-1">
          <Input
            label="负责治疗师"
            required
            value={formData.therapistName}
            onChange={(e) => handleInputChange('therapistName', e.target.value)}
            error={errors.therapistName}
            placeholder="请输入负责治疗师姓名"
          />
        </div>

        <div className="form-grid-1">
          <Textarea
            label="参与要求"
            value={formData.requirements}
            onChange={(e) => handleInputChange('requirements', e.target.value)}
            placeholder="请描述参与团体的条件和要求"
            rows={3}
          />
        </div>

        <div className="form-grid-1">
          <Textarea
            label="备注说明"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="其他需要说明的信息"
            rows={2}
          />
        </div>
      </div>
    </FormModal>
  );
};
