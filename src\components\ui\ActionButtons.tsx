import React from 'react';
import './ActionButtons.css';

interface ActionButtonsProps {
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  compact?: boolean;
  viewLabel?: string;
  editLabel?: string;
  deleteLabel?: string;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  onView,
  onEdit,
  onDelete,
  compact = false,
  viewLabel = '查看',
  editLabel = '编辑',
  deleteLabel = '删除'
}) => {
  return (
    <div className={`action-buttons-container ${compact ? 'compact' : ''}`}>
      {onView && (
        <button
          className="action-btn view-btn"
          onClick={onView}
          title={viewLabel}
        >
          <span>{viewLabel}</span>
        </button>
      )}
      {onEdit && (
        <button
          className="action-btn edit-btn"
          onClick={onEdit}
          title={editLabel}
        >
          <span>{editLabel}</span>
        </button>
      )}
      {onDelete && (
        <button
          className="action-btn delete-btn"
          onClick={onDelete}
          title={deleteLabel}
        >
          <span>{deleteLabel}</span>
        </button>
      )}
    </div>
  );
};
