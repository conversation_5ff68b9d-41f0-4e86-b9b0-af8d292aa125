import React from 'react';
import './LoadingScreen.css';

interface LoadingScreenProps {
  title?: string;
  subtitle?: string;
  progress?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  title = '正在初始化系统...',
  subtitle = '首次使用需要加载基础数据，请稍候',
  progress
}) => {
  return (
    <div className="loading-screen">
      <div className="loading-background">
        <div className="loading-circles">
          <div className="circle circle-1"></div>
          <div className="circle circle-2"></div>
          <div className="circle circle-3"></div>
        </div>
      </div>
      
      <div className="loading-content">
        <div className="loading-logo">
          <div className="logo-icon">
            {/* 使用公共目录中的 PNG 图标替换原先的 SVG */}
            <img
              src="/logo.png"
              alt="沙盘管理系统 Logo"
              style={{ width: 64, height: 64, borderRadius: 16, objectFit: 'cover', display: 'block', margin: '0 auto' }}
            />
          </div>
          <h1 className="loading-title">沙盘管理系统</h1>
        </div>
        
        <div className="loading-info">
          <h2 className="loading-main-text">{title}</h2>
          <p className="loading-sub-text">{subtitle}</p>
        </div>
        
        <div className="loading-spinner-container">
          <div className="loading-spinner">
            <div className="spinner-ring"></div>
            <div className="spinner-ring"></div>
            <div className="spinner-ring"></div>
          </div>
        </div>
        
        {progress !== undefined && (
          <div className="loading-progress">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              ></div>
            </div>
            <span className="progress-text">{Math.round(progress)}%</span>
          </div>
        )}
        
        <div className="loading-dots">
          <span className="dot"></span>
          <span className="dot"></span>
          <span className="dot"></span>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;