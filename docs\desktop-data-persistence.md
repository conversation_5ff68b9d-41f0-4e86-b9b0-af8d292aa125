# 桌面端数据持久化说明

## 概述

本应用是基于Electron的桌面端应用，采用SQLite数据库进行本地数据存储。

## 数据存储位置

### Windows
```
%APPDATA%\沙盘管理系统\xlsp.db
```

### macOS
```
~/Library/Application Support/沙盘管理系统/xlsp.db
```

### Linux
```
~/.config/沙盘管理系统/xlsp.db
```

## 数据架构

### 自适应数据管理器 (adaptiveDataManager)

系统使用`adaptiveDataManager`自动检测运行环境：

```typescript
// 环境检测
isElectronEnvironment(): boolean {
  return typeof window !== 'undefined' && window.electronAPI?.isElectron;
}
```

- **桌面端**: 使用 `electronDataManager` + SQLite
- **Web端**: 使用 `dataManager` + IndexedDB (开发/备用)

### 数据表结构

#### 来访者表 (visitors)
```sql
CREATE TABLE visitors (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  gender TEXT,
  age INTEGER,
  phone TEXT UNIQUE,
  email TEXT,
  emergency_contact TEXT,
  emergency_phone TEXT,
  occupation TEXT,
  education TEXT,
  address TEXT,
  notes TEXT,
  status TEXT DEFAULT '活跃',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

#### 个案表 (cases)
```sql
CREATE TABLE cases (
  id TEXT PRIMARY KEY,
  visitor_id TEXT,
  name TEXT NOT NULL,
  summary TEXT,
  therapy_method TEXT DEFAULT '箱庭疗法',
  selected_sand_tools TEXT,
  last_date TEXT,
  next_date TEXT,
  total INTEGER DEFAULT 0,
  star BOOLEAN DEFAULT FALSE,
  duration INTEGER DEFAULT 50,
  crisis TEXT CHECK(crisis IN ('⚠️', '⚡', '✅')),
  homework TEXT CHECK(homework IN ('📋', '✅', '❌')),
  progress TEXT CHECK(progress IN ('⬆️', '➡️', '⬇️')),
  keywords TEXT,
  supervision TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (visitor_id) REFERENCES visitors (id)
);
```

## 数据迁移

系统支持自动数据库升级：

```javascript
// 检查并添加新字段
const caseColumns = db.prepare("PRAGMA table_info(cases)").all();
const hasTherapyMethod = caseColumns.some(col => col.name === 'therapy_method');
if (!hasTherapyMethod) {
  db.exec('ALTER TABLE cases ADD COLUMN therapy_method TEXT DEFAULT "箱庭疗法"');
}
```

## API使用说明

### 个案管理示例

```typescript
import { caseService } from '../services';

// 创建个案
const newCase = await caseService.createCase({
  visitorId: 'visitor-123',
  name: '张三',
  summary: '学习焦虑问题',
  therapyMethod: '认知行为疗法',
  selectedSandTools: ['tool-1', 'tool-2'],
  crisis: '✅',
  homework: '📋',
  progress: '⬆️'
});

// 更新个案
await caseService.updateCase(caseId, {
  progress: '➡️',
  supervision: '治疗进展良好'
});
```

### 数据验证

系统会自动验证：
- 来访者ID的有效性
- 数据类型的正确性
- 必填字段的完整性

## 错误处理

常见问题及解决方案：

### 1. 数据保存失败
```typescript
// 检查环境
console.log(adaptiveDataManager.getEnvironmentInfo());
// 输出: { isElectron: true, platform: 'win32', storageType: 'SQLite' }
```

### 2. 字段映射错误
确保数据库字段与TypeScript类型定义一致：
```typescript
interface SimpleCase {
  id: string;
  visitorId: string;    // 对应数据库 visitor_id
  therapyMethod: string; // 对应数据库 therapy_method
  selectedSandTools: string[]; // 对应数据库 selected_sand_tools (JSON)
}
```

### 3. 环境检测失败
检查preload.js是否正确暴露API：
```javascript
contextBridge.exposeInMainWorld('electronAPI', {
  isElectron: true,
  platform: process.platform,
  dbQuery: (sql, params) => ipcRenderer.invoke('db-query', sql, params),
  dbRun: (sql, params) => ipcRenderer.invoke('db-run', sql, params)
});
```

## 数据备份

桌面端支持数据导出功能：
- 菜单 → 文件 → 导出数据
- 导出格式：JSON
- 包含所有来访者、个案、团体会话数据

## 开发调试

### 查看数据库内容
```javascript
// 在DevTools控制台中
console.log(await window.electronAPI.dbQuery('SELECT * FROM cases'));
```

### 环境信息
```javascript
console.log(window.electronAPI?.isElectron); // true (桌面端)
console.log(window.electronAPI?.platform);   // 'win32', 'darwin', 'linux'
```

## 注意事项

1. **数据安全**: 所有数据存储在本地，无网络传输
2. **备份重要**: 建议定期使用导出功能备份数据
3. **版本兼容**: 数据库结构支持自动升级
4. **性能优化**: SQLite支持事务和索引，适合大量数据
5. **开发环境**: 开发时如需重置数据库，删除用户数据目录中的xlsp.db文件

## 故障排除

### 问题：数据保存后丢失
**原因**: 可能在Web环境中运行，数据保存到了临时的IndexedDB
**解决**: 确保在Electron环境中运行应用

### 问题：字段无法保存
**原因**: 数据库表结构缺少对应字段
**解决**: 检查数据库迁移逻辑，或手动删除数据库文件重新创建

### 问题：外键约束错误
**原因**: 尝试保存不存在的来访者ID的个案
**解决**: 先创建来访者，再创建个案，或检查visitorId的有效性
