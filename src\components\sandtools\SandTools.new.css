/* 沙具管理页面样式 - 重新设计 */

/* 页面容器 */
.sandtools-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-light);
}

.page-header-content h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.page-header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 12px;
  font-size: 24px;
  flex-shrink: 0;
}

.stat-icon.primary {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-blue);
}

.stat-icon.success {
  background-color: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.stat-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.stat-icon.info {
  background-color: rgba(14, 165, 233, 0.1);
  color: #0ea5e9;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

/* 搜索和筛选区域 */
.filters-section {
  padding: 24px;
  margin-bottom: 24px;
}

.search-filters-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
  max-width: 500px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 2px solid var(--border-light);
  border-radius: 12px;
  font-size: 16px;
  background-color: var(--bg-primary);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
  z-index: 1;
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 10px 16px;
  border: 2px solid var(--border-light);
  border-radius: 8px;
  background-color: var(--bg-primary);
  font-size: 14px;
  min-width: 140px;
  transition: border-color 0.2s ease;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkbox-filter {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.checkbox-filter:hover {
  background-color: var(--bg-secondary);
}

.checkbox-filter input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
  width: 16px;
  height: 16px;
}

/* 表格容器 */
.table-wrapper {
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

/* 缩略图样式 */
.tool-thumbnail {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--bg-secondary);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
}

.thumbnail-icon {
  font-size: 24px;
}

/* 沙具信息 */
.tool-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 0;
}

.tool-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.tool-name-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.tool-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
  flex-wrap: wrap;
}

.care-indicator,
.replacement-indicator {
  font-size: 16px;
  flex-shrink: 0;
}

.category-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.subcategory {
  font-size: 12px;
  color: var(--text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stock-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.stock-numbers {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  font-size: 16px;
}

.location-info {
  font-size: 14px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.usage-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
}

.usage-count {
  font-size: 12px;
  color: var(--text-tertiary);
  margin: 0;
}

.never-used {
  color: var(--text-tertiary);
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

/* 网格视图 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.tool-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.tool-card-content {
  padding: 20px;
}

.tool-card-image {
  position: relative;
  width: 100%;
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--bg-secondary);
  margin-bottom: 16px;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  color: var(--text-tertiary);
}

.card-icon {
  font-size: 48px;
}

.tool-card-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tool-card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tool-card-category {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.tool-card-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--text-secondary);
}

.tool-card-actions {
  display: flex;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid var(--border-light);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-state-icon {
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.empty-state-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.empty-state-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0 0 24px 0;
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .sandtools-container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }
  
  .page-header-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .search-filters-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .search-box {
    min-width: auto;
    max-width: none;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .filter-select {
    min-width: auto;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .tool-card-image {
    height: 160px;
  }
  
  .table-wrapper {
    font-size: 14px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .sandtools-container {
    padding: 12px;
  }
  
  .page-header {
    margin-bottom: 24px;
  }
  
  .stats-grid {
    margin-bottom: 24px;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }
  
  .filters-section {
    padding: 16px;
  }
  
  .search-input {
    font-size: 16px;
    padding: 10px 14px 10px 40px;
  }
  
  .tool-card-image {
    height: 140px;
  }
  
  .tool-card-content {
    padding: 16px;
  }
  
  .thumbnail-icon {
    font-size: 20px;
  }
  
  .tool-thumbnail {
    width: 50px;
    height: 50px;
  }
}
