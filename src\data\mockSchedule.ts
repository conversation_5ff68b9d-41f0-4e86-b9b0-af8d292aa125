// 日程安排模拟数据
import type { Appointment, Room, ScheduleStats, QuickBookingTemplate } from '../types/schedule';

// 模拟预约数据
export const mockAppointments: Appointment[] = [
  {
    id: 'apt-1',
    visitorId: 'v1',
    visitorName: '张小明',
    visitorPhone: '138****1234',
    visitorAge: 25,
    visitorGender: '男',
    date: '2024-08-29',
    startTime: '09:00',
    endTime: '09:50',
    duration: 50,
    type: '个体咨询',
    status: '已确认',
    urgency: '普通',
    room: '咨询室A',
    therapistId: 'therapist-1',
    therapistName: '李心理师',
    subject: '焦虑情绪调节',
    description: '工作压力导致的焦虑症状，需要情绪调节和压力管理技巧指导',
    notes: '第3次咨询，来访者配合度良好',
    reminderEnabled: true,
    reminderTime: 30,
    isFirstSession: false,
    sessionNumber: 3,
    totalPlannedSessions: 8,
    fee: 300,
    paymentStatus: '已支付',
    createdAt: '2024-08-25T10:00:00Z',
    updatedAt: '2024-08-25T10:00:00Z',
    createdBy: 'admin'
  },
  {
    id: 'apt-2',
    visitorId: 'v2',
    visitorName: '王小红',
    visitorPhone: '139****5678',
    visitorAge: 32,
    visitorGender: '女',
    date: '2024-08-29',
    startTime: '10:30',
    endTime: '11:20',
    duration: 50,
    type: '沙盘疗法',
    status: '已确认',
    urgency: '普通',
    room: '沙盘室1',
    therapistId: 'therapist-1',
    therapistName: '李心理师',
    subject: '童年创伤处理',
    description: '通过沙盘疗法处理童年创伤记忆，帮助情绪释放',
    notes: '需要准备儿童主题沙具',
    reminderEnabled: true,
    reminderTime: 60,
    isFirstSession: true,
    sessionNumber: 1,
    totalPlannedSessions: 12,
    fee: 400,
    paymentStatus: '已支付',
    createdAt: '2024-08-26T14:30:00Z',
    updatedAt: '2024-08-26T14:30:00Z',
    createdBy: 'admin'
  },
  {
    id: 'apt-3',
    visitorName: '陈家庭',
    visitorPhone: '137****9012',
    date: '2024-08-29',
    startTime: '14:00',
    endTime: '15:30',
    duration: 90,
    type: '家庭咨询',
    status: '已确认',
    urgency: '普通',
    room: '家庭咨询室',
    therapistId: 'therapist-2',
    therapistName: '张心理师',
    subject: '亲子关系改善',
    description: '父母与青春期孩子沟通困难，需要家庭系统治疗',
    notes: '父母双方和孩子一起参与',
    reminderEnabled: true,
    reminderTime: 30,
    isFirstSession: false,
    sessionNumber: 5,
    totalPlannedSessions: 10,
    fee: 500,
    paymentStatus: '已支付',
    createdAt: '2024-08-27T09:15:00Z',
    updatedAt: '2024-08-27T09:15:00Z',
    createdBy: 'admin'
  },
  {
    id: 'apt-4',
    visitorName: '李小强',
    visitorPhone: '136****3456',
    visitorAge: 28,
    visitorGender: '男',
    date: '2024-08-29',
    startTime: '16:00',
    endTime: '16:50',
    duration: 50,
    type: '个体咨询',
    status: '待确认',
    urgency: '紧急',
    room: '咨询室B',
    therapistId: 'therapist-1',
    therapistName: '李心理师',
    subject: '抑郁情绪评估',
    description: '近期情绪低落严重，需要抑郁症状评估和危机干预',
    notes: '紧急预约，需要重点关注',
    reminderEnabled: true,
    reminderTime: 15,
    isFirstSession: true,
    sessionNumber: 1,
    fee: 300,
    paymentStatus: '未支付',
    createdAt: '2024-08-28T16:45:00Z',
    updatedAt: '2024-08-28T16:45:00Z',
    createdBy: 'admin'
  },
  {
    id: 'apt-5',
    visitorName: '情绪管理团体',
    date: '2024-08-30',
    startTime: '09:30',
    endTime: '11:00',
    duration: 90,
    type: '团体咨询',
    status: '已确认',
    urgency: '普通',
    room: '团体咨询室A',
    therapistId: 'therapist-2',
    therapistName: '张心理师',
    subject: '情绪管理技能训练',
    description: '8人小组，情绪调节技能训练，第6次团体活动',
    notes: '需要准备情绪卡片和练习材料',
    reminderEnabled: true,
    reminderTime: 45,
    isFirstSession: false,
    sessionNumber: 6,
    totalPlannedSessions: 12,
    fee: 150,
    paymentStatus: '已支付',
    createdAt: '2024-08-20T11:00:00Z',
    updatedAt: '2024-08-20T11:00:00Z',
    createdBy: 'admin'
  },
  {
    id: 'apt-6',
    visitorName: '刘小芳',
    visitorPhone: '135****7890',
    visitorAge: 35,
    visitorGender: '女',
    date: '2024-08-30',
    startTime: '15:00',
    endTime: '15:50',
    duration: 50,
    type: '督导',
    status: '已确认',
    urgency: '普通',
    room: '督导室',
    therapistId: 'therapist-3',
    therapistName: '王督导师',
    subject: '个案督导',
    description: '针对复杂个案的督导讨论，包括治疗方案调整',
    notes: '实习咨询师督导',
    reminderEnabled: true,
    reminderTime: 30,
    isFirstSession: false,
    fee: 200,
    paymentStatus: '已支付',
    createdAt: '2024-08-25T13:20:00Z',
    updatedAt: '2024-08-25T13:20:00Z',
    createdBy: 'admin'
  }
];

// 模拟咨询室数据
export const mockRooms: Room[] = [
  {
    id: 'room-1',
    name: '咨询室A',
    capacity: 2,
    type: '个体咨询室',
    equipment: ['沙发', '茶几', '纸巾盒', '录音设备', '空气净化器'],
    available: true,
    notes: '安静舒适，适合个体咨询'
  },
  {
    id: 'room-2',
    name: '咨询室B',
    capacity: 2,
    type: '个体咨询室',
    equipment: ['沙发', '茶几', '纸巾盒', '录音设备', '绿植'],
    available: true,
    notes: '采光良好，温馨环境'
  },
  {
    id: 'room-3',
    name: '沙盘室1',
    capacity: 3,
    type: '沙盘室',
    equipment: ['沙盘', '沙具柜', '沙子', '摄像设备', '舒适座椅'],
    available: true,
    notes: '专业沙盘治疗室，沙具齐全'
  },
  {
    id: 'room-4',
    name: '沙盘室2',
    capacity: 4,
    type: '沙盘室',
    equipment: ['大型沙盘', '丰富沙具', '摄像设备', '沙具分类柜'],
    available: true,
    notes: '可进行团体沙盘治疗'
  },
  {
    id: 'room-5',
    name: '家庭咨询室',
    capacity: 6,
    type: '家庭咨询室',
    equipment: ['圆桌', '家庭椅', '儿童玩具', '白板', '投影仪'],
    available: true,
    notes: '适合家庭治疗和亲子咨询'
  },
  {
    id: 'room-6',
    name: '团体咨询室A',
    capacity: 12,
    type: '团体咨询室',
    equipment: ['圆形座椅', '移动白板', '投影设备', '音响系统'],
    available: true,
    notes: '适合小组活动和团体治疗'
  },
  {
    id: 'room-7',
    name: '督导室',
    capacity: 4,
    type: '个体咨询室',
    equipment: ['会议桌', '舒适椅子', '投影仪', '案例资料柜'],
    available: true,
    notes: '用于督导和案例讨论'
  }
];

// 模拟统计数据
export const mockScheduleStats: ScheduleStats = {
  today: {
    total: 8,
    completed: 3,
    cancelled: 1,
    noShow: 0
  },
  thisWeek: {
    total: 42,
    completed: 28,
    utilizationRate: 85
  },
  thisMonth: {
    total: 168,
    revenue: 50400,
    newClients: 23
  }
};

// 快速预约模板
export const mockQuickTemplates: QuickBookingTemplate[] = [
  {
    id: 'template-1',
    name: '个体咨询（50分钟）',
    type: '个体咨询',
    duration: 50,
    description: '标准个体心理咨询',
    defaultRoom: '咨询室A',
    isDefault: true
  },
  {
    id: 'template-2',
    name: '沙盘疗法（50分钟）',
    type: '沙盘疗法',
    duration: 50,
    description: '个体沙盘治疗',
    defaultRoom: '沙盘室1',
    isDefault: false
  },
  {
    id: 'template-3',
    name: '家庭咨询（90分钟）',
    type: '家庭咨询',
    duration: 90,
    description: '家庭系统治疗',
    defaultRoom: '家庭咨询室',
    isDefault: false
  },
  {
    id: 'template-4',
    name: '团体咨询（90分钟）',
    type: '团体咨询',
    duration: 90,
    description: '小组团体治疗',
    defaultRoom: '团体咨询室A',
    isDefault: false
  },
  {
    id: 'template-5',
    name: '心理评估（80分钟）',
    type: '评估',
    duration: 80,
    description: '心理健康评估',
    defaultRoom: '咨询室B',
    isDefault: false
  }
];

// 工具函数：获取指定日期的预约
export function getAppointmentsByDate(date: string): Appointment[] {
  return mockAppointments
    .filter(apt => apt.date === date)
    .sort((a, b) => a.startTime.localeCompare(b.startTime));
}

// 工具函数：获取指定日期范围的预约
export function getAppointmentsByDateRange(startDate: string, endDate: string): Appointment[] {
  return mockAppointments
    .filter(apt => apt.date >= startDate && apt.date <= endDate)
    .sort((a, b) => {
      if (a.date === b.date) {
        return a.startTime.localeCompare(b.startTime);
      }
      return a.date.localeCompare(b.date);
    });
}

// 工具函数：检查时间冲突
export function checkTimeConflict(
  date: string, 
  startTime: string, 
  endTime: string, 
  therapistId: string,
  excludeId?: string
): boolean {
  const dayAppointments = getAppointmentsByDate(date)
    .filter(apt => apt.therapistId === therapistId && apt.id !== excludeId);

  return dayAppointments.some(apt => {
    return (startTime < apt.endTime && endTime > apt.startTime);
  });
}

// 工具函数：获取可用时间段
export function getAvailableTimeSlots(
  date: string, 
  therapistId: string, 
  duration: number = 50
): { start: string; end: string }[] {
  const workStart = '08:00';
  const workEnd = '18:00';
  const slots: { start: string; end: string }[] = [];
  
  // 获取当天已有预约
  const appointments = getAppointmentsByDate(date)
    .filter(apt => apt.therapistId === therapistId)
    .sort((a, b) => a.startTime.localeCompare(b.startTime));

  let currentTime = workStart;
  
  for (const apt of appointments) {
    // 检查当前时间到预约开始时间是否有足够空隙
    const gap = getTimeDiffInMinutes(currentTime, apt.startTime);
    if (gap >= duration) {
      slots.push({
        start: currentTime,
        end: addMinutesToTime(currentTime, duration)
      });
    }
    currentTime = apt.endTime;
  }
  
  // 检查最后一个预约后到下班时间
  const finalGap = getTimeDiffInMinutes(currentTime, workEnd);
  if (finalGap >= duration) {
    slots.push({
      start: currentTime,
      end: addMinutesToTime(currentTime, duration)
    });
  }
  
  return slots;
}

// 辅助函数：计算时间差（分钟）
function getTimeDiffInMinutes(startTime: string, endTime: string): number {
  const start = new Date(`2024-01-01T${startTime}:00`);
  const end = new Date(`2024-01-01T${endTime}:00`);
  return (end.getTime() - start.getTime()) / (1000 * 60);
}

// 辅助函数：给时间增加分钟
function addMinutesToTime(time: string, minutes: number): string {
  const date = new Date(`2024-01-01T${time}:00`);
  date.setMinutes(date.getMinutes() + minutes);
  return date.toTimeString().slice(0, 5);
}

// 工具函数：获取统计信息
export function getScheduleStatistics(): ScheduleStats {
  return mockScheduleStats;
}

// 工具函数：获取今日预约概览
export function getTodayOverview() {
  // 为了演示，我们使用模拟的今天日期
  const today = '2024-08-29'; // 使用固定日期来匹配模拟数据
  const todayAppointments = getAppointmentsByDate(today);
  
  return {
    total: todayAppointments.length,
    confirmed: todayAppointments.filter(apt => apt.status === '已确认').length,
    pending: todayAppointments.filter(apt => apt.status === '待确认').length,
    urgent: todayAppointments.filter(apt => apt.urgency === '紧急' || apt.urgency === '危机干预').length,
    nextAppointment: todayAppointments.find(apt => 
      apt.startTime > '08:00' && // 使用固定时间来演示
      (apt.status === '已确认' || apt.status === '待确认')
    )
  };
}

// 工具函数：格式化时间显示
export function formatTimeRange(startTime: string, endTime: string): string {
  return `${startTime} - ${endTime}`;
}

// 工具函数：获取状态颜色
export function getStatusColor(status: string): string {
  const colors: Record<string, string> = {
    '待确认': '#f59e0b',
    '已确认': '#10b981',
    '进行中': '#3b82f6',
    '已完成': '#6b7280',
    '已取消': '#ef4444',
    '缺席': '#dc2626'
  };
  return colors[status] || '#6b7280';
}

// 工具函数：获取紧急程度颜色
export function getUrgencyColor(urgency: string): string {
  const colors: Record<string, string> = {
    '普通': '#10b981',
    '紧急': '#f59e0b',
    '危机干预': '#ef4444'
  };
  return colors[urgency] || '#10b981';
}
