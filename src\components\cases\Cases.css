/* 个案管理页面样式 - 统一风格（参考来访者管理） */

/* 统计概览样式 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
  border-radius: 8px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
  color: var(--primary-blue);
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 表格样式优化 */
.cases-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.cases-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #374151;
  padding: 12px 8px;
  border-bottom: 2px solid #e5e7eb;
  text-align: left;
}

.cases-table td {
  padding: 12px 8px;
  vertical-align: middle;
  border-bottom: 1px solid #f3f4f6;
}

.cases-table tr:hover {
  background-color: #f9fafb;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* 来访者信息样式 */
.visitor-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.visitor-name {
  font-weight: 500;
  color: #1f2937;
}

.star-icon {
  color: #f59e0b;
  fill: currentColor;
}

/* 状态徽章样式 */
.status-badge {
  font-weight: 600;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cases-table {
    font-size: 12px;
  }
  
  .cases-table th,
  .cases-table td {
    padding: 8px 4px;
  }
}

/* 专业主题色彩 */
:root {
  --crisis-high: #dc2626;
  --crisis-medium: #d97706;
  --crisis-low: #059669;
  --progress-improve: #059669;
  --progress-stable: #2563eb;
  --progress-decline: #dc2626;
  --professional-primary: #1e40af;
  --professional-secondary: #64748b;
}
