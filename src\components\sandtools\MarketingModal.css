/* 营销弹窗样式 - 简洁版 */
.marketing-modal {
  padding: 24px;
  text-align: center;
}

.marketing-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.marketing-header svg {
  color: #10b981;
  margin-bottom: 12px;
}

.marketing-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.marketing-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.marketing-text {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #4b5563;
}

.marketing-text strong {
  color: #10b981;
  font-weight: 600;
}

.marketing-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.contact-btn {
  background: #10b981;
  color: white;
  border: none;
}

.contact-btn:hover {
  background: #059669;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .marketing-modal {
    padding: 20px;
  }
  
  .marketing-buttons {
    flex-direction: column;
  }
  
  .marketing-buttons button {
    width: 100%;
  }
}
