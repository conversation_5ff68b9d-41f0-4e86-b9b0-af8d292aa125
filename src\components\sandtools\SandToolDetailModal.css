/* 沙具详情模态框样式 */

.sand-tool-detail-content {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* 主要信息区域 */
.detail-main-info {
  padding-bottom: 24px;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

/* 信息区块 */
.info-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.info-section.full-width {
  grid-column: 1 / -1;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.section-icon {
  color: #3b82f6;
  flex-shrink: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* 信息项 */
.info-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 24px;
}

.info-label {
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  min-width: 80px;
}

.info-value {
  font-size: 13px;
  color: #1e293b;
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 12px;
}

/* 次要信息区域 */
.detail-secondary-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 16px;
}

.detail-secondary-info .info-section.full-width {
  grid-column: 1 / -1;
}

/* 描述内容 */
.description-content {
  font-size: 14px;
  color: #475569;
  line-height: 1.5;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

/* 标签列表 */
.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* 特殊标记 */
.special-marks {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 图片容器 */
.tool-image-container {
  display: flex;
  justify-content: center;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.tool-image {
  max-width: 200px;
  max-height: 200px;
  width: auto;
  height: auto;
  border-radius: 4px;
  object-fit: cover;
}

/* 备注内容 */
.notes-content {
  font-size: 14px;
  color: #475569;
  line-height: 1.5;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
  border-top: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
}

/* 操作按钮区域 */
.modal-actions {
  border-top: 1px solid #e2e8f0;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  background: #f8fafc;
  margin: 24px -24px -24px -24px;
  border-radius: 0 0 8px 8px;
}

.actions-left {
  display: flex;
  gap: 8px;
}

.actions-right {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .detail-secondary-info {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-value {
    text-align: left;
    margin-left: 0;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
    gap: 12px;
  }
  
  .actions-left,
  .actions-right {
    width: 100%;
    justify-content: center;
  }
}

/* 滚动条样式 */
.sand-tool-detail-content::-webkit-scrollbar {
  width: 6px;
}

.sand-tool-detail-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.sand-tool-detail-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.sand-tool-detail-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
