import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { Button } from '../ui/Button';
import { AlertCircle } from 'lucide-react';
import { SimpleCase } from '../../types/case';

interface EditCaseModalProps {
  isOpen: boolean;
  case: SimpleCase;
  onClose: () => void;
  onUpdate: (updatedCase: SimpleCase) => void;
}

const EditCaseModal: React.FC<EditCaseModalProps> = ({ 
  isOpen, 
  onClose 
}) => {

  return (
    <BaseModal isOpen={isOpen} onClose={onClose}>
      <div className="edit-case-modal-placeholder">
        <div className="placeholder-content">
          <AlertCircle size={48} className="placeholder-icon" />
          <h2>编辑个案功能</h2>
          <p>此功能正在开发中，敬请期待</p>
          <Button onClick={onClose} variant="primary">
            关闭
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export default EditCaseModal;
