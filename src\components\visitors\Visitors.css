/* 来访者页面样式 */
.visitor-filters {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 批量操作工具栏样式 */
.batch-toolbar {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 12px 16px;
}

.batch-toolbar .text-sm {
  font-size: 14px;
  color: #475569;
  font-weight: 500;
}

.filter-row {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-end;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 300px;
}

.search-input .form-group {
  margin-bottom: 0;
}

.filter-controls {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  align-items: flex-end;
}

.filter-controls .form-group {
  margin-bottom: 0;
  min-width: 120px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.stat-number {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--primary-blue);
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.visitor-name .name {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.visitor-name .email {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* 地址列样式 */
.address-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  cursor: help;
  transition: all 0.2s ease;
}

.address-cell:hover {
  color: var(--text-primary);
  background-color: #f8fafc;
  padding: 2px 4px;
  border-radius: 4px;
}

/* 表格列宽设置 */
.visitors-table {
  table-layout: fixed;
}

.visitors-table th:nth-child(1),
.visitors-table td:nth-child(1) {
  width: 50px; /* 选择框列 */
}

.visitors-table th:nth-child(2),
.visitors-table td:nth-child(2) {
  width: 120px; /* 姓名列 */
}

.visitors-table th:nth-child(3),
.visitors-table td:nth-child(3) {
  width: 80px; /* 性别列 */
}

.visitors-table th:nth-child(4),
.visitors-table td:nth-child(4) {
  width: 80px; /* 年龄列 */
}

.visitors-table th:nth-child(5),
.visitors-table td:nth-child(5) {
  width: 150px; /* 职业列 */
}

.visitors-table th:nth-child(6),
.visitors-table td:nth-child(6) {
  width: 200px; /* 地址列 */
}

.visitors-table th:nth-child(7),
.visitors-table td:nth-child(7) {
  width: 100px; /* 状态列 */
}

.visitors-table th:nth-child(8),
.visitors-table td:nth-child(8) {
  width: 120px; /* 操作列 */
}

/* 状态列表头居中 - 访者管理特定列 */
.visitors-table th:nth-child(7) {
  text-align: center;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.contact-info .phone,
.contact-info .email {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 联系信息样式 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.contact-info .phone,
.contact-info .email {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .filter-controls {
    justify-content: space-between;
  }
  
  .filter-controls .form-group {
    flex: 1;
    min-width: auto;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
}
