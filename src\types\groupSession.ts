// 团体沙盘治疗相关类型定义

export interface GroupSession {
  id: string;
  title: string;
  description?: string;
  therapistId: string;
  therapistName: string;
  maxParticipants: number;
  currentParticipants: number;
  participants: GroupParticipant[];
  sessionType: '开放式团体' | '封闭式团体' | '主题团体' | '发展性团体';
  targetAge: '儿童' | '青少年' | '成人' | '老年' | '混合';
  duration: number; // 持续时间（分钟）
  frequency: '单次' | '每周' | '每两周' | '每月';
  totalSessions?: number; // 总次数（如果是系列活动）
  currentSession?: number; // 当前次数
  startDate: string;
  endDate?: string;
  meetingTime: string; // 会议时间 HH:mm
  location: string;
  status: '计划中' | '进行中' | '已完成' | '已取消' | '暂停';
  requirements?: string; // 参与要求
  materials?: string[]; // 所需材料
  selectedSandTools?: string[]; // 选择的沙具ID列表
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface GroupParticipant {
  id: string;
  visitorId: string;
  visitorName: string;
  age: number;
  gender: '男' | '女' | '其他';
  joinDate: string;
  status: '已报名' | '已确认' | '进行中' | '已完成' | '退出';
  attendanceRate?: number; // 出勤率
  notes?: string;
}

export interface SessionRecord {
  id: string;
  groupSessionId: string;
  sessionNumber: number;
  date: string;
  startTime: string;
  endTime: string;
  actualDuration: number;
  attendees: SessionAttendee[];
  activities: SessionActivity[];
  therapistNotes: string;
  groupDynamics?: string; // 团体动力学观察
  achievements?: string; // 本次成果
  challenges?: string; // 遇到的挑战
  nextSteps?: string; // 下次计划
  materials: string[];
  photos?: string[]; // 作品照片
  createdAt: string;
}

export interface SessionAttendee {
  participantId: string;
  visitorName: string;
  attended: boolean;
  arrivalTime?: string;
  departureTime?: string;
  participation: '积极' | '一般' | '被动' | '抗拒';
  mood: '愉快' | '平静' | '紧张' | '沮丧' | '愤怒' | '其他';
  interactions: '主动互动' | '被动参与' | '独立活动' | '需要引导';
  sandplayTheme?: string; // 沙盘主题
  symbolicElements?: string[]; // 象征性元素
  individualNotes?: string;
}

export interface SessionActivity {
  id: string;
  name: string;
  type: '暖场活动' | '主体活动' | '分享环节' | '结束活动';
  duration: number;
  description: string;
  objectives: string[]; // 活动目标
  materials: string[];
  facilitator: string; // 带领者
}

export interface GroupSessionFilters {
  search: string;
  status: string;
  sessionType: string;
  targetAge: string;
  therapist: string;
  dateRange: {
    start: string;
    end: string;
  };
}

export interface CreateGroupSessionForm {
  title: string;
  description: string;
  therapistId: string;
  maxParticipants: number;
  sessionType: GroupSession['sessionType'];
  targetAge: GroupSession['targetAge'];
  duration: number;
  frequency: GroupSession['frequency'];
  totalSessions: number;
  startDate: string;
  endDate: string;
  meetingTime: string;
  location: string;
  requirements: string;
  materials: string[];
  notes: string;
}

export interface GroupStatistics {
  totalGroups: number;
  activeGroups: number;
  completedGroups: number;
  totalParticipants: number;
  averageAttendanceRate: number;
  mostPopularSessionType: string;
  monthlyStats: {
    month: string;
    sessions: number;
    participants: number;
  }[];
}
