/* 重新设计的柔和自动播放日历 - 简洁纯色风格 */
.auto-play-calendar {
  position: relative;
  overflow: hidden;
  cursor: default;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.auto-play-calendar:hover {
  box-shadow: 0 4px 20px rgba(148, 163, 184, 0.08);
  border-color: #cbd5e1;
}

/* 动画暂停状态 */
.auto-play-calendar.paused * {
  animation-play-state: paused !important;
}

.calendar-navigation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e2e8f0;
  background: #ffffff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
}

.nav-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

.today-btn {
  margin-left: 0.25rem;
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.today-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.current-month {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  min-width: 120px;
  text-align: center;
  margin: 0 0.5rem;
}

/* 自动播放日期格子样式 */
.auto-play-day {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}

.auto-play-day:hover {
  background: #f8fafc;
  transform: translateY(-1px);
}

/* 选中状态样式 - 纯色设计 */
.auto-play-day.selected {
  background: #eff6ff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.12) !important;
}

.auto-play-day.selected .day-number {
  color: #3b82f6 !important;
  font-weight: 600;
}

/* 预约指示器 - 无交互效果 */
.auto-play-calendar .appointment-indicator {
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
}

.auto-play-calendar .appointment-indicator:hover {
  background: #e2e8f0;
  transform: scale(1.02);
}

/* 今日标记的柔和呼吸动画 */
.auto-play-day.today {
  animation: gentleBreath 4s ease-in-out infinite;
}

@keyframes gentleBreath {
  0%, 100% {
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.06);
    border-color: #bfdbfe;
    background: #fefefe;
  }
  50% {
    box-shadow: 0 3px 10px rgba(59, 130, 246, 0.1);
    border-color: #93c5fd;
    background: #fafbff;
  }
}

.auto-play-day.today .day-number {
  color: #3b82f6;
  font-weight: 600;
}

/* 预约指示器的柔和脉动动画 */
.auto-play-calendar .appointment-indicator {
  animation: softPulse 3s ease-in-out infinite;
}

@keyframes softPulse {
  0%, 100% {
    opacity: 0.85;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.01);
  }
}

/* 为不同状态的预约添加不同的动画延迟和颜色 */
.auto-play-calendar .appointment-indicator.status-confirmed {
  animation-delay: 0s;
  background: #dcfce7;
  border-color: #bbf7d0;
  color: #166534;
}

.auto-play-calendar .appointment-indicator.status-pending {
  animation-delay: 0.5s;
  background: #fef3c7;
  border-color: #fde68a;
  color: #92400e;
}

.auto-play-calendar .appointment-indicator.status-ongoing {
  animation-delay: 1s;
  background: #dbeafe;
  border-color: #bfdbfe;
  color: #1e40af;
}

.auto-play-calendar .appointment-indicator.status-completed {
  animation-delay: 1.5s;
  background: #f3e8ff;
  border-color: #e9d5ff;
  color: #7c3aed;
}

.auto-play-calendar .appointment-indicator.status-cancelled {
  animation-delay: 2s;
  background: #fee2e2;
  border-color: #fecaca;
  color: #dc2626;
}

/* 星期标题的柔和渐显动画 */
.auto-play-calendar .calendar-weekdays .weekday {
  animation: gentleFadeIn 6s ease-in-out infinite;
  color: #64748b;
  font-weight: 500;
}

.auto-play-calendar .calendar-weekdays .weekday:nth-child(1) { animation-delay: 0s; }
.auto-play-calendar .calendar-weekdays .weekday:nth-child(2) { animation-delay: 0.2s; }
.auto-play-calendar .calendar-weekdays .weekday:nth-child(3) { animation-delay: 0.4s; }
.auto-play-calendar .calendar-weekdays .weekday:nth-child(4) { animation-delay: 0.6s; }
.auto-play-calendar .calendar-weekdays .weekday:nth-child(5) { animation-delay: 0.8s; }
.auto-play-calendar .calendar-weekdays .weekday:nth-child(6) { animation-delay: 1s; }
.auto-play-calendar .calendar-weekdays .weekday:nth-child(7) { animation-delay: 1.2s; }

@keyframes gentleFadeIn {
  0%, 100% {
    opacity: 0.7;
    color: #94a3b8;
  }
  50% {
    opacity: 1;
    color: #64748b;
  }
}

/* 日历图标的柔和呼吸动画 */
.auto-play-calendar .calendar-icon {
  animation: iconBreath 8s ease-in-out infinite;
  color: #3b82f6;
}

@keyframes iconBreath {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.03);
  }
}

/* 日历标题的柔和动画 */
.auto-play-calendar .calendar-title h3 {
  animation: titleGlow 10s ease-in-out infinite;
  color: #1e293b;
}

@keyframes titleGlow {
  0%, 100% {
    opacity: 0.9;
    color: #334155;
  }
  50% {
    opacity: 1;
    color: #1e293b;
  }
}

/* 动画暂停状态的样式 */
.auto-play-day.animation-paused {
  animation-play-state: paused;
}

.auto-play-calendar.paused .appointment-indicator {
  animation-play-state: paused;
}

.auto-play-calendar.paused .calendar-icon {
  animation-play-state: paused;
}

.auto-play-calendar.paused .calendar-title h3 {
  animation-play-state: paused;
}

.auto-play-calendar.paused .weekday {
  animation-play-state: paused;
}

/* 更多预约指示器 */
.more-appointments {
  font-size: 10px;
  color: #64748b;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 3px;
  padding: 1px 4px;
  margin-top: 2px;
  text-align: center;
  animation: softPulse 3s ease-in-out infinite;
  animation-delay: 2.5s;
}

/* 预约容器 */
.appointments-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 4px;
}

/* 预约时间显示 */
.appointment-time {
  font-size: 9px;
  margin-left: 2px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-play-calendar {
    border-radius: 8px;
  }
  
  .current-month {
    font-size: 1rem;
    min-width: 100px;
  }
  
  .nav-btn {
    width: 28px;
    height: 28px;
  }
}

/* 减少动画强度的选项（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .auto-play-calendar,
  .auto-play-calendar *,
  .auto-play-calendar::before,
  .auto-play-calendar::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 确保动画不会造成视觉疲劳 */
.auto-play-calendar {
  /* 使用更柔和的动画曲线 */
  --ease-gentle: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 限制动画强度 */
  --max-scale: 1.03;
  --max-opacity-change: 0.3;
}

/* 确保所有动画都使用柔和的缓动函数 */
.auto-play-calendar * {
  animation-timing-function: var(--ease-gentle);
}