import type { GroupSession, GroupParticipant } from '../types/groupSession';
import { electronDataManager } from './electronDataManager';

export class GroupSessionService {
  
  // ????????
  async getAllGroupSessions(): Promise<GroupSession[]> {
    return await electronDataManager.getAllGroupSessions();
  }

  // ????????
  async getGroupSession(id: string): Promise<GroupSession | null> {
    return await electronDataManager.getGroupSession(id);
  }

  // ??????
  async createGroupSession(sessionData: Omit<GroupSession, 'id' | 'createdAt' | 'updatedAt'>): Promise<GroupSession> {
    const newSession: GroupSession = {
      ...sessionData,
      id: `group-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await electronDataManager.saveGroupSession(newSession);
    return newSession;
  }

  // ??????
  async updateGroupSession(id: string, updates: Partial<GroupSession>): Promise<GroupSession | null> {
    const existingSession = await this.getGroupSession(id);
    if (!existingSession) return null;

    const updatedSession: GroupSession = {
      ...existingSession,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await electronDataManager.saveGroupSession(updatedSession);
    return updatedSession;
  }

  // ??????
  async deleteGroupSession(id: string): Promise<boolean> {
    try {
      await electronDataManager.deleteGroupSession(id);
      return true;
    } catch (error) {
      console.error('Failed to delete group session:', error);
      return false;
    }
  }

  // ??????
  async searchGroupSessions(query: string): Promise<GroupSession[]> {
    const allSessions = await this.getAllGroupSessions();
    const searchTerm = query.toLowerCase();
    
    return allSessions.filter(session =>
      session.title.toLowerCase().includes(searchTerm) ||
      (session.description && session.description.toLowerCase().includes(searchTerm)) ||
      session.therapistName.toLowerCase().includes(searchTerm) ||
      session.location.toLowerCase().includes(searchTerm)
    );
  }

  // ?????????
  async filterGroupSessions(filters: {
    status?: string;
    therapistId?: string;
    sessionType?: string;
    targetAge?: string;
  }): Promise<GroupSession[]> {
    const allSessions = await this.getAllGroupSessions();
    let filtered = [...allSessions];

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(session => session.status === filters.status);
    }

    if (filters.therapistId && filters.therapistId !== 'all') {
      filtered = filtered.filter(session => session.therapistId === filters.therapistId);
    }

    if (filters.sessionType && filters.sessionType !== 'all') {
      filtered = filtered.filter(session => session.sessionType === filters.sessionType);
    }

    if (filters.targetAge && filters.targetAge !== 'all') {
      filtered = filtered.filter(session => session.targetAge === filters.targetAge);
    }

    return filtered;
  }

  // ??????????
  async getGroupSessionStats() {
    const sessions = await this.getAllGroupSessions();
    
    const activeSessions = sessions.filter(s => s.status === '???').length;
    const completedSessions = sessions.filter(s => s.status === '???').length;
    const upcomingSessions = sessions.filter(s => s.status === '???').length;

    // ?????
    const byType = sessions.reduce((acc, session) => {
      acc[session.sessionType] = (acc[session.sessionType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // ???????
    const byAge = sessions.reduce((acc, session) => {
      acc[session.targetAge] = (acc[session.targetAge] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // ??????
    const byTherapist = sessions.reduce((acc, session) => {
      acc[session.therapistName] = (acc[session.therapistName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: sessions.length,
      active: activeSessions,
      completed: completedSessions,
      upcoming: upcomingSessions,
      byType,
      byAge,
      byTherapist
    };
  }

  // ???????????
  async getUpcomingGroupSessions(days: number = 7): Promise<GroupSession[]> {
    try {
      const sessions = await this.getAllGroupSessions();
      const now = new Date();
      const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

      return sessions.filter(session => {
        if (session.status !== '???') return false;
        
        const sessionDate = new Date(session.startDate);
        return sessionDate >= now && sessionDate <= futureDate;
      });
    } catch (error) {
      console.error('Failed to get upcoming group sessions:', error);
      return [];
    }
  }

  // ??????????
  async getActiveGroupSessions(): Promise<GroupSession[]> {
    try {
      const sessions = await this.getAllGroupSessions();
      return sessions.filter(session => session.status === '???');
    } catch (error) {
      console.error('Failed to get active group sessions:', error);
      return [];
    }
  }

  // ??????????
  async getPlannedGroupSessions(): Promise<GroupSession[]> {
    try {
      const sessions = await this.getAllGroupSessions();
      return sessions.filter(session => session.status === '???');
    } catch (error) {
      console.error('Failed to get planned group sessions:', error);
      return [];
    }
  }

  // ????????
  async updateSessionStatus(id: string, status: GroupSession['status']): Promise<boolean> {
    try {
      const updated = await this.updateGroupSession(id, { status });
      return updated !== null;
    } catch (error) {
      console.error('Failed to update session status:', error);
      return false;
    }
  }

  // ??????????
  async addParticipantToSession(sessionId: string, participant: Omit<GroupParticipant, 'id' | 'joinDate' | 'status'>): Promise<boolean> {
    try {
      const session = await this.getGroupSession(sessionId);
      if (!session) return false;

      // ??????????
      const existingParticipant = session.participants.find(p => p.visitorId === participant.visitorId);
      if (existingParticipant) {
        return false; // ??????
      }

      if (session.participants.length >= session.maxParticipants) {
        return false; // ????
      }

      const newParticipant: GroupParticipant = {
        ...participant,
        id: `participant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        joinDate: new Date().toISOString(),
        status: '???'
      };

      const updatedParticipants = [...session.participants, newParticipant];
      const updated = await this.updateGroupSession(sessionId, {
        participants: updatedParticipants,
        currentParticipants: updatedParticipants.length
      });

      return updated !== null;
    } catch (error) {
      console.error('Failed to add participant:', error);
      return false;
    }
  }

  // ???????????
  async removeParticipantFromSession(sessionId: string, participantId: string): Promise<boolean> {
    try {
      const session = await this.getGroupSession(sessionId);
      if (!session) return false;

      const updatedParticipants = session.participants.filter(p => p.id !== participantId);
      const updated = await this.updateGroupSession(sessionId, {
        participants: updatedParticipants,
        currentParticipants: updatedParticipants.length
      });

      return updated !== null;
    } catch (error) {
      console.error('Failed to remove participant:', error);
      return false;
    }
  }
}

// ????
export const groupSessionService = new GroupSessionService();
