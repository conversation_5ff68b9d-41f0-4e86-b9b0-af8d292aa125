import React, { useState, useEffect } from 'react';
import { Users, Calendar as CalendarIcon, Wrench, UsersRound, FileText } from 'lucide-react';
import { Carousel } from './ui';
import { DailyWorkspaceSection } from './daily-workspace/DailyWorkspaceSection';
import './Dashboard.css';

interface StatCardProps {
  icon: React.ReactNode;
  label: string;
  value: number;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ icon, label, value, color }) => (
  <div className="stat-card">
    <div className="stat-icon" style={{ backgroundColor: color }}>
      {icon}
    </div>
    <div className="stat-content">
      <h3>{label}</h3>
      <div className="stat-value">{value}</div>
    </div>
  </div>
);



interface DashboardProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help' | 'dev-browser') => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onNavigate }) => {
  // 使用useState创建状态来存储当前时间
  const [currentDate, setCurrentDate] = useState(new Date());
  
  // 轮播图数据
  const carouselSlides = [
    {
      id: 1,
      image: '/1.jpg',
      title: '心理健康服务',
      subtitle: '专业的沙盘治疗管理平台',
      description: '为心理咨询师提供全面的沙盘治疗工具和管理功能'
    },
    {
      id: 2,
      image: '/2.jpg',
      title: '智能管理系统',
      subtitle: '高效的来访者与个案管理',
      description: '完整的预约调度、档案管理和数据统计功能'
    },
    {
      id: 3,
      image: '/3.jpg',
      title: '专业沙具库',
      subtitle: '丰富的沙具分类与管理',
      description: '支持沙具分类、库存管理和使用记录追踪'
    }
  ];
  
  // 处理"了解更多"按钮点击
  const handleLearnMore = () => {
    if (onNavigate) {
      onNavigate('statistics'); // 跳转到数据统计页面了解更多信息
    }
  };
  

  
  const formatDate = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
  };

  // 根据当前时间返回合适的问候语
  const getGreeting = (date: Date) => {
    const hour = date.getHours();
    if (hour >= 5 && hour < 12) {
      return '早上好';
    } else if (hour >= 12 && hour < 14) {
      return '中午好';
    } else if (hour >= 14 && hour < 18) {
      return '下午好';
    } else if (hour >= 18 && hour < 22) {
      return '晚上好';
    } else {
      return '夜深了';
    }
  };



  // 使用useEffect设置定时器，每秒更新当前时间
  useEffect(() => {
    // 设置每秒更新一次时间的定时器
    const timer = setInterval(() => {
      setCurrentDate(new Date());
    }, 1000);

    // 清理函数，组件卸载时清除定时器
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="dashboard">
      {/* 欢迎横幅 */}
      <div className="welcome-banner">
        <div className="welcome-content">
          <div className="welcome-text">
            <h1>💛 {getGreeting(currentDate)}，欢迎回来！</h1>
            <p>今天是 {formatDate(currentDate)}</p>
            <p>我们一起为心理健康事业贡献力量 💪</p>
          </div>
          <div className="welcome-icon">
            <img 
              src="/logo.png" 
              alt="沙盘管理系统Logo" 
              className="logo-image"
            />
          </div>
        </div>
      </div>

      {/* 轮播图区域 */}
      <div className="carousel-section">
        <Carousel 
          slides={carouselSlides} 
          onLearnMore={handleLearnMore}
        />
      </div>

      {/* 今日工作台区域 */}
      <DailyWorkspaceSection onNavigate={onNavigate} />

    </div>
  );
};

export default Dashboard;
