/* 分页组件样式 */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-xl);
}

/* 总数显示 */
.pagination-total {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* 分页控件容器 */
.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 分页按钮基础样式 */
.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.pagination-btn:hover:not(.disabled) {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  background: var(--primary-blue-light);
}

.pagination-btn.disabled {
  cursor: not-allowed;
  opacity: 0.4;
  background: var(--bg-secondary);
}

/* 页码容器 */
.pagination-pages {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin: 0 var(--spacing-sm);
}

/* 页码按钮 */
.pagination-page {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.pagination-page:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  background: var(--primary-blue-light);
}

.pagination-page.active {
  background: var(--primary-blue);
  border-color: var(--primary-blue);
  color: white;
  font-weight: var(--font-semibold);
}

.pagination-page.active:hover {
  background: var(--primary-blue-dark);
  border-color: var(--primary-blue-dark);
}

/* 省略号 */
.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}

/* 每页条数选择器 */
.pagination-size-changer {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.size-changer-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.size-changer-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.size-changer-select:hover {
  border-color: var(--primary-blue);
}

.size-changer-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px var(--primary-blue-light);
}

/* 快速跳转 */
.pagination-jumper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.jumper-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.jumper-input {
  width: 60px;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  text-align: center;
  transition: border-color 0.2s ease;
}

.jumper-input:hover {
  border-color: var(--primary-blue);
}

.jumper-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px var(--primary-blue-light);
}

.jumper-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  border: 1px solid var(--primary-blue);
  background: var(--primary-blue);
  color: white;
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.jumper-btn:hover:not(:disabled) {
  background: var(--primary-blue-dark);
  border-color: var(--primary-blue-dark);
}

.jumper-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .pagination-controls {
    justify-content: center;
  }

  .pagination-total {
    text-align: center;
  }

  .pagination-size-changer,
  .pagination-jumper {
    justify-content: center;
  }

  /* 在小屏幕上隐藏部分功能以保持简洁 */
  .pagination-jumper {
    display: none;
  }
}

@media (max-width: 480px) {
  .pagination-btn,
  .pagination-page {
    width: 32px;
    height: 32px;
    font-size: var(--text-xs);
  }

  .pagination-pages {
    gap: 2px;
  }

  .pagination-controls {
    gap: var(--spacing-xs);
  }

  /* 在极小屏幕上进一步简化 */
  .pagination-size-changer {
    display: none;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .pagination-btn,
  .pagination-page {
    border-color: var(--border-dark, #374151);
    background: var(--bg-secondary-dark, #1f2937);
  }

  .pagination-btn:hover:not(.disabled),
  .pagination-page:hover {
    background: var(--primary-blue-dark, #1d4ed8);
  }

  .size-changer-select,
  .jumper-input {
    border-color: var(--border-dark, #374151);
    background: var(--bg-secondary-dark, #1f2937);
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .pagination-btn,
  .pagination-page {
    border-width: 2px;
  }

  .pagination-page.active {
    border-width: 3px;
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  .pagination-btn,
  .pagination-page,
  .size-changer-select,
  .jumper-input,
  .jumper-btn {
    transition: none;
  }
}