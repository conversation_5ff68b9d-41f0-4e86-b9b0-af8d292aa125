import React from 'react';
import { clsx } from 'clsx';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  className,
  loading = false,
  leftIcon,
  rightIcon,
  disabled,
  ...props
}) => {
  const classes = clsx(
    'btn',
    `btn-${variant}`,
    size !== 'md' && `btn-${size}`,
    loading && 'loading',
    className
  );

  // 内联样式作为终极fallback，确保按钮一定显示正确
  const getInlineStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      padding: '8px 16px', // 统一padding
      fontSize: '14px', // 统一字体大小
      fontWeight: '500',
      borderRadius: '6px',
      cursor: disabled ? 'not-allowed' : 'pointer',
      transition: 'all 0.12s ease',
      textDecoration: 'none',
      whiteSpace: 'nowrap',
      border: '1px solid transparent',
      lineHeight: '1', // 统一行高
      height: '40px', // 统一高度
      boxSizing: 'border-box',
    };

    switch (variant) {
      case 'ghost':
        return {
          ...baseStyle,
          background: 'transparent',
          color: '#3B82F6',
          borderColor: 'transparent',
        };
      case 'secondary':
        return {
          ...baseStyle,
          background: 'white',
          color: '#374151',
          borderColor: '#d1d5db',
        };
      case 'primary':
      default:
        return {
          ...baseStyle,
          background: '#3B82F6',
          color: 'white',
          borderColor: '#3B82F6',
        };
    }
  };

  return (
    <button 
      className={classes} 
      style={getInlineStyle()}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <div className="loading-spinner" />
      ) : (
        <>
          {leftIcon && <span className="btn-icon">{leftIcon}</span>}
          <span>{children}</span>
          {rightIcon && <span className="btn-icon">{rightIcon}</span>}
        </>
      )}
    </button>
  );
};
