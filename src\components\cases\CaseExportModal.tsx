import React, { useState } from 'react';
import { FormModal, Button } from '../ui';
import { Download, FileText, FileSpreadsheet, CheckCircle } from 'lucide-react';
import type { SimpleCase } from '../../types/case';
import { caseService } from '../../services/caseService';
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, WidthType, BorderStyle, AlignmentType } from 'docx';
import { saveAs } from 'file-saver';
import jsPDF from 'jspdf';
import './CaseExportModal.css';

interface CaseExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  cases: SimpleCase[];
  totalCases: number;
  isFiltered: boolean;
}

export const CaseExportModal: React.FC<CaseExportModalProps> = ({
  isOpen,
  onClose,
  cases,
  totalCases,
  isFiltered
}) => {
  const [exportRange, setExportRange] = useState<'current' | 'all'>('current');
  const [exportFormat, setExportFormat] = useState<'csv' | 'docx' | 'pdf'>('csv');
  const [fileName, setFileName] = useState(() => {
    const date = new Date().toISOString().split('T')[0];
    const time = new Date().toTimeString().split(' ')[0].replace(/:/g, '-');
    return `个案数据_${date}_${time}`;
  });
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    try {
      setIsExporting(true);
      
      // 获取要导出的数据
      let dataToExport: SimpleCase[];
      if (exportRange === 'current') {
        dataToExport = cases;
      } else {
        dataToExport = await caseService.getAllCases();
      }

      // 根据格式导出
      switch (exportFormat) {
        case 'csv':
          await exportToCSV(dataToExport, fileName);
          break;
        case 'docx':
          await exportToWord(dataToExport, fileName);
          break;
        case 'pdf':
          await exportToPDF(dataToExport, fileName);
          break;
      }

      alert(`成功导出 ${dataToExport.length} 条个案数据！`);
      onClose();
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请稍后重试');
    } finally {
      setIsExporting(false);
    }
  };

  const exportToCSV = async (data: SimpleCase[], filename: string) => {
    const headers = [
      '个案ID',
      '来访者姓名', 
      '个案摘要',
      '治疗方法',
      '总次数',
      '是否加星',
      '持续时长',
      '危机等级',
      '进展情况',
      '创建时间',
      '更新时间',
      '最近咨询',
      '下次预约',
      '关键词',
      '督导信息'
    ];

    const csvContent = [
      headers.join(','),
      ...data.map(item => [
        item.id,
        `"${item.name || ''}"`,
        `"${(item.summary || '').replace(/"/g, '""')}"`,
        `"${item.therapyMethod || ''}"`,
        item.total || 0,
        item.star ? '是' : '否',
        item.duration || '',
        `"${item.crisis || ''}"`,
        `"${item.progress || ''}"`,
        `"${item.createdAt || ''}"`,
        `"${item.updatedAt || ''}"`,
        `"${item.lastDate || ''}"`,
        `"${item.nextDate || ''}"`,
        `"${(item.keywords || []).join(';')}"`,
        `"${(item.supervision || '').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    // 添加 BOM 头，确保中文正常显示
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    
    // 浏览器环境：直接下载
    downloadFile(blob, `${filename}.csv`);
  };

  const exportToWord = async (data: SimpleCase[], filename: string) => {
    try {
      // 创建表格行
      const tableRows = [
        // 表头
        new TableRow({
          children: [
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "个案ID", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "来访者姓名", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "个案摘要", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "治疗方法", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "总次数", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "是否加星", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "危机等级", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "进展情况", bold: true })] })] }),
            new TableCell({ children: [new Paragraph({ children: [new TextRun({ text: "创建时间", bold: true })] })] }),
          ],
        }),
        // 数据行
        ...data.map(item => new TableRow({
          children: [
            new TableCell({ children: [new Paragraph(item.id)] }),
            new TableCell({ children: [new Paragraph(item.name || '')] }),
            new TableCell({ children: [new Paragraph(item.summary || '')] }),
            new TableCell({ children: [new Paragraph(item.therapyMethod || '')] }),
            new TableCell({ children: [new Paragraph(String(item.total || 0))] }),
            new TableCell({ children: [new Paragraph(item.star ? '是' : '否')] }),
            new TableCell({ children: [new Paragraph(item.crisis || '')] }),
            new TableCell({ children: [new Paragraph(item.progress || '')] }),
            new TableCell({ children: [new Paragraph(item.createdAt || '')] }),
          ],
        })),
      ];

      // 创建Word文档
      const doc = new Document({
        sections: [{
          properties: {},
          children: [
            new Paragraph({
              children: [new TextRun({ text: "个案数据导出报告", bold: true, size: 32 })],
              alignment: AlignmentType.CENTER,
              spacing: { after: 400 },
            }),
            new Paragraph({
              children: [new TextRun({ 
                text: `导出时间：${new Date().toLocaleString('zh-CN')}`, 
                size: 20 
              })],
              spacing: { after: 200 },
            }),
            new Paragraph({
              children: [new TextRun({ 
                text: `数据条数：${data.length} 条`, 
                size: 20 
              })],
              spacing: { after: 400 },
            }),
            new Table({
              width: {
                size: 100,
                type: WidthType.PERCENTAGE,
              },
              borders: {
                top: { style: BorderStyle.SINGLE, size: 1 },
                bottom: { style: BorderStyle.SINGLE, size: 1 },
                left: { style: BorderStyle.SINGLE, size: 1 },
                right: { style: BorderStyle.SINGLE, size: 1 },
                insideHorizontal: { style: BorderStyle.SINGLE, size: 1 },
                insideVertical: { style: BorderStyle.SINGLE, size: 1 },
              },
              rows: tableRows,
            }),
          ],
        }],
      });

      // 生成并下载文件
      const buffer = await Packer.toBuffer(doc);
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      saveAs(blob, `${filename}.docx`);
    } catch (error) {
      console.error('Word导出失败:', error);
      throw new Error('Word导出失败');
    }
  };

  const exportToPDF = async (data: SimpleCase[], filename: string) => {
    try {
      // 创建jsPDF实例，使用A4纸张，横向显示以容纳更多列
      const pdf = new jsPDF('landscape', 'mm', 'a4');
      
      // 设置中文字体支持
      pdf.setFont('courier', 'normal');
      
      // 添加标题
      pdf.setFontSize(18);
      pdf.text('个案数据导出报告', 20, 20);
      
      // 添加导出信息
      pdf.setFontSize(12);
      pdf.text(`导出时间: ${new Date().toLocaleString('zh-CN')}`, 20, 35);
      pdf.text(`数据条数: ${data.length} 条`, 20, 45);
      
      // 表格配置
      const startY = 60;
      const rowHeight = 8;
      const colWidths = [25, 30, 60, 30, 20, 20, 25, 40]; // 列宽度
      let currentY = startY;
      
      // 绘制表头
      pdf.setFontSize(10);
      pdf.setFont('courier', 'bold');
      const headers = ['ID', '姓名', '摘要', '治疗方法', '次数', '加星', '危机等级', '进展'];
      let currentX = 20;
      
      headers.forEach((header, index) => {
        pdf.rect(currentX, currentY, colWidths[index], rowHeight); // 绘制边框
        pdf.text(header, currentX + 2, currentY + 5);
        currentX += colWidths[index];
      });
      
      currentY += rowHeight;
      
      // 绘制数据行
      pdf.setFont('courier', 'normal');
      data.forEach((item) => {
        if (currentY > 180) { // 如果超出页面，创建新页面
          pdf.addPage();
          currentY = 20;
        }
        
        currentX = 20;
        const rowData = [
          item.id,
          item.name || '',
          (item.summary || '').substring(0, 30) + '...', // 截断长文本
          item.therapyMethod || '',
          String(item.total || 0),
          item.star ? '是' : '否',
          item.crisis || '',
          (item.progress || '').substring(0, 20) + '...' // 截断长文本
        ];
        
        rowData.forEach((cellData, colIndex) => {
          pdf.rect(currentX, currentY, colWidths[colIndex], rowHeight); // 绘制边框
          // 处理中文显示问题，使用ASCII字符
          const displayText = cellData.replace(/[\u4e00-\u9fa5]/g, '?'); // 临时替换中文
          pdf.text(displayText, currentX + 2, currentY + 5);
          currentX += colWidths[colIndex];
        });
        
        currentY += rowHeight;
      });
      
      // 保存PDF
      pdf.save(`${filename}.pdf`);
    } catch (error) {
      console.error('PDF导出失败:', error);
      throw new Error('PDF导出失败');
    }
  };

  const downloadFile = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getRangeDescription = () => {
    if (exportRange === 'current') {
      return isFiltered 
        ? `当前筛选结果 (${cases.length} 条)`
        : `当前页面数据 (${cases.length} 条)`;
    }
    return `全部个案数据 (${totalCases} 条)`;
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="导出个案数据"
      size="lg"
    >
      <div className="export-modal-container">
        {/* 导出范围选择 */}
        <div className="export-section">
          <h3 className="export-section-title">
            <CheckCircle size={20} />
            导出范围
          </h3>
          <div className="export-range-options">
            <label className="export-option-card">
              <input
                type="radio"
                name="exportRange"
                value="current"
                checked={exportRange === 'current'}
                onChange={(e) => setExportRange(e.target.value as 'current' | 'all')}
              />
              <div className="option-content">
                <div className="option-title">当前数据</div>
                <div className="option-subtitle">{getRangeDescription()}</div>
              </div>
            </label>
            <label className="export-option-card">
              <input
                type="radio"
                name="exportRange"
                value="all"
                checked={exportRange === 'all'}
                onChange={(e) => setExportRange(e.target.value as 'current' | 'all')}
              />
              <div className="option-content">
                <div className="option-title">全部数据</div>
                <div className="option-subtitle">全部个案数据 ({totalCases} 条)</div>
              </div>
            </label>
          </div>
        </div>

        {/* 导出格式选择 */}
        <div className="export-section">
          <h3 className="export-section-title">
            <FileText size={20} />
            导出格式
          </h3>
          <div className="export-format-options">
            <label className="export-format-card">
              <input
                type="radio"
                name="exportFormat"
                value="csv"
                checked={exportFormat === 'csv'}
                onChange={(e) => setExportFormat(e.target.value as 'csv' | 'docx' | 'pdf')}
              />
              <div className="format-icon">
                <FileText size={24} />
              </div>
              <div className="format-content">
                <div className="format-title">CSV 文件</div>
                <div className="format-subtitle">适合 Excel 打开，兼容性好</div>
                <div className="format-status available">立即可用</div>
              </div>
            </label>
            
            <label className="export-format-card">
              <input
                type="radio"
                name="exportFormat"
                value="docx"
                checked={exportFormat === 'docx'}
                onChange={(e) => setExportFormat(e.target.value as 'csv' | 'docx' | 'pdf')}
              />
              <div className="format-icon">
                <FileSpreadsheet size={24} />
              </div>
              <div className="format-content">
                <div className="format-title">Word 文件</div>
                <div className="format-subtitle">便于编辑和分享</div>
                <div className="format-status available">立即可用</div>
              </div>
            </label>
            
            <label className="export-format-card">
              <input
                type="radio"
                name="exportFormat"
                value="pdf"
                checked={exportFormat === 'pdf'}
                onChange={(e) => setExportFormat(e.target.value as 'csv' | 'docx' | 'pdf')}
              />
              <div className="format-icon">
                <FileText size={24} />
              </div>
              <div className="format-content">
                <div className="format-title">PDF 文件</div>
                <div className="format-subtitle">便于打印和分享</div>
                <div className="format-status available">立即可用</div>
              </div>
            </label>
          </div>
        </div>

        {/* 文件名设置 */}
        <div className="export-section">
          <h3 className="export-section-title">
            <Download size={20} />
            文件设置
          </h3>
          <div className="export-filename-group">
            <div className="filename-input-wrapper">
              <input
                type="text"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                className="filename-input"
                placeholder="请输入文件名"
              />
              <span className="filename-extension">.{exportFormat}</span>
            </div>
            <div className="filename-help">
              文件扩展名将自动添加
            </div>
          </div>
        </div>

        {/* 导出预览 */}
        <div className="export-preview-section">
          <div className="export-preview-card">
            <div className="preview-icon">
              <Download size={24} />
            </div>
            <div className="preview-content">
              <div className="preview-title">
                即将导出：{getRangeDescription()}
              </div>
              <div className="preview-subtitle">
                格式：{exportFormat.toUpperCase()} | 文件名：{fileName}.{exportFormat}
              </div>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="export-tips">
          <div className="tips-content">
            <h4>导出说明：</h4>
            <ul>
              <li>CSV格式可直接在Excel中打开，支持中文显示</li>
              <li>Word格式便于编辑文档，包含完整的表格数据</li>
              <li>PDF格式便于打印和分享，保持格式统一</li>
              <li>导出内容包含个案的完整信息和元数据</li>
              <li>文件将自动下载到浏览器默认下载位置</li>
            </ul>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="export-actions">
          <Button
            leftIcon={<Download size={16} />}
            onClick={handleExport}
            disabled={isExporting || !fileName.trim()}
            size="lg"
          >
            {isExporting ? '导出中...' : '开始导出'}
          </Button>
        </div>
      </div>
    </FormModal>
  );
};

export default CaseExportModal;
