# 数据库迁移 - 添加预约表

## 概述
本文档描述了如何在现有数据库中添加预约管理功能所需的表结构。

## 新增表

### appointments 表
用于存储预约信息。

```sql
CREATE TABLE IF NOT EXISTS appointments (
  id TEXT PRIMARY KEY,
  visitor_id TEXT,
  case_id TEXT,
  visitor_name TEXT NOT NULL,
  visitor_phone TEXT,
  visitor_age INTEGER,
  visitor_gender TEXT CHECK (visitor_gender IN ('男', '女')),
  
  date TEXT NOT NULL,
  start_time TEXT NOT NULL,
  end_time TEXT NOT NULL,
  duration INTEGER NOT NULL,
  
  type TEXT NOT NULL CHECK (type IN ('个体咨询', '团体咨询', '家庭咨询', '沙盘疗法', '评估', '督导', '其他')),
  status TEXT NOT NULL CHECK (status IN ('待确认', '已确认', '进行中', '已完成', '已取消', '缺席')),
  urgency TEXT NOT NULL CHECK (urgency IN ('普通', '紧急', '危机干预')),
  
  room TEXT NOT NULL,
  therapist_id TEXT NOT NULL,
  therapist_name TEXT NOT NULL,
  
  subject TEXT NOT NULL,
  description TEXT,
  notes TEXT,
  
  reminder_enabled INTEGER DEFAULT 0,
  reminder_time INTEGER DEFAULT 15,
  
  is_first_session INTEGER DEFAULT 0,
  session_number INTEGER,
  total_planned_sessions INTEGER,
  
  fee REAL,
  payment_status TEXT CHECK (payment_status IN ('未支付', '已支付', '部分支付')),
  
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  created_by TEXT NOT NULL,
  
  FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE SET NULL,
  FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE SET NULL
);
```

## 索引
为了提高查询性能，建议添加以下索引：

```sql
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(date);
CREATE INDEX IF NOT EXISTS idx_appointments_visitor_id ON appointments(visitor_id);
CREATE INDEX IF NOT EXISTS idx_appointments_case_id ON appointments(case_id);
CREATE INDEX IF NOT EXISTS idx_appointments_therapist_id ON appointments(therapist_id);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
```

## 使用说明

1. 在电子应用的数据库初始化脚本中添加上述 SQL 语句
2. 确保在现有的 visitors 和 cases 表存在之后再创建 appointments 表
3. 外键约束使用 SET NULL，这样删除来访者或个案时不会影响历史预约记录

## 数据关系

- `visitor_id`: 可选，关联到 visitors 表
- `case_id`: 可选，关联到 cases 表
- 支持一次性咨询（不关联个案）
- 支持团体咨询（可能没有特定的来访者ID）

## 兼容性说明

- 表结构兼容现有的 Appointment 接口
- 所有字段都考虑了可选性和默认值
- 使用 TEXT 类型存储日期和时间，保持与现有系统一致
