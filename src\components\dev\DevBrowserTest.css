/* 开发测试组件样式 - 封版时需要删除 */

.dev-browser-test {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.test-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.warning {
  color: #ff6b35;
  font-weight: 600;
  margin: 10px 0;
  background: #fff3f0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #ff6b35;
}

.env-info {
  color: #666;
  font-size: 14px;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  display: inline-block;
}

.message {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 500;
}

.message.success {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.message.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #fafafa;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #374151;
  font-size: 18px;
}

.input-group {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.url-input,
.search-input,
.path-input {
  flex: 1;
  min-width: 300px;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.url-input:focus,
.search-input:focus,
.path-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 按钮样式 */
button {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-baidu {
  background: #2932e1;
  color: white;
}

.btn-baidu:hover {
  background: #1e2ac7;
}

.btn-google {
  background: #ea4335;
  color: white;
}

.btn-google:hover {
  background: #d23826;
}

.btn-warning {
  background: #f59e0b;
  color: white;
}

.btn-warning:hover {
  background: #d97706;
}

.hint {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.info-section {
  background: #f0f9ff;
  border-color: #0ea5e9;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
}

.info-section li {
  margin-bottom: 5px;
  color: #0369a1;
}

.warning-section {
  background: #fefce8;
  border-color: #eab308;
}

.warning-section h3 {
  color: #a16207;
}

.warning-section ul {
  margin: 0;
  padding-left: 20px;
}

.warning-section li {
  margin-bottom: 5px;
  color: #a16207;
  font-family: monospace;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .dev-browser-test {
    margin: 10px;
    padding: 15px;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .url-input,
  .search-input,
  .path-input {
    min-width: 100%;
  }
  
  .button-group {
    justify-content: center;
  }
  
  button {
    flex: 1;
    min-width: 120px;
  }
}
