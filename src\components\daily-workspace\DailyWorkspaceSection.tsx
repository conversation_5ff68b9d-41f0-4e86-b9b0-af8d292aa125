import React from 'react';
import { MoodTrackerCard } from './MoodTrackerCard';
import { MindfulnessCard } from './MindfulnessCard';
import { DailyAchievementCard } from './DailyAchievementCard';
import { WellbeingTipsCard } from './WellbeingTipsCard';
import './DailyWorkspace.css';

interface DailyWorkspaceSectionProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help' | 'dev-browser') => void;
}

export const DailyWorkspaceSection: React.FC<DailyWorkspaceSectionProps> = ({ onNavigate }) => {
  return (
    <div className="daily-workspace">
      <div className="daily-workspace-header">
        <h2>🌱 今日心灵工作台</h2>
        <p>关注内心，记录成长，让每一天都充满意义</p>
      </div>
      
      <div className="daily-workspace-cards">
        <MoodTrackerCard />
        <MindfulnessCard />
        <DailyAchievementCard onNavigate={onNavigate} />
        <WellbeingTipsCard />
      </div>
    </div>
  );
};
