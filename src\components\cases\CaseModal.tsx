import React, { useState, useEffect } from 'react';
import { User, FileText, Eye, X } from 'lucide-react';
import type { SimpleCase, TherapyMethod, CrisisLevel, Progress, Duration } from '../../types/case';
import { Button } from '../ui/Button';

interface CaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (caseData: Partial<SimpleCase>) => Promise<void>;
  case?: SimpleCase | null;
  mode: 'create' | 'edit' | 'view';
}

const initialFormData: Partial<SimpleCase> = {
  name: '',
  summary: '',
  therapyMethod: '认知行为疗法' as TherapyMethod,
  duration: 50 as Duration,
  crisis: '✅' as CrisisLevel,
  progress: '➡️' as Progress,
  total: 0,
  lastDate: '',
  nextDate: '',
  star: false,
  keywords: [],
  supervision: ''
};

export const CaseModal: React.FC<CaseModalProps> = ({
  isOpen,
  onClose,
  onSave,
  case: existingCase,
  mode
}) => {
  const [formData, setFormData] = useState<Partial<SimpleCase>>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (existingCase && mode !== 'create') {
      setFormData({
        ...existingCase,
        keywords: existingCase.keywords || []
      });
    } else {
      setFormData(initialFormData);
    }
    setErrors({});
  }, [existingCase, mode, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleKeywordsChange = (value: string) => {
    const keywords = value.split(',').map(k => k.trim()).filter(k => k);
    handleInputChange('keywords', keywords);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name?.trim()) {
      newErrors.name = '请输入来访者姓名';
    }
    
    if (!formData.summary?.trim()) {
      newErrors.summary = '请输入个案摘要';
    }
    
    if (!formData.therapyMethod) {
      newErrors.therapyMethod = '请选择治疗方法';
    }
    
    if (!formData.duration || formData.duration <= 0) {
      newErrors.duration = '请输入有效的咨询时长';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 查看模式下不处理提交
    if (mode === 'view') {
      return;
    }
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const submitData = {
        ...formData,
        total: Number(formData.total) || 0,
        duration: Number(formData.duration) || 50,
        keywords: formData.keywords || [],
        updatedAt: new Date().toISOString()
      };

      if (mode === 'create') {
        submitData.createdAt = new Date().toISOString();
      }

      await onSave(submitData);
      onClose();
    } catch (error) {
      console.error('保存个案失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case 'create': return '新建个案';
      case 'edit': return '编辑个案';
      case 'view': return '查看个案详情';
      default: return '个案';
    }
  };

  const getModalIcon = () => {
    switch (mode) {
      case 'create': return <User size={16} />;
      case 'edit': return <FileText size={16} />;
      case 'view': return <Eye size={16} />;
      default: return null;
    }
  };

  const isReadOnly = mode === 'view';

  if (!isOpen) return null;

  return (
    <div className="base-modal-overlay">
      <div className="base-modal-container base-modal-lg">
        <div className="base-modal-header">
          <div className="base-modal-header-content">
            <div className="base-modal-title-section">
              <h2 className="base-modal-title" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {getModalIcon()}
                {getModalTitle()}
              </h2>
              {existingCase && mode === 'view' && (
                <p className="base-modal-subtitle">
                  个案ID: {existingCase.id} | 创建时间: {new Date(existingCase.createdAt).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
          <button className="base-modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="base-modal-content">
          <form onSubmit={handleSubmit} className="form-modal-form">
            <div className="form-modal-body">
              {/* 基本信息 */}
              <div className="form-section">
                <h4 className="form-section-title">
                  <User size={16} />
                  基本信息
                </h4>
                
                <div className="form-grid form-grid-2">
                  <div className="form-group">
                    <label className="form-label required">来访者姓名</label>
                    <input
                      type="text"
                      className={`form-input ${errors.name ? 'error' : ''}`}
                      value={formData.name || ''}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="请输入来访者姓名"
                      readOnly={isReadOnly}
                    />
                    {errors.name && <span className="form-error">{errors.name}</span>}
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label required">个案摘要</label>
                  <textarea
                    className={`form-textarea ${errors.summary ? 'error' : ''}`}
                    value={formData.summary || ''}
                    onChange={(e) => handleInputChange('summary', e.target.value)}
                    placeholder="请简要描述个案情况"
                    rows={3}
                    readOnly={isReadOnly}
                  />
                  {errors.summary && <span className="form-error">{errors.summary}</span>}
                </div>
              </div>

              {/* 治疗信息 */}
              <div className="form-section">
                <h4 className="form-section-title">
                  <FileText size={16} />
                  治疗信息
                </h4>
                
                <div className="form-grid form-grid-3">
                  <div className="form-group">
                    <label className="form-label required">治疗方法</label>
                    <select
                      className={`form-select ${errors.therapyMethod ? 'error' : ''}`}
                      value={formData.therapyMethod || '认知行为疗法'}
                      onChange={(e) => handleInputChange('therapyMethod', e.target.value)}
                      disabled={isReadOnly}
                    >
                      <option value="箱庭疗法">箱庭疗法</option>
                      <option value="认知行为疗法">认知行为疗法</option>
                      <option value="精神动力学疗法">精神动力学疗法</option>
                      <option value="人本主义疗法">人本主义疗法</option>
                      <option value="家庭治疗">家庭治疗</option>
                      <option value="团体治疗">团体治疗</option>
                      <option value="其他">其他</option>
                    </select>
                    {errors.therapyMethod && <span className="form-error">{errors.therapyMethod}</span>}
                  </div>

                  <div className="form-group">
                    <label className="form-label">咨询时长（分钟）</label>
                    <select
                      className={`form-select ${errors.duration ? 'error' : ''}`}
                      value={formData.duration?.toString() || '50'}
                      onChange={(e) => handleInputChange('duration', Number(e.target.value))}
                      disabled={isReadOnly}
                    >
                      <option value="25">25分钟</option>
                      <option value="50">50分钟</option>
                      <option value="75">75分钟</option>
                      <option value="90">90分钟</option>
                    </select>
                    {errors.duration && <span className="form-error">{errors.duration}</span>}
                  </div>

                  <div className="form-group">
                    <label className="form-label">危机等级</label>
                    <select
                      className="form-select"
                      value={formData.crisis || '✅'}
                      onChange={(e) => handleInputChange('crisis', e.target.value)}
                      disabled={isReadOnly}
                    >
                      <option value="✅">安全 ✅</option>
                      <option value="⚠️">需要关注 ⚠️</option>
                      <option value="⚡">高危 ⚡</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label className="form-label">治疗进展</label>
                    <select
                      className="form-select"
                      value={formData.progress || '➡️'}
                      onChange={(e) => handleInputChange('progress', e.target.value)}
                      disabled={isReadOnly}
                    >
                      <option value="⬆️">好转 ⬆️</option>
                      <option value="➡️">维持 ➡️</option>
                      <option value="⬇️">恶化 ⬇️</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label className="form-label">总咨询次数</label>
                    <input
                      type="number"
                      className="form-input"
                      value={formData.total?.toString() || '0'}
                      onChange={(e) => handleInputChange('total', Number(e.target.value))}
                      placeholder="咨询次数"
                      min="0"
                      readOnly={isReadOnly}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">最近咨询日期</label>
                    <input
                      type="date"
                      className="form-input"
                      value={formData.lastDate || ''}
                      onChange={(e) => handleInputChange('lastDate', e.target.value)}
                      readOnly={isReadOnly}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">下次预约日期</label>
                    <input
                      type="date"
                      className="form-input"
                      value={formData.nextDate || ''}
                      onChange={(e) => handleInputChange('nextDate', e.target.value)}
                      readOnly={isReadOnly}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-checkbox-item">
                    <input
                      type="checkbox"
                      checked={formData.star || false}
                      onChange={(e) => handleInputChange('star', e.target.checked)}
                      disabled={isReadOnly}
                    />
                    <span>重点关注个案</span>
                  </label>
                </div>
              </div>

              {/* 其他信息 */}
              <div className="form-section">
                <h4 className="form-section-title">
                  其他信息
                </h4>
                
                <div className="form-group">
                  <label className="form-label">关键词</label>
                  <input
                    type="text"
                    className="form-input"
                    value={formData.keywords?.join(', ') || ''}
                    onChange={(e) => handleKeywordsChange(e.target.value)}
                    placeholder="请输入关键词，用逗号分隔"
                    readOnly={isReadOnly}
                  />
                  <span className="form-help">例如：焦虑, 抑郁, 家庭关系</span>
                </div>

                <div className="form-group">
                  <label className="form-label">督导建议</label>
                  <textarea
                    className="form-textarea"
                    value={formData.supervision || ''}
                    onChange={(e) => handleInputChange('supervision', e.target.value)}
                    placeholder="请输入督导建议或备注"
                    rows={4}
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </div>

            <div className="form-modal-footer">
              <div className="form-modal-actions">
                <Button type="button" variant="secondary" onClick={onClose} disabled={isLoading}>
                  {isReadOnly ? '关闭' : '取消'}
                </Button>
                {!isReadOnly && (
                  <Button type="submit" variant="primary" disabled={isLoading}>
                    {isLoading ? '保存中...' : '保存'}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
