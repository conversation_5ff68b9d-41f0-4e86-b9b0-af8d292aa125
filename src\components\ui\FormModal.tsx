import React from 'react';
import { BaseModal } from './BaseModal';
import { Button } from './Button';
import './FormModal.css';

interface FormModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  subtitle?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  submitText?: string;
  cancelText?: string;
  isSubmitting?: boolean;
  submitDisabled?: boolean;
  showFooter?: boolean;
  footerContent?: React.ReactNode;
  className?: string;
}

export const FormModal: React.FC<FormModalProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  size = 'md',
  children,
  onSubmit,
  submitText = '确定',
  cancelText = '取消',
  isSubmitting = false,
  submitDisabled = false,
  showFooter = true,
  footerContent,
  className = ''
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSubmit && !isSubmitting && !submitDisabled) {
      onSubmit(e);
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      subtitle={subtitle}
      size={size}
      className={`form-modal ${className}`}
    >
      <form onSubmit={handleSubmit} className="form-modal-form">
        {/* 表单内容 */}
        <div className="form-modal-body">
          {children}
        </div>

        {/* 底部操作栏 */}
        {showFooter && (
          <div className="form-modal-footer">
            {footerContent || (
              <div className="form-modal-actions">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  {cancelText}
                </Button>
                <Button
                  type="submit"
                  loading={isSubmitting}
                  disabled={submitDisabled || isSubmitting}
                >
                  {isSubmitting ? '处理中...' : submitText}
                </Button>
              </div>
            )}
          </div>
        )}
      </form>
    </BaseModal>
  );
};
