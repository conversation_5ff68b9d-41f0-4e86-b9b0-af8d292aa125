import React from 'react';
import { Card } from '../ui/Card';
import { Select } from '../ui/Form';
import type { AppSettings } from '../../types/settings';

interface NotificationSettingsProps {
  settings: AppSettings;
  updateSettings: (path: string, value: any) => void;
  saving: boolean;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ settings, updateSettings }) => {
  const { notifications } = settings;

  return (
    <div className="settings-form">
      {/* 预约提醒 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">预约提醒</h3>
          <p className="card-subtitle">咨询预约相关通知设置</p>
        </div>
        <div className="card-content">
          <div className="mb-lg">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={notifications.appointments.enabled}
                onChange={(e) => updateSettings('notifications.appointments.enabled', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用预约提醒
            </label>
          </div>
          
          {notifications.appointments.enabled && (
            <div className="settings-form-grid">
              <Select
                label="提前提醒时间"
                value={notifications.appointments.advanceTime.toString()}
                onChange={(e) => updateSettings('notifications.appointments.advanceTime', parseInt(e.target.value))}
                options={[
                  { value: '15', label: '15分钟前' },
                  { value: '30', label: '30分钟前' },
                  { value: '60', label: '1小时前' },
                  { value: '120', label: '2小时前' },
                  { value: '1440', label: '1天前' }
                ]}
              />
            </div>
          )}
          
          {notifications.appointments.enabled && (
            <div className="mt-lg">
              <label className="form-label">提醒方式</label>
              <div className="checkbox-group">
                <label className="checkbox-container">
                  <input
                    type="checkbox"
                    checked={true}
                    disabled
                  />
                  <span className="checkmark"></span>
                  桌面通知
                </label>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* 系统通知 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">系统通知</h3>
          <p className="card-subtitle">系统状态和重要信息通知</p>
        </div>
        <div className="card-content">
          <div className="checkbox-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={notifications.system.updates}
                onChange={(e) => updateSettings('notifications.system.updates', e.target.checked)}
              />
              <span className="checkmark"></span>
              系统更新通知
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={notifications.system.errors}
                onChange={(e) => updateSettings('notifications.system.errors', e.target.checked)}
              />
              <span className="checkmark"></span>
              错误警告通知
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={notifications.system.maintenance}
                onChange={(e) => updateSettings('notifications.system.maintenance', e.target.checked)}
              />
              <span className="checkmark"></span>
              维护计划通知
            </label>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default NotificationSettings;
