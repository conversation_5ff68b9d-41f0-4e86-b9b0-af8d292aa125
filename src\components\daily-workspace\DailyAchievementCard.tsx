import React, { useState, useEffect } from 'react';
import { Trophy, Star, Target, TrendingUp, Calendar, Users } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  progress: number;
  maxProgress: number;
  category: 'daily' | 'weekly' | 'milestone';
  unlockedAt?: string;
}

interface DailyStats {
  appointmentsCompleted: number;
  visitorsReceived: number;
  casesHandled: number;
  mindfulnessSessions: number;
  moodTracked: boolean;
}

interface DailyAchievementCardProps {
  onNavigate?: (page: string) => void;
}

export const DailyAchievementCard: React.FC<DailyAchievementCardProps> = ({ onNavigate }) => {
  const [todayStats, setTodayStats] = useState<DailyStats>({
    appointmentsCompleted: 0,
    visitorsReceived: 0,
    casesHandled: 0,
    mindfulnessSessions: 0,
    moodTracked: false
  });
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [todayProgress, setTodayProgress] = useState(0);

  // 预定义成就
  const predefinedAchievements: Achievement[] = [
    {
      id: 'first_mood',
      title: '心情记录者',
      description: '首次记录今日心情',
      icon: '😊',
      progress: 0,
      maxProgress: 1,
      category: 'daily'
    },
    {
      id: 'mindful_moment',
      title: '正念践行者',
      description: '完成一次正念练习',
      icon: '🧘',
      progress: 0,
      maxProgress: 1,
      category: 'daily'
    },
    {
      id: 'productive_day',
      title: '高效一天',
      description: '完成3个或以上预约',
      icon: '⚡',
      progress: 0,
      maxProgress: 3,
      category: 'daily'
    },
    {
      id: 'helper',
      title: '助人为乐',
      description: '接待5位访客',
      icon: '🤝',
      progress: 0,
      maxProgress: 5,
      category: 'daily'
    },
    {
      id: 'case_solver',
      title: '问题解决者',
      description: '处理2个案例',
      icon: '🎯',
      progress: 0,
      maxProgress: 2,
      category: 'daily'
    },
    {
      id: 'wellness_champion',
      title: '健康倡导者',
      description: '完成心情记录和正念练习',
      icon: '🌟',
      progress: 0,
      maxProgress: 2,
      category: 'daily'
    }
  ];

  // 加载今日数据
  useEffect(() => {
    loadTodayData();
  }, []);

  // 更新成就进度
  useEffect(() => {
    updateAchievements();
  }, [todayStats]);

  const loadTodayData = () => {
    const today = new Date().toISOString().split('T')[0];
    
    // 加载心情记录状态
    const moodData = localStorage.getItem(`mood_${today}`);
    const moodTracked = !!moodData;
    
    // 加载正念练习次数
    const mindfulnessData = localStorage.getItem(`mindfulness_${today}`);
    const mindfulnessSessions = mindfulnessData ? JSON.parse(mindfulnessData).length : 0;
    
    // 模拟其他数据（在实际应用中应该从真实数据源获取）
    const mockStats = {
      appointmentsCompleted: Math.floor(Math.random() * 5),
      visitorsReceived: Math.floor(Math.random() * 8),
      casesHandled: Math.floor(Math.random() * 3),
      mindfulnessSessions,
      moodTracked
    };
    
    setTodayStats(mockStats);
  };

  const updateAchievements = () => {
    const updatedAchievements = predefinedAchievements.map(achievement => {
      let progress = 0;
      
      switch (achievement.id) {
        case 'first_mood':
          progress = todayStats.moodTracked ? 1 : 0;
          break;
        case 'mindful_moment':
          progress = Math.min(todayStats.mindfulnessSessions, 1);
          break;
        case 'productive_day':
          progress = todayStats.appointmentsCompleted;
          break;
        case 'helper':
          progress = todayStats.visitorsReceived;
          break;
        case 'case_solver':
          progress = todayStats.casesHandled;
          break;
        case 'wellness_champion':
          progress = (todayStats.moodTracked ? 1 : 0) + (todayStats.mindfulnessSessions > 0 ? 1 : 0);
          break;
      }
      
      return {
        ...achievement,
        progress,
        unlockedAt: progress >= achievement.maxProgress ? new Date().toISOString() : undefined
      };
    });
    
    setAchievements(updatedAchievements);
    
    // 计算今日总体进度
    const completedAchievements = updatedAchievements.filter(a => a.progress >= a.maxProgress).length;
    const progressPercentage = (completedAchievements / updatedAchievements.length) * 100;
    setTodayProgress(progressPercentage);
  };

  const getProgressColor = (progress: number, maxProgress: number) => {
    const percentage = (progress / maxProgress) * 100;
    if (percentage >= 100) return '#10b981';
    if (percentage >= 50) return '#f59e0b';
    return '#6b7280';
  };

  const completedCount = achievements.filter(a => a.progress >= a.maxProgress).length;

  return (
    <div className="workspace-card achievement-card">
      <div className="workspace-card-header">
        <div className="workspace-card-icon achievement-icon">
          <Trophy size={20} />
        </div>
        <h3 className="workspace-card-title">今日成就</h3>
        <div className="achievement-summary">
          {completedCount}/{achievements.length}
        </div>
      </div>
      
      <div className="workspace-card-content">
        <div className="daily-progress">
          <div className="progress-header">
            <span className="progress-label">今日进度</span>
            <span className="progress-percentage">{Math.round(todayProgress)}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ 
                width: `${todayProgress}%`,
                backgroundColor: todayProgress >= 100 ? '#10b981' : '#3b82f6'
              }}
            />
          </div>
        </div>
        
        <div className="achievements-list">
          {achievements.slice(0, 4).map((achievement) => {
            const isCompleted = achievement.progress >= achievement.maxProgress;
            const progressPercentage = (achievement.progress / achievement.maxProgress) * 100;
            
            return (
              <div 
                key={achievement.id} 
                className={`achievement-item ${isCompleted ? 'completed' : ''}`}
              >
                <div className="achievement-icon-wrapper">
                  <span className="achievement-emoji">{achievement.icon}</span>
                  {isCompleted && (
                    <div className="completion-badge">
                      <Star size={12} fill="#fbbf24" color="#fbbf24" />
                    </div>
                  )}
                </div>
                
                <div className="achievement-info">
                  <h4 className="achievement-title">{achievement.title}</h4>
                  <p className="achievement-description">{achievement.description}</p>
                  
                  <div className="achievement-progress">
                    <div className="progress-bar-small">
                      <div 
                        className="progress-fill-small"
                        style={{ 
                          width: `${Math.min(progressPercentage, 100)}%`,
                          backgroundColor: getProgressColor(achievement.progress, achievement.maxProgress)
                        }}
                      />
                    </div>
                    <span className="progress-text">
                      {achievement.progress}/{achievement.maxProgress}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="quick-stats">
          <div className="stat-item" onClick={() => onNavigate?.('schedule')}>
            <Calendar size={16} />
            <span>预约 {todayStats.appointmentsCompleted}</span>
          </div>
          <div className="stat-item" onClick={() => onNavigate?.('visitors')}>
            <Users size={16} />
            <span>访客 {todayStats.visitorsReceived}</span>
          </div>
          <div className="stat-item" onClick={() => onNavigate?.('cases')}>
            <Target size={16} />
            <span>案例 {todayStats.casesHandled}</span>
          </div>
        </div>
        
        {todayProgress >= 100 && (
          <div className="celebration">
            <div className="celebration-message">
              🎉 恭喜！今日目标全部达成！
            </div>
          </div>
        )}
      </div>
    </div>
  );
};