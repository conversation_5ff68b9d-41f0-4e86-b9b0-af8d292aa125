// 日程安排相关类型定义

// 预约状态
export type AppointmentStatus = '待确认' | '已确认' | '进行中' | '已完成' | '已取消' | '缺席';

// 预约类型
export type AppointmentType = '个体咨询' | '团体咨询' | '家庭咨询' | '沙盘疗法' | '评估' | '督导' | '其他';

// 紧急程度
export type UrgencyLevel = '普通' | '紧急' | '危机干预';

// 咨询师可用状态
export type AvailabilityStatus = '可用' | '忙碌' | '休息' | '请假';

// 预约信息
export interface Appointment {
  id: string;
  visitorId?: string;  // 来访者ID（可选，因为可能是团体预约）
  caseId?: string;     // 关联的个案ID（可选，一次性咨询可能没有个案）
  visitorName: string;
  visitorPhone?: string;
  visitorAge?: number;
  visitorGender?: '男' | '女';
  
  // 时间信息
  date: string;        // YYYY-MM-DD
  startTime: string;   // HH:mm
  endTime: string;     // HH:mm
  duration: number;    // 分钟
  
  // 预约详情
  type: AppointmentType;
  status: AppointmentStatus;
  urgency: UrgencyLevel;
  room: string;        // 咨询室
  therapistId: string;
  therapistName: string;
  
  // 预约内容
  subject: string;     // 主题
  description?: string; // 详细描述
  notes?: string;      // 备注
  
  // 提醒设置
  reminderEnabled: boolean;
  reminderTime: number; // 提前多少分钟提醒
  
  // 跟踪信息
  isFirstSession: boolean;  // 是否首次咨询
  sessionNumber?: number;   // 第几次咨询
  totalPlannedSessions?: number; // 计划总次数
  
  // 费用信息
  fee?: number;        // 费用
  paymentStatus?: '未支付' | '已支付' | '部分支付';
  
  // 系统信息
  createdAt: string;
  updatedAt: string;
  createdBy: string;   // 创建人
}

// 咨询师日程
export interface TherapistSchedule {
  therapistId: string;
  therapistName: string;
  date: string;
  availability: AvailabilityStatus;
  workingHours: {
    start: string;  // HH:mm
    end: string;    // HH:mm
  };
  breaks: {
    start: string;
    end: string;
    title: string;
  }[];
  appointments: Appointment[];
  notes?: string;
}

// 日程视图类型
export type ViewType = 'day' | 'week' | 'month';

// 时间段
export interface TimeSlot {
  start: string;  // HH:mm
  end: string;    // HH:mm
  available: boolean;
  appointment?: Appointment;
}

// 咨询室信息
export interface Room {
  id: string;
  name: string;
  capacity: number;
  type: '个体咨询室' | '团体咨询室' | '沙盘室' | '家庭咨询室';
  equipment: string[];
  available: boolean;
  notes?: string;
}

// 工作时间配置
export interface WorkingHours {
  id: string;
  therapistId: string;
  dayOfWeek: number; // 0-6, 0是周日
  startTime: string;
  endTime: string;
  isWorkingDay: boolean;
}

// 节假日配置
export interface Holiday {
  id: string;
  date: string;
  name: string;
  isWorkingDay: boolean; // 是否工作日
}

// 预约冲突检查结果
export interface ConflictCheck {
  hasConflict: boolean;
  conflicts: {
    type: 'time' | 'room' | 'therapist';
    message: string;
    appointment?: Appointment;
  }[];
}

// 统计信息
export interface ScheduleStats {
  today: {
    total: number;
    completed: number;
    cancelled: number;
    noShow: number;
  };
  thisWeek: {
    total: number;
    completed: number;
    utilizationRate: number; // 利用率
  };
  thisMonth: {
    total: number;
    revenue: number;
    newClients: number;
  };
}

// 快速预约模板
export interface QuickBookingTemplate {
  id: string;
  name: string;
  type: AppointmentType;
  duration: number;
  description: string;
  defaultRoom: string;
  isDefault: boolean;
}
