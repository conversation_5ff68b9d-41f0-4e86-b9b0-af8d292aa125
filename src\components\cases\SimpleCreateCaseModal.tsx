import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { caseService } from '../../services';
import { visitorService } from '../../services';
import type { SimpleCase, TherapyMethod } from '../../types/case';
import type { Visitor } from '../../types/visitor';
import '../cases/SimpleCases.css';

interface SimpleCreateCaseModalProps {
  onClose: () => void;
  onCaseCreated: () => void;
}

const SimpleCreateCaseModal: React.FC<SimpleCreateCaseModalProps> = ({
  onClose,
  onCaseCreated
}) => {
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [formData, setFormData] = useState({
    visitorId: '',
    summary: '',
    therapyMethod: '箱庭疗法' as TherapyMethod,
    duration: 50,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadVisitors();
  }, []);

  const loadVisitors = async () => {
    try {
      const visitorsData = await visitorService.getAllVisitors();
      setVisitors(visitorsData);
    } catch (error) {
      console.error('加载来访者失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.visitorId || !formData.summary.trim()) {
      alert('请填写必要信息');
      return;
    }

    setLoading(true);
    try {
      const selectedVisitor = visitors.find(v => v.id === formData.visitorId);
      if (!selectedVisitor) {
        alert('请选择有效的来访者');
        return;
      }

      const newCase: Omit<SimpleCase, 'id' | 'createdAt' | 'updatedAt'> = {
        visitorId: formData.visitorId,
        name: selectedVisitor.name,
        summary: formData.summary.trim(),
        therapyMethod: formData.therapyMethod,
        selectedSandTools: [],
        total: 1,
        star: true, // 新建的个案默认为进行中
        duration: formData.duration,
      };

      await caseService.createCase(newCase);
      onCaseCreated();
    } catch (error) {
      console.error('创建个案失败:', error);
      alert('创建失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const therapyMethods: TherapyMethod[] = [
    '箱庭疗法',
    '认知行为疗法',
    '精神动力学疗法',
    '人本主义疗法',
    '家庭治疗',
    '团体治疗',
    '行为疗法',
    '绘画治疗',
    '音乐治疗',
    '综合疗法'
  ];

  return (
    <div className="simple-modal">
      <div className="simple-modal-content">
        <div className="simple-modal-header">
          <h2 className="simple-modal-title">新建个案</h2>
          <button 
            className="simple-btn"
            onClick={onClose}
          >
            <X size={16} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="simple-form-group">
            <label className="simple-form-label">选择来访者 *</label>
            <select
              className="simple-form-select"
              value={formData.visitorId}
              onChange={(e) => setFormData({...formData, visitorId: e.target.value})}
              required
            >
              <option value="">请选择来访者</option>
              {visitors.map(visitor => (
                <option key={visitor.id} value={visitor.id}>
                  {visitor.name} ({visitor.gender}, {visitor.age}岁)
                </option>
              ))}
            </select>
          </div>

          <div className="simple-form-group">
            <label className="simple-form-label">问题描述 *</label>
            <textarea
              className="simple-form-textarea"
              value={formData.summary}
              onChange={(e) => setFormData({...formData, summary: e.target.value})}
              placeholder="请简要描述来访者的主要问题..."
              rows={4}
              required
            />
          </div>

          <div className="simple-form-group">
            <label className="simple-form-label">治疗方法</label>
            <select
              className="simple-form-select"
              value={formData.therapyMethod}
              onChange={(e) => setFormData({...formData, therapyMethod: e.target.value as TherapyMethod})}
            >
              {therapyMethods.map(method => (
                <option key={method} value={method}>{method}</option>
              ))}
            </select>
          </div>

          <div className="simple-form-group">
            <label className="simple-form-label">咨询时长（分钟）</label>
            <select
              className="simple-form-select"
              value={formData.duration}
              onChange={(e) => setFormData({...formData, duration: Number(e.target.value)})}
            >
              <option value={25}>25分钟</option>
              <option value={50}>50分钟</option>
              <option value={75}>75分钟</option>
              <option value={90}>90分钟</option>
            </select>
          </div>

          <div className="simple-form-actions">
            <button 
              type="button"
              className="simple-btn"
              onClick={onClose}
            >
              取消
            </button>
            <button 
              type="submit"
              className="simple-btn primary"
              disabled={loading}
            >
              {loading ? '创建中...' : '创建个案'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SimpleCreateCaseModal;
