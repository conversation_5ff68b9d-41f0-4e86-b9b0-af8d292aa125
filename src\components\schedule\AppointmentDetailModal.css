/* 预约详情弹窗样式 - 重新制作 */

/* 模态框基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 8px;
}

.modal-container {
  background: var(--bg-primary);
  border-radius: 6px;
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  height: auto;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  flex-shrink: 0;
}

.header-info {
  flex: 1;
}

.modal-title {
  font-size: 16px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.header-badges {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: var(--font-medium);
  color: white;
}

.urgency-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: var(--font-medium);
  color: white;
  display: flex;
  align-items: center;
  gap: 2px;
}

.first-session-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: var(--font-medium);
  background: var(--accent-purple);
  color: white;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 标签导航 */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
  flex-shrink: 0;
}

.tab-button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.tab-button:hover {
  color: var(--text-primary);
  background: rgba(79, 156, 249, 0.05);
}

.tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
  background: var(--bg-primary);
}

/* 内容区域 */
.modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  min-height: 0;
}

.tab-pane {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

.tab-pane > :last-child {
  margin-bottom: 0 !important;
}

/* 信息区块样式 */
.info-section {
  margin-bottom: 12px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-title {
  font-size: 15px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 6px 0;
  padding-bottom: 3px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-item .label {
  font-size: 12px;
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.info-item .value {
  font-size: 13px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 3px;
}

/* 内容区块 */
.content-block {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.content-item {
  font-size: 13px;
  line-height: 1.4;
  color: var(--text-primary);
}

.content-item strong {
  font-weight: var(--font-semibold);
  color: var(--text-secondary);
}

/* 备注区域 */
.notes-section {
  margin-bottom: 12px;
}

.notes-title {
  font-size: 15px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 6px 0;
  padding-bottom: 3px;
  border-bottom: 1px solid var(--border-light);
}

.notes-content {
  font-size: 13px;
  line-height: 1.5;
  color: var(--text-primary);
  margin: 0;
  padding: 6px;
  background: var(--bg-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

/* 历史记录时间线 */
.history-timeline {
  position: relative;
  padding-left: 20px;
}

.history-timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-light);
}

.timeline-item {
  position: relative;
  margin-bottom: 12px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -16px;
  top: 3px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-blue);
  border: 2px solid var(--bg-primary);
}

.timeline-dot.updated {
  background: var(--success);
}

.timeline-content {
  padding-left: 8px;
}

.timeline-title {
  font-size: 13px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.timeline-time {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.timeline-detail {
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 备注列表 */
.notes-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.note-item {
  padding: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

.note-content {
  font-size: 13px;
  line-height: 1.4;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: var(--text-tertiary);
}

.empty-notes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: var(--text-tertiary);
  text-align: center;
}

.empty-notes p {
  margin: 8px 0 0 0;
  font-size: 13px;
}

/* 状态操作 */
.status-actions {
  padding: 8px 12px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
  flex-shrink: 0;
}

.status-actions h4 {
  font-size: 14px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 6px 0;
}

.actions-row {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

/* 支付状态 */
.payment-status {
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: var(--font-medium);
}

.payment-status.已支付 {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.payment-status.未支付 {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

.payment-status.部分支付 {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

/* 表单组样式 */
.form-group {
  margin-bottom: 8px;
}

.form-group.single {
  grid-column: 1 / -1;
}

.form-label {
  display: block;
  font-size: 13px;
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.form-label.required::after {
  content: " *";
  color: var(--error);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 4px 6px;
  font-size: 13px;
  line-height: 1.4;
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: 3px;
  transition: var(--transition-fast);
  margin: 0;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(79, 156, 249, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
}

.form-error {
  color: var(--error);
  font-size: 12px;
  margin-top: 2px;
}

.calculated-time {
  font-size: 13px;
  color: var(--text-primary);
  padding: 4px 6px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 3px;
  font-weight: var(--font-medium);
}

.checkbox-group {
  margin-bottom: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--text-primary);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 14px;
  height: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 4px;
  }
  
  .modal-container {
    max-width: 100%;
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 8px 10px;
  }
  
  .modal-title {
    font-size: 14px;
  }
  
  .tab-content {
    padding: 6px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }
  
  .header-badges {
    margin-top: 3px;
  }
  
  .actions-row {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .tab-button {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .info-title {
    font-size: 14px;
  }
  
  .info-item .label,
  .info-item .value,
  .content-item,
  .notes-content {
    font-size: 12px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 2px;
  }
}
