import React, { useState } from 'react';
import {
  FormModal,
  Button,
  Input,
  Select,
  Card,
  CardContent
} from '../ui/index';
import { Plus, Trash2, Download, Upload, Users, CheckCircle, AlertCircle, Info } from 'lucide-react';
import type { Visitor, CreateVisitorForm } from '../../types/visitor';
import './BatchImportVisitorModal.enhanced.css';

interface BatchImportVisitorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (visitorsData: Array<Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'>>) => void;
}

// 批量导入表单数据类型
interface BatchImportRow extends Omit<CreateVisitorForm, 'age'> {
  id: string;
  age: number | '';
  errors?: Partial<Record<keyof CreateVisitorForm, string>>;
}

const BatchImportVisitorModal: React.FC<BatchImportVisitorModalProps> = ({ 
  isOpen, 
  onClose, 
  onSubmit 
}) => {
  // 初始化3行空数据
  const createEmptyRow = (): BatchImportRow => ({
    id: `row-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    name: '',
    gender: '男',
    age: '',
    phone: '',
    email: '',
    emergencyContact: '',
    emergencyPhone: '',
    occupation: '',
    education: '',
    address: '',
    notes: ''
  });

  const [rows, setRows] = useState<BatchImportRow[]>([
    createEmptyRow(),
    createEmptyRow(),
    createEmptyRow()
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState<'guide' | 'input' | 'review'>('guide');

  // 更新单个单元格数据
  const updateCell = (rowId: string, field: keyof CreateVisitorForm, value: string | number) => {
    setRows(prevRows => 
      prevRows.map(row => {
        if (row.id === rowId) {
          const updatedRow = { ...row, [field]: value };
          // 清除该字段的错误
          if (updatedRow.errors && updatedRow.errors[field]) {
            const { [field]: removedError, ...restErrors } = updatedRow.errors;
            updatedRow.errors = Object.keys(restErrors).length > 0 ? restErrors : undefined;
          }
          return updatedRow;
        }
        return row;
      })
    );
  };

  // 添加新行
  const addRow = () => {
    setRows(prev => [...prev, createEmptyRow()]);
  };

  // 删除行
  const deleteRow = (rowId: string) => {
    if (rows.length > 1) {
      setRows(prev => prev.filter(row => row.id !== rowId));
    }
  };

  // 验证单行数据
  const validateRow = (row: BatchImportRow): Partial<Record<keyof CreateVisitorForm, string>> => {
    const errors: Partial<Record<keyof CreateVisitorForm, string>> = {};

    if (!row.name.trim()) {
      errors.name = '请输入姓名';
    }

    if (!row.age || Number(row.age) < 1 || Number(row.age) > 120) {
      errors.age = '请输入有效年龄（1-120）';
    }

    if (!row.phone.trim()) {
      errors.phone = '请输入手机号码';
    } else if (!/^1[3-9]\d{9}$/.test(row.phone)) {
      errors.phone = '请输入有效的手机号码';
    }

    if (row.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!row.emergencyContact.trim()) {
      errors.emergencyContact = '请输入紧急联系人';
    }

    if (!row.emergencyPhone.trim()) {
      errors.emergencyPhone = '请输入紧急联系电话';
    } else if (!/^1[3-9]\d{9}$/.test(row.emergencyPhone)) {
      errors.emergencyPhone = '请输入有效的紧急联系电话';
    }

    return errors;
  };

  // 验证所有数据
  const validateAllRows = (): boolean => {
    let hasErrors = false;
    const updatedRows = rows.map(row => {
      // 跳过完全空白的行
      const isEmptyRow = !row.name.trim() && !row.phone.trim() && !row.emergencyContact.trim();
      if (isEmptyRow) {
        return { ...row, errors: undefined };
      }

      const errors = validateRow(row);
      if (Object.keys(errors).length > 0) {
        hasErrors = true;
        return { ...row, errors };
      }
      return { ...row, errors: undefined };
    });

    setRows(updatedRows);
    return !hasErrors;
  };

  // 检查手机号重复
  const checkDuplicatePhones = (): boolean => {
    const phones = rows
      .filter(row => row.phone.trim())
      .map(row => row.phone.trim());
    
    const duplicates = phones.filter((phone, index) => phones.indexOf(phone) !== index);
    
    if (duplicates.length > 0) {
      const updatedRows = rows.map(row => {
        if (duplicates.includes(row.phone.trim())) {
          return {
            ...row,
            errors: {
              ...row.errors,
              phone: '手机号码重复'
            }
          };
        }
        return row;
      });
      setRows(updatedRows);
      return false;
    }
    return true;
  };

  // 提交数据
  const handleSubmit = async () => {
    if (!validateAllRows() || !checkDuplicatePhones()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 过滤掉空行并转换为Visitor格式
      const validRows = rows.filter(row => 
        row.name.trim() && row.phone.trim() && row.emergencyContact.trim()
      );

      const visitorsData: Array<Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'>> = validRows.map(row => ({
        name: row.name.trim(),
        gender: row.gender,
        age: Number(row.age),
        phone: row.phone.trim(),
        email: row.email.trim() || '',
        emergencyContact: row.emergencyContact.trim(),
        emergencyPhone: row.emergencyPhone.trim(),
        occupation: row.occupation.trim() || '',
        education: row.education.trim() || '',
        address: row.address.trim() || '',
        notes: row.notes.trim() || '',
        status: '活跃'
      }));

      if (visitorsData.length === 0) {
        return;
      }

      onSubmit(visitorsData);
      onClose();
      
      // 重置表单
      setRows([
        createEmptyRow(),
        createEmptyRow(),
        createEmptyRow()
      ]);
      setCurrentStep('guide');
    } catch (error) {
      console.error('批量导入失败:', error);
      alert('批量导入失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 下载模板
  const downloadTemplate = () => {
    const csvContent = [
      '姓名,性别,年龄,手机号码,邮箱,紧急联系人,紧急联系电话,职业,教育背景,地址,备注',
      '张三,男,28,13800138001,<EMAIL>,张父,13800138002,软件工程师,本科,北京市朝阳区,工作压力较大',
      '李四,女,32,13800138003,<EMAIL>,李母,13800138004,教师,硕士,上海市浦东新区,人际关系困扰'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '来访者导入模板.csv';
    link.click();
  };

  // 获取有效数据统计
  const getValidRowsCount = () => {
    return rows.filter(row => row.name.trim() && row.phone.trim()).length;
  };

  // 获取错误数量
  const getErrorsCount = () => {
    return rows.filter(row => row.errors && Object.keys(row.errors).length > 0).length;
  };

  // 渲染引导步骤
  const renderGuideStep = () => (
    <div className="batch-import-guide">
      <div className="guide-header">
        <div className="guide-icon">
          <Users size={32} />
        </div>
        <h3>批量新增来访者</h3>
        <p>快速添加多个来访者信息，提高工作效率</p>
      </div>

      <div className="guide-features">
        <div className="feature-item">
          <CheckCircle size={20} />
          <div>
            <h4>智能表格编辑</h4>
            <p>直接在表格中编辑，支持快速输入和验证</p>
          </div>
        </div>
        <div className="feature-item">
          <CheckCircle size={20} />
          <div>
            <h4>数据验证</h4>
            <p>自动验证必填字段和格式，确保数据质量</p>
          </div>
        </div>
        <div className="feature-item">
          <CheckCircle size={20} />
          <div>
            <h4>模板下载</h4>
            <p>提供CSV模板文件，方便批量准备数据</p>
          </div>
        </div>
      </div>

      <div className="guide-actions">
        <Button
          variant="secondary"
          leftIcon={<Download size={16} />}
          onClick={downloadTemplate}
        >
          下载模板
        </Button>
        <Button
          variant="secondary"
          leftIcon={<Upload size={16} />}
          onClick={() => {}}
        >
          导入Excel
        </Button>
        <Button
          leftIcon={<Plus size={16} />}
          onClick={() => setCurrentStep('input')}
        >
          开始添加
        </Button>
      </div>
    </div>
  );

  // 渲染输入步骤
  const renderInputStep = () => (
    <div className="batch-import-input">
      {/* 状态栏 */}
      <div className="input-status-bar">
        <div className="status-info">
          <div className="status-item">
            <span className="status-label">总行数</span>
            <span className="status-value">{rows.length}</span>
          </div>
          <div className="status-item">
            <span className="status-label">有效数据</span>
            <span className="status-value valid">{getValidRowsCount()}</span>
          </div>
          {getErrorsCount() > 0 && (
            <div className="status-item">
              <span className="status-label">错误</span>
              <span className="status-value error">{getErrorsCount()}</span>
            </div>
          )}
        </div>
        <div className="status-actions">
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Plus size={16} />}
            onClick={addRow}
          >
            添加行
          </Button>
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Download size={16} />}
            onClick={downloadTemplate}
          >
            下载模板
          </Button>
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<Upload size={16} />}
            onClick={() => {}}
          >
            导入Excel
          </Button>
        </div>
      </div>

      {/* 表格区域 */}
      <div className="enhanced-table-container">
        <div className="table-wrapper">
          <table className="enhanced-table">
            <thead>
              <tr>
                <th className="col-index">#</th>
                <th className="col-name required">姓名</th>
                <th className="col-gender">性别</th>
                <th className="col-age required">年龄</th>
                <th className="col-phone required">手机号码</th>
                <th className="col-email">邮箱</th>
                <th className="col-emergency-contact required">紧急联系人</th>
                <th className="col-emergency-phone required">紧急联系电话</th>
                <th className="col-occupation">职业</th>
                <th className="col-education">学历</th>
                <th className="col-address">地址</th>
                <th className="col-notes">备注</th>
                <th className="col-actions">操作</th>
              </tr>
            </thead>
            <tbody>
              {rows.map((row, index) => (
                <tr key={row.id} className={`table-row ${row.errors ? 'has-errors' : ''}`}>
                  <td className="col-index">
                    <span className="row-number">{index + 1}</span>
                  </td>
                  <td className="col-name">
                    <Input
                      value={row.name}
                      onChange={(e) => updateCell(row.id, 'name', e.target.value)}
                      placeholder="请输入姓名"
                      error={row.errors?.name}
                      size="sm"
                    />
                  </td>
                  <td className="col-gender">
                    <Select
                      value={row.gender}
                      onChange={(e) => updateCell(row.id, 'gender', e.target.value as '男' | '女' | '其他')}
                      options={[
                        { value: '男', label: '男' },
                        { value: '女', label: '女' },
                        { value: '其他', label: '其他' }
                      ]}
                      size="sm"
                    />
                  </td>
                  <td className="col-age">
                    <Input
                      type="number"
                      value={row.age}
                      onChange={(e) => updateCell(row.id, 'age', e.target.value ? Number(e.target.value) : '')}
                      placeholder="年龄"
                      min={1}
                      max={120}
                      error={row.errors?.age}
                      size="sm"
                    />
                  </td>
                  <td className="col-phone">
                    <Input
                      value={row.phone}
                      onChange={(e) => updateCell(row.id, 'phone', e.target.value)}
                      placeholder="手机号码"
                      error={row.errors?.phone}
                      size="sm"
                    />
                  </td>
                  <td className="col-email">
                    <Input
                      type="email"
                      value={row.email}
                      onChange={(e) => updateCell(row.id, 'email', e.target.value)}
                      placeholder="邮箱地址"
                      error={row.errors?.email}
                      size="sm"
                    />
                  </td>
                  <td className="col-emergency-contact">
                    <Input
                      value={row.emergencyContact}
                      onChange={(e) => updateCell(row.id, 'emergencyContact', e.target.value)}
                      placeholder="紧急联系人"
                      error={row.errors?.emergencyContact}
                      size="sm"
                    />
                  </td>
                  <td className="col-emergency-phone">
                    <Input
                      value={row.emergencyPhone}
                      onChange={(e) => updateCell(row.id, 'emergencyPhone', e.target.value)}
                      placeholder="紧急联系电话"
                      error={row.errors?.emergencyPhone}
                      size="sm"
                    />
                  </td>
                  <td className="col-occupation">
                    <Input
                      value={row.occupation}
                      onChange={(e) => updateCell(row.id, 'occupation', e.target.value)}
                      placeholder="职业"
                      size="sm"
                    />
                  </td>
                  <td className="col-education">
                    <Select
                      value={row.education}
                      onChange={(e) => updateCell(row.id, 'education', e.target.value)}
                      options={[
                        { value: '', label: '请选择学历' },
                        { value: '小学', label: '小学' },
                        { value: '初中', label: '初中' },
                        { value: '高中', label: '高中' },
                        { value: '中专', label: '中专' },
                        { value: '大专', label: '大专' },
                        { value: '本科', label: '本科' },
                        { value: '硕士', label: '硕士' },
                        { value: '博士', label: '博士' }
                      ]}
                      size="sm"
                    />
                  </td>
                  <td className="col-address">
                    <Input
                      value={row.address}
                      onChange={(e) => updateCell(row.id, 'address', e.target.value)}
                      placeholder="地址"
                      size="sm"
                    />
                  </td>
                  <td className="col-notes">
                    <Input
                      value={row.notes}
                      onChange={(e) => updateCell(row.id, 'notes', e.target.value)}
                      placeholder="备注"
                      size="sm"
                    />
                  </td>
                  <td className="col-actions">
                    <Button
                      variant="ghost"
                      size="sm"
                      leftIcon={<Trash2 size={14} />}
                      onClick={() => deleteRow(row.id)}
                      disabled={rows.length <= 1}
                      className="delete-btn"
                      title="删除此行"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 帮助信息 */}
      <Card className="help-card">
        <CardContent>
          <div className="help-header">
            <Info size={16} />
            <span>填写说明</span>
          </div>
          <div className="help-content">
            <div className="help-section">
              <h5>必填字段</h5>
              <p>姓名、年龄、手机号码、紧急联系人、紧急联系电话为必填项</p>
            </div>
            <div className="help-section">
              <h5>格式要求</h5>
              <p>手机号码：11位数字，以1开头 • 邮箱：标准邮箱格式 • 年龄：1-120岁</p>
            </div>
            <div className="help-section">
              <h5>操作提示</h5>
              <p>空行将被自动忽略 • 重复的手机号码会被标记为错误 • 可随时添加或删除行</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // 渲染步骤导航
  const renderStepNavigation = () => {
    if (currentStep === 'guide') return null;

    return (
      <div className="step-navigation">
        <Button
          variant="ghost"
          onClick={() => setCurrentStep('guide')}
        >
          返回引导
        </Button>
        <div className="step-info">
          <span className="step-current">数据输入</span>
        </div>
      </div>
    );
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title={currentStep === 'guide' ? '批量新增来访者' : `批量新增来访者 - 数据输入`}
      subtitle={currentStep === 'guide' ? '快速添加多个来访者信息' : `准备导入 ${getValidRowsCount()} 个来访者`}
      onSubmit={currentStep === 'input' ? handleSubmit : undefined}
      submitText={currentStep === 'input' ? `导入 ${getValidRowsCount()} 个来访者` : undefined}
      isSubmitting={isSubmitting}
      size="large"
      className="enhanced-batch-import-modal"
    >
      {renderStepNavigation()}
      {currentStep === 'guide' && renderGuideStep()}
      {currentStep === 'input' && renderInputStep()}
    </FormModal>
  );
};

export { BatchImportVisitorModal };
export default BatchImportVisitorModal;