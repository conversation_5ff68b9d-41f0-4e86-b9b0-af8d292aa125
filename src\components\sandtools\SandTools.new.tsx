import React, { useState, useMemo } from 'react';
import { 
  Plus, 
  Search, 
  Package, 
  Eye, 
  Edit,
  Trash2,
  Grid,
  List
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  PageHeader, 
  Card, 
  CardHeader, 
  CardContent, 
  Button, 
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  Badge,
  FilterBar,
  EmptyState
} from '../ui/index';
import { SandToolDetailModal } from './SandToolDetailModal';
import CreateSandToolModal from './CreateSandToolModal';
import { 
  mockSandTools, 
  sandToolCategories, 
  getStatsData, 
  filterTools
} from '../../data/mockSandTools';
import type { SandTool, SandToolFilters } from '../../types/sandtool';
import './SandTools.css';

const SandTools: React.FC = () => {
  const [tools, setTools] = useState<SandTool[]>(mockSandTools);
  const [selectedTool, setSelectedTool] = useState<SandTool | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [filters, setFilters] = useState<SandToolFilters>({});
  const [searchTerm, setSearchTerm] = useState('');

  const stats = useMemo(() => getStatsData(), [tools]);
  
  const filteredTools = useMemo(() => {
    return filterTools(tools, { ...filters, search: searchTerm });
  }, [tools, filters, searchTerm]);

  const handleViewDetail = (tool: SandTool) => {
    setSelectedTool(tool);
    setShowDetailModal(true);
  };

  const handleEdit = (tool: SandTool) => {
    console.log('编辑沙具:', tool.name);
  };

  // 删除沙具
  const handleDelete = (tool: SandTool) => {
    if (confirm(`确定要删除沙具"${tool.name}"吗？此操作不可恢复。`)) {
      console.log('删除沙具:', tool);
      // TODO: 实际删除逻辑
    }
  };

  const handleAddTool = () => {
    setShowCreateModal(true);
  };

  // 获取库存状态徽章
  const getStockBadge = (tool: SandTool) => {
    if (tool.available === 0) {
      return <Badge variant="danger">无库存</Badge>;
    } else if (tool.available <= tool.quantity * 0.2) {
      return <Badge variant="warning">库存不足</Badge>;
    } else if (tool.available >= tool.quantity * 0.8) {
      return <Badge variant="success">库存充足</Badge>;
    } else {
      return <Badge variant="gray">库存正常</Badge>;
    }
  };

  // 获取状况标签
  const getConditionBadge = (condition: string) => {
    const variants = {
      '全新': 'success' as const,
      '良好': 'success' as const,  
      '一般': 'warning' as const,
      '损坏': 'danger' as const,
      '报废': 'danger' as const
    };
    return <Badge variant={variants[condition as keyof typeof variants] || 'gray'}>{condition}</Badge>;
  };

  // 获取类别图标
  const getCategoryIcon = (category: string) => {
    const categoryInfo = sandToolCategories.find(cat => cat.id === category);
    return categoryInfo?.icon || '📦';
  };

  return (
    <PageContainer>
      <PageHeader
        title="沙具管理"
        subtitle="专业沙盘治疗工具库存管理系统"
        actions={
          <>
            <Button
              variant="secondary"
              leftIcon={viewMode === 'grid' ? <List /> : <Grid />}
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? '列表视图' : '网格视图'}
            </Button>
            <Button
              variant="primary"
              leftIcon={<Plus />}
              onClick={handleAddTool}
            >
              添加沙具
            </Button>
          </>
        }
      />

      {/* 统计概览 - 与个案管理完全一致 */}
      <div className="stats-row">
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-value">{stats.totalTools}</div>
              <div className="stat-label">沙具总数</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-value">{stats.availableTools}</div>
              <div className="stat-label">可用库存</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-value">{stats.lowStockTools}</div>
              <div className="stat-label">库存不足</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-value">{stats.needsMaintenanceTools}</div>
              <div className="stat-label">需要维护</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-xl">
        <CardContent>
          <FilterBar
            searchProps={{
              value: searchTerm,
              onChange: (e) => setSearchTerm(e.target.value),
              placeholder: "搜索沙具名称...",
              leftIcon: <Search size={16} />
            }}
            filters={[
              {
                value: filters.category || 'all',
                onChange: (e) => setFilters(prev => ({ ...prev, category: e.target.value as any })),
                options: [
                  { value: 'all', label: '全部类别' },
                  ...sandToolCategories.map(category => ({
                    value: category.id,
                    label: `${category.icon} ${category.name}`
                  }))
                ]
              },
              {
                value: filters.condition || 'all',
                onChange: (e) => setFilters(prev => ({ ...prev, condition: e.target.value as any })),
                options: [
                  { value: 'all', label: '全部状况' },
                  { value: '全新', label: '全新' },
                  { value: '良好', label: '良好' },
                  { value: '一般', label: '一般' },
                  { value: '损坏', label: '损坏' },
                  { value: '报废', label: '报废' }
                ]
              },
              {
                value: filters.availability || 'all',
                onChange: (e) => setFilters(prev => ({ ...prev, availability: e.target.value as any })),
                options: [
                  { value: 'all', label: '全部库存' },
                  { value: 'available', label: '有库存' },
                  { value: 'unavailable', label: '无库存' },
                  { value: 'low-stock', label: '库存不足' }
                ]
              }
            ]}
          />
          <div className="mt-4">
            <label className="checkbox-filter">
              <input
                type="checkbox"
                checked={filters.needsMaintenance || false}
                onChange={(e) => setFilters(prev => ({ ...prev, needsMaintenance: e.target.checked }))}
              />
              需要维护
            </label>
          </div>
        </CardContent>
      </Card>

      {/* 沙具列表 - 与个案管理布局完全一致 */}
      <Card>
        <CardHeader title={`沙具列表 (${filteredTools.length})`} />
        <CardContent>
          {filteredTools.length === 0 ? (
            <EmptyState
              icon={<Package size={48} />}
              title="暂无沙具"
              description="没有找到符合条件的沙具，请调整筛选条件或添加新的沙具。"
              action={
                <Button
                  variant="primary"
                  leftIcon={<Plus />}
                  onClick={handleAddTool}
                >
                  添加第一个沙具
                </Button>
              }
            />
          ) : viewMode === 'list' ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableCell>沙具信息</TableCell>
                  <TableCell>类别</TableCell>
                  <TableCell>库存状态</TableCell>
                  <TableCell>状况</TableCell>
                  <TableCell>位置</TableCell>
                  <TableCell>最后使用</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTools.map((tool) => (
                  <TableRow key={tool.id}>
                    <TableCell>
                      <div className="tool-info">
                        <div className="tool-name">
                          <div className="tool-thumbnail">
                            {tool.imageUrl ? (
                              <img 
                                src={tool.imageUrl} 
                                alt={tool.name}
                                className="thumbnail-image"
                                onError={(e) => {
                                  const target = e.currentTarget;
                                  const placeholder = target.nextElementSibling as HTMLElement;
                                  target.style.display = 'none';
                                  if (placeholder) {
                                    placeholder.style.display = 'flex';
                                  }
                                }}
                              />
                            ) : null}
                            <div className="thumbnail-placeholder" style={{display: tool.imageUrl ? 'none' : 'flex'}}>
                              <span className="thumbnail-icon">{getCategoryIcon(tool.category)}</span>
                            </div>
                          </div>
                          <div className="name-details">
                            <div className="name">{tool.name}</div>
                            <div className="details">
                              {tool.size} • {tool.material}
                            </div>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="category-info">
                        <div className="category">{getCategoryIcon(tool.category)} {sandToolCategories.find(cat => cat.id === tool.category)?.name || tool.category}</div>
                        {tool.subcategory && (
                          <div className="subcategory">{tool.subcategory}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="stock-info">
                        <div className="stock-numbers">{tool.available}/{tool.quantity}</div>
                        {getStockBadge(tool)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getConditionBadge(tool.condition)}
                    </TableCell>
                    <TableCell>
                      <div className="location-info">{tool.location}</div>
                    </TableCell>
                    <TableCell>
                      <div className="usage-info">
                        {tool.lastUsed ? (
                          <>
                            <div className="last-used">{tool.lastUsed}</div>
                            <div className="usage-count">使用 {tool.usageCount || 0} 次</div>
                          </>
                        ) : (
                          <div className="never-used">从未使用</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="action-buttons">
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Eye size={14} />}
                          onClick={() => handleViewDetail(tool)}
                        >
                          查看
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Edit size={14} />}
                          onClick={() => handleEdit(tool)}
                        >
                          编辑
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Trash2 size={14} />}
                          onClick={() => handleDelete(tool)}
                        >
                          删除
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="tools-grid">
              {filteredTools.map((tool) => (
                <Card key={tool.id} className="tool-card">
                  <CardContent className="tool-card-content">
                    <div className="tool-card-image">
                      {tool.imageUrl ? (
                        <img 
                          src={tool.imageUrl} 
                          alt={tool.name}
                          className="card-image"
                          onError={(e) => {
                            const target = e.currentTarget;
                            const placeholder = target.nextElementSibling as HTMLElement;
                            target.style.display = 'none';
                            if (placeholder) {
                              placeholder.style.display = 'flex';
                            }
                          }}
                        />
                      ) : null}
                      <div className="card-image-placeholder" style={{display: tool.imageUrl ? 'none' : 'flex'}}>
                        <span className="card-icon">{getCategoryIcon(tool.category)}</span>
                      </div>
                    </div>
                    <div className="tool-card-info">
                      <h3 className="tool-card-title">{tool.name}</h3>
                      <p className="tool-card-category">{getCategoryIcon(tool.category)} {sandToolCategories.find(cat => cat.id === tool.category)?.name || tool.category}</p>
                      
                      <div className="tool-card-details">
                        <span>{tool.size}</span>
                        <span>•</span>
                        <span>{tool.material}</span>
                        <span>•</span>
                        <span>{tool.available}/{tool.quantity}</span>
                        {getStockBadge(tool)}
                        {getConditionBadge(tool.condition)}
                      </div>
                      
                      <div className="tool-card-actions">
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Eye size={14} />}
                          onClick={() => handleViewDetail(tool)}
                        >
                          查看
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Edit size={14} />}
                          onClick={() => handleEdit(tool)}
                        >
                          编辑
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Trash2 size={14} />}
                          onClick={() => handleDelete(tool)}
                        >
                          删除
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 详情模态框 */}
      {showDetailModal && selectedTool && (
        <SandToolDetailModal
          isOpen={showDetailModal}
          tool={selectedTool}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedTool(null);
          }}
          onEdit={(tool: SandTool) => {
            setShowDetailModal(false);
            setSelectedTool(null);
            handleEdit(tool);
          }}
          onDelete={(tool: SandTool) => {
            setShowDetailModal(false);
            setSelectedTool(null);
            handleDelete(tool);
          }}
        />
      )}

      {/* 创建模态框 */}
      {showCreateModal && (
        <CreateSandToolModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={(newTool: Omit<SandTool, 'id'>) => {
            const toolWithId: SandTool = {
              ...newTool,
              id: `tool-${Date.now()}`
            };
            setTools(prev => [...prev, toolWithId]);
            setShowCreateModal(false);
          }}
        />
      )}
    </PageContainer>
  );
};

export default SandTools;
