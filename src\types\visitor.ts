// 来访者数据类型定义
export interface Visitor {
  id: string;
  name: string;
  gender: '男' | '女';
  age: number;
  phone: string;
  email?: string;
  emergencyContact: string;
  emergencyPhone: string;
  occupation?: string;
  education?: string;
  address?: string;
  notes?: string;
  status: '活跃' | '暂停' | '完成';
  createdAt: string;
  updatedAt: string;
}

// 创建来访者表单数据
export interface CreateVisitorForm {
  name: string;
  gender: '男' | '女' | '';
  age: number | '';
  phone: string;
  email: string;
  emergencyContact: string;
  emergencyPhone: string;
  occupation: string;
  education: string;
  address: string;
  notes: string;
  status: '活跃' | '暂停' | '完成' | '';
}

// 搜索和筛选类型
export interface VisitorFilters {
  search: string;
  status: string;
  gender: string;
  ageRange: string;
}
