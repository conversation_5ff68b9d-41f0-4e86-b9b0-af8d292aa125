import React, { useState } from 'react';
import { Search, Bell, Menu, X } from 'lucide-react';
import './Header.css';

interface HeaderProps {
  onToggleSidebar: () => void;
  isSidebarCollapsed: boolean;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar, isSidebarCollapsed }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications] = useState([
    { id: 1, title: '新的预约提醒', message: '张三的沙盘疗法预约在30分钟后开始', time: '2分钟前', unread: true },
    { id: 2, title: '系统维护通知', message: '系统将于今晚22:00-24:00进行维护', time: '3小时前', unread: false },
  ]);

  const unreadCount = notifications.filter(n => n.unread).length;

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      console.log('搜索:', searchQuery);
      // 这里可以添加搜索逻辑
    }
  };

  return (
    <header className="header">
      <div className="header-left">
        <button 
          className="menu-toggle"
          onClick={onToggleSidebar}
          aria-label={isSidebarCollapsed ? '展开菜单' : '折叠菜单'}
        >
          <Menu size={20} />
        </button>
        
        <div className="brand-info">
          <div className="logo-badge">
            <img src="/logo.png" alt="Logo" className="logo-image" />
          </div>
          <div className="brand-text">
            <span className="app-title">沙盘管理系统</span>
            <span className="app-subtitle">心理健康服务平台</span>
          </div>
        </div>
      </div>

      <div className="header-center">
        <form className="search-form" onSubmit={handleSearch}>
          <div className="search-container">
            <Search size={18} className="search-icon" />
            <input
              type="text"
              placeholder="搜索来访者、沙具..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                type="button"
                className="search-clear"
                onClick={() => setSearchQuery('')}
              >
                <X size={16} />
              </button>
            )}
          </div>
        </form>
      </div>

      <div className="header-right">
        <div className="notification-container">
          <button 
            className="notification-btn"
            onClick={() => setShowNotifications(!showNotifications)}
            aria-label="通知"
          >
            <Bell size={20} />
            {unreadCount > 0 && (
              <span className="notification-badge">{unreadCount}</span>
            )}
          </button>

          {showNotifications && (
            <div className="notification-dropdown">
              <div className="notification-header">
                <h3>通知</h3>
                <span className="notification-count">{unreadCount} 条未读</span>
              </div>
              <div className="notification-list">
                {notifications.map((notification) => (
                  <div 
                    key={notification.id} 
                    className={`notification-item ${notification.unread ? 'unread' : ''}`}
                  >
                    <div className="notification-content">
                      <h4>{notification.title}</h4>
                      <p>{notification.message}</p>
                      <span className="notification-time">{notification.time}</span>
                    </div>
                    {notification.unread && <div className="unread-dot"></div>}
                  </div>
                ))}
              </div>
              <div className="notification-footer">
                <button className="view-all-btn">查看全部通知</button>
              </div>
            </div>
          )}
        </div>

        <div className="user-info">
          <div className="user-avatar">
            <span>管</span>
          </div>
          <span className="user-name">管理员</span>
        </div>
      </div>
    </header>
  );
};

export default Header;
