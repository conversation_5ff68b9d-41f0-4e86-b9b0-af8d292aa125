import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { <PERSON>ton, Badge } from '../ui';
import { 
  Package, 
  Tag, 
  MapPin, 
  Calendar, 
  Activity,
  AlertTriangle,
  Heart,
  Palette,
  Ruler
} from 'lucide-react';
import type { SandTool } from '../../types/sandtool';

interface SandToolDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  tool: SandTool | null;
  onEdit?: (tool: SandTool) => void;
  onDelete?: (tool: SandTool) => void;
}

export const SandToolDetailModal: React.FC<SandToolDetailModalProps> = ({
  isOpen,
  onClose,
  tool,
  onEdit,
  onDelete
}) => {
  if (!tool) return null;

  const handleEdit = () => {
    if (onEdit) {
      onEdit(tool);
    }
    onClose();
  };

  const handleDelete = () => {
    if (onDelete && window.confirm('确定要删除这个沙具吗？')) {
      onDelete(tool);
      onClose();
    }
  };

  // 状况颜色映射
  const getConditionColor = (condition: string): 'primary' | 'success' | 'warning' | 'danger' | 'gray' => {
    switch (condition) {
      case '全新': return 'success';
      case '良好': return 'success';
      case '一般': return 'warning';
      case '损坏': return 'danger';
      case '报废': return 'danger';
      default: return 'gray';
    }
  };

  // 库存状态
  const getStockStatus = (): { text: string; color: 'primary' | 'success' | 'warning' | 'danger' | 'gray' } => {
    const percentage = (tool.available / tool.quantity) * 100;
    if (percentage === 0) return { text: '无库存', color: 'danger' };
    if (percentage <= 20) return { text: '库存不足', color: 'warning' };
    if (percentage <= 50) return { text: '库存偏低', color: 'warning' };
    return { text: '库存充足', color: 'success' };
  };

  const stockStatus = getStockStatus();

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="查看沙具详情"
      subtitle={`沙具ID: ${tool.id}`}
      size="lg"
    >
      <div className="form-modal-body">
        {/* 基本信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <Package size={16} />
            基本信息
          </h4>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">沙具名称</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {tool.name}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">类别</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {tool.category}
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">子类别</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {tool.subcategory || '无'}
              </div>
            </div>
          </div>
          
          <div className="form-grid form-grid-2">
            <div className="form-group">
              <label className="form-label">材质</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {tool.material}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">状况</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                <Badge variant={getConditionColor(tool.condition)}>
                  {tool.condition}
                </Badge>
              </div>
            </div>
          </div>

          {tool.description && (
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <label className="form-label">描述</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', minHeight: '60px', alignItems: 'flex-start', padding: '12px' }}>
                  {tool.description}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 规格信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <Palette size={16} />
            规格信息
          </h4>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">尺寸</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Ruler size={16} />
                {tool.size}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">颜色</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {tool.color || '未指定'}
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">存放位置</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <MapPin size={16} />
                {tool.location}
              </div>
            </div>
          </div>
        </div>

        {/* 库存信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <Activity size={16} />
            库存信息
          </h4>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">总数量</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {tool.quantity} 件
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">可用数量</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {tool.available} 件
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">库存状态</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                <Badge variant={stockStatus.color}>
                  {stockStatus.text}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* 使用记录 */}
        {(tool.usageCount || tool.lastUsed) && (
          <div className="form-section">
            <h4 className="form-section-title">
              <Calendar size={16} />
              使用记录
            </h4>
            
            <div className="form-grid form-grid-2">
              {tool.usageCount !== undefined && (
                <div className="form-group">
                  <label className="form-label">累计使用次数</label>
                  <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                    {tool.usageCount} 次
                  </div>
                </div>
              )}
              
              {tool.lastUsed && (
                <div className="form-group">
                  <label className="form-label">最后使用时间</label>
                  <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                    {tool.lastUsed}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 图片显示 */}
        {tool.imageData && (
          <div className="form-section">
            <h4 className="form-section-title">沙具图片</h4>
            
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <div className="tool-image-preview">
                  <img 
                    src={tool.imageData} 
                    alt={tool.name}
                    className="preview-image"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 标签 */}
        {tool.tags && tool.tags.length > 0 && (
          <div className="form-section">
            <h4 className="form-section-title">
              <Tag size={16} />
              标签
            </h4>
            
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <div className="tags-display">
                  {tool.tags.map((tag, index) => (
                    <Badge key={index} variant="gray">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 特殊标记 */}
        {(tool.isFragile || tool.needsCare || tool.replacementNeeded) && (
          <div className="form-section">
            <h4 className="form-section-title">
              <AlertTriangle size={16} />
              特殊标记
            </h4>
            
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <div className="special-marks-display">
                  {tool.isFragile && (
                    <Badge variant="warning">
                      <AlertTriangle size={12} />
                      易碎物品
                    </Badge>
                  )}
                  {tool.needsCare && (
                    <Badge variant="primary">
                      <Heart size={12} />
                      需要特殊保养
                    </Badge>
                  )}
                  {tool.replacementNeeded && (
                    <Badge variant="danger">
                      <AlertTriangle size={12} />
                      需要更换
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 备注 */}
        {tool.notes && (
          <div className="form-section">
            <h4 className="form-section-title">备注</h4>
            
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', minHeight: '80px', alignItems: 'flex-start', padding: '12px' }}>
                  {tool.notes}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="form-modal-footer">
        <div className="form-modal-actions">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            关闭
          </Button>
          {onEdit && (
            <Button
              onClick={handleEdit}
            >
              编辑沙具
            </Button>
          )}
          {onDelete && (
            <Button
              variant="danger"
              onClick={handleDelete}
            >
              删除沙具
            </Button>
          )}
        </div>
      </div>
    </BaseModal>
  );
};

export default SandToolDetailModal;
