import React, { useState } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import Dashboard from './Dashboard';
import Visitors from './visitors/Visitors';
import Cases from './cases/Cases';
import Schedule from './schedule/Schedule';
import GroupSessions from './group-sessions/GroupSessions';
import SandTools from './sandtools/SandTools';
import Statistics from './Statistics';
import Settings from './settings/Settings';
import Help from './help/Help';
import DevBrowserTest from './dev/DevBrowserTest';
import './Layout.css';

type PageType = 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help' | 'dev-browser';

const Layout: React.FC = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState<PageType>('dashboard');

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'visitors':
        return <Visitors />;
      case 'cases':
        return <Cases />;
      case 'schedule':
        return <Schedule />;
      case 'group-sessions':
        return <GroupSessions />;
      case 'sandtools':
        return <SandTools />;
      case 'statistics':
        return <Statistics />;
      case 'settings':
        return <Settings />;
      case 'help':
        return <Help />;
      case 'dev-browser':
        return <DevBrowserTest />;
      case 'dashboard':
      default:
        return <Dashboard onNavigate={setCurrentPage} />;
    }
  };

  return (
    <div className="layout">
      <Header 
        onToggleSidebar={toggleSidebar} 
        isSidebarCollapsed={!isSidebarCollapsed} 
      />
      <Sidebar 
        isCollapsed={isSidebarCollapsed} 
        currentPage={currentPage}
        onPageChange={setCurrentPage}
      />
      <main className={`main-content ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        {renderCurrentPage()}
      </main>
    </div>
  );
};

export default Layout;
