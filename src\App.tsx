import { useEffect, useState } from 'react'
import Layout from './components/Layout'
import { LoadingScreen } from './components/ui'
import { runDatabaseMigrations } from './services/migrationService'
import './App.css'

function App() {
  const [isInitializing, setIsInitializing] = useState(true)
  const [initError, setInitError] = useState<string | null>(null)

  useEffect(() => {
    // 简化的应用初始化
    const initializeApp = async () => {
      try {
        console.log('正在初始化应用...')
        
        // 检查桌面环境（开发期间允许浏览器环境）
        const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron;
        const isDevelopment = import.meta.env.DEV;
        
        if (isElectron) {
          console.log('桌面环境检查: 通过');
          
          // 运行数据库迁移
          try {
            console.log('正在运行数据库迁移...');
            await runDatabaseMigrations();
            console.log('数据库迁移完成');
          } catch (migrationError) {
            console.error('数据库迁移失败:', migrationError);
            // 迁移失败不阻止应用启动，只是记录错误
          }
        } else if (isDevelopment) {
          console.log('浏览器开发环境: 通过');
          // 开发环境下在浏览器中提供基本的模拟API
          if (!window.electronAPI) {
            window.electronAPI = {
              dbQuery: async () => [],
              dbRun: async () => ({ changes: 0, lastInsertRowid: 0 }),
              getAppVersion: async () => '1.0.0-browser',
              getAppPath: async () => '/browser-mode',
              openExternal: async (url: string) => { 
                window.open(url, '_blank'); 
                return { success: true }; 
              },
              showItemInFolder: async () => ({ success: false, error: '浏览器环境不支持' }),
              platform: 'browser',
              isElectron: false
            };
          }
        } else {
          setInitError('此应用只支持桌面环境运行，请使用桌面版本');
          return;
        }
        
        console.log('应用初始化完成');
      } catch (error) {
        console.error('桌面应用初始化失败:', error)
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        setInitError(`应用初始化失败: ${errorMessage}`)
      } finally {
        // 无论成功失败，都要设置初始化完成
        setTimeout(() => {
          setIsInitializing(false)
        }, 1500) // 稍微延长显示时间
      }
    }

    initializeApp()
  }, [])

  // 显示加载状态
  if (isInitializing) {
    return (
      <LoadingScreen 
        title="正在初始化应用..."
      />
    )
  }

  // 显示错误状态
  if (initError) {
    return (
      <div className="app-error" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f5f5f5',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div className="error-container" style={{
          background: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          textAlign: 'center',
          maxWidth: '500px'
        }}>
          <h2 style={{ color: '#dc3545', marginBottom: '20px' }}>应用启动失败</h2>
          <p style={{ marginBottom: '30px', color: '#666' }}>{initError}</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              padding: '12px 24px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  // 正常应用界面
  return (
    <div className="app">
      <Layout />
    </div>
  )
}

export default App
