// 数据库迁移服务

export class MigrationService {
  
  // 检查环境
  private isElectronEnvironment(): boolean {
    return typeof window !== 'undefined' && !!window.electronAPI?.isElectron;
  }

  // 执行SQL命令
  private async run(sql: string, params: any[] = []): Promise<any> {
    if (!this.isElectronEnvironment()) {
      throw new Error('此功能只在桌面环境下可用');
    }
    try {
      return await window.electronAPI!.dbRun(sql, params);
    } catch (error) {
      console.error('数据库执行失败:', error);
      throw error;
    }
  }

  // 创建预约表
  async createAppointmentsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS appointments (
        id TEXT PRIMARY KEY,
        visitor_id TEXT,
        case_id TEXT,
        visitor_name TEXT NOT NULL,
        visitor_phone TEXT,
        visitor_age INTEGER,
        visitor_gender TEXT CHECK (visitor_gender IN ('男', '女')),
        
        date TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        duration INTEGER NOT NULL,
        
        type TEXT NOT NULL CHECK (type IN ('个体咨询', '团体咨询', '家庭咨询', '沙盘疗法', '评估', '督导', '其他')),
        status TEXT NOT NULL CHECK (status IN ('待确认', '已确认', '进行中', '已完成', '已取消', '缺席')),
        urgency TEXT NOT NULL CHECK (urgency IN ('普通', '紧急', '危机干预')),
        
        room TEXT NOT NULL,
        therapist_id TEXT NOT NULL,
        therapist_name TEXT NOT NULL,
        
        subject TEXT NOT NULL,
        description TEXT,
        notes TEXT,
        
        reminder_enabled INTEGER DEFAULT 0,
        reminder_time INTEGER DEFAULT 15,
        
        is_first_session INTEGER DEFAULT 0,
        session_number INTEGER,
        total_planned_sessions INTEGER,
        
        fee REAL,
        payment_status TEXT CHECK (payment_status IN ('未支付', '已支付', '部分支付')),
        
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        created_by TEXT NOT NULL,
        
        FOREIGN KEY (visitor_id) REFERENCES visitors(id) ON DELETE SET NULL,
        FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE SET NULL
      );
    `;
    
    await this.run(sql);
    console.log('预约表创建成功');
  }

  // 创建预约表索引
  async createAppointmentsIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(date);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_visitor_id ON appointments(visitor_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_case_id ON appointments(case_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_therapist_id ON appointments(therapist_id);',
      'CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);'
    ];

    for (const indexSql of indexes) {
      await this.run(indexSql);
    }
    
    console.log('预约表索引创建成功');
  }

  // 创建每日心语表
  async createDailyMessagesTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS daily_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK (type IN ('quote', 'selfcare')),
        content TEXT NOT NULL,
        author TEXT,
        locale TEXT DEFAULT 'zh-CN',
        weight INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `;
    
    await this.run(sql);
    console.log('每日心语表创建成功');
  }

  // 创建快速备注表
  async createQuickNotesTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS quick_notes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        date TEXT NOT NULL,
        pinned INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `;
    
    await this.run(sql);
    console.log('快速备注表创建成功');
  }

  // 创建设置表（如果不存在）
  async createSettingsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        data TEXT,
        updated_at TEXT NOT NULL
      );
    `;
    
    await this.run(sql);
    console.log('设置表创建成功');
  }

  // 初始化每日心语数据
  async initializeDailyMessages(): Promise<void> {
    // 检查是否已有数据
    const existingMessages = await window.electronAPI!.dbQuery('SELECT COUNT(*) as count FROM daily_messages');
    if (existingMessages[0].count > 0) {
      console.log('每日心语数据已存在，跳过初始化');
      return;
    }

    const defaultMessages = [
      // 心理学名言
      { type: 'quote', content: '你无法阻止鸟儿在你头顶飞过，但你可以阻止它们在你头上筑巢。', author: '马丁·路德' },
      { type: 'quote', content: '接纳你无法改变的，改变你能改变的，智慧地分辨两者的区别。', author: '尼布尔' },
      { type: 'quote', content: '真正的勇气不是没有恐惧，而是明知恐惧仍然前行。', author: '纳尔逊·曼德拉' },
      { type: 'quote', content: '治愈的开始在于被看见、被听见、被理解。', author: '卡尔·荣格' },
      { type: 'quote', content: '自我关怀是一种内在的温柔，而非自我纵容。', author: '克里斯汀·奈夫' },
      { type: 'quote', content: '心理健康就像洗澡一样，需要定期维护。', author: '未知' },
      { type: 'quote', content: '没有人可以让你感到自卑，除非你同意。', author: '埃莉诺·罗斯福' },
      { type: 'quote', content: '成长意味着接受你的不完美，并依然爱自己。', author: '未知' },
      
      // 自我关怀提醒
      { type: 'selfcare', content: '记得今天给自己一些时间，哪怕只是深呼吸五分钟。' },
      { type: 'selfcare', content: '工作之余，别忘了起身活动一下，你的身体会感谢你。' },
      { type: 'selfcare', content: '今天有没有给自己一个拥抱？自我关怀从小事开始。' },
      { type: 'selfcare', content: '助人者也需要被帮助，不要害怕寻求支持。' },
      { type: 'selfcare', content: '记得给自己的情绪一些空间，它们都是有意义的。' },
      { type: 'selfcare', content: '今天不妨试试冥想，给内心一份宁静。' },
      { type: 'selfcare', content: '完美不是目标，成长才是。给自己一些耐心。' },
      { type: 'selfcare', content: '记得保持界限，说"不"也是一种自我保护。' },
    ];

    const now = new Date().toISOString();
    for (const message of defaultMessages) {
      await this.run(
        'INSERT INTO daily_messages (type, content, author, locale, weight, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [message.type, message.content, message.author || null, 'zh-CN', 1, now, now]
      );
    }

    console.log('每日心语数据初始化完成');
  }

  // 创建今日工作台相关索引
  async createDailyWorkspaceIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_daily_messages_type ON daily_messages(type);',
      'CREATE INDEX IF NOT EXISTS idx_daily_messages_locale ON daily_messages(locale);',
      'CREATE INDEX IF NOT EXISTS idx_quick_notes_date ON quick_notes(date);',
      'CREATE INDEX IF NOT EXISTS idx_quick_notes_pinned ON quick_notes(pinned);',
      'CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);'
    ];

    for (const indexSql of indexes) {
      await this.run(indexSql);
    }
    
    console.log('今日工作台索引创建成功');
  }

  // 检查表是否存在
  async checkTableExists(tableName: string): Promise<boolean> {
    if (!this.isElectronEnvironment()) {
      return false;
    }
    
    try {
      const result = await window.electronAPI!.dbQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
        [tableName]
      );
      return result.length > 0;
    } catch (error) {
      console.error('检查表存在性失败:', error);
      return false;
    }
  }

  // 运行所有迁移
  async runMigrations(): Promise<void> {
    try {
      console.log('开始数据库迁移...');
      
      // 检查预约表是否存在
      const appointmentsExists = await this.checkTableExists('appointments');
      
      if (!appointmentsExists) {
        await this.createAppointmentsTable();
        await this.createAppointmentsIndexes();
        console.log('预约功能迁移完成');
      } else {
        console.log('预约表已存在，跳过迁移');
      }

      // 检查今日工作台相关表
      const dailyMessagesExists = await this.checkTableExists('daily_messages');
      const quickNotesExists = await this.checkTableExists('quick_notes');
      const settingsExists = await this.checkTableExists('settings');

      if (!dailyMessagesExists) {
        await this.createDailyMessagesTable();
        await this.initializeDailyMessages();
        console.log('每日心语功能迁移完成');
      }

      if (!quickNotesExists) {
        await this.createQuickNotesTable();
        console.log('快速备注功能迁移完成');
      }

      if (!settingsExists) {
        await this.createSettingsTable();
        console.log('设置表迁移完成');
      }

      // 创建索引
      await this.createDailyWorkspaceIndexes();
      
      console.log('数据库迁移完成');
    } catch (error) {
      console.error('数据库迁移失败:', error);
      throw error;
    }
  }

  // 获取数据库版本信息
  async getDatabaseVersion(): Promise<string> {
    if (!this.isElectronEnvironment()) {
      return '0.0.0';
    }
    
    try {
      // 检查是否有版本表
      const versionTableExists = await this.checkTableExists('database_version');
      
      if (!versionTableExists) {
        // 创建版本表
        await this.run(`
          CREATE TABLE IF NOT EXISTS database_version (
            id INTEGER PRIMARY KEY,
            version TEXT NOT NULL,
            updated_at TEXT NOT NULL
          );
        `);
        
        // 插入初始版本
        await this.run(
          'INSERT INTO database_version (version, updated_at) VALUES (?, ?)',
          ['1.0.0', new Date().toISOString()]
        );
        
        return '1.0.0';
      }
      
      const result = await window.electronAPI!.dbQuery(
        'SELECT version FROM database_version ORDER BY id DESC LIMIT 1'
      );
      
      return result.length > 0 ? result[0].version : '0.0.0';
    } catch (error) {
      console.error('获取数据库版本失败:', error);
      return '0.0.0';
    }
  }

  // 更新数据库版本
  async updateDatabaseVersion(version: string): Promise<void> {
    if (!this.isElectronEnvironment()) {
      return;
    }
    
    try {
      await this.run(
        'INSERT INTO database_version (version, updated_at) VALUES (?, ?)',
        [version, new Date().toISOString()]
      );
      console.log(`数据库版本更新为: ${version}`);
    } catch (error) {
      console.error('更新数据库版本失败:', error);
    }
  }
}

// 导出实例
export const migrationService = new MigrationService();

// 导出迁移函数供直接调用
export const runDatabaseMigrations = async (): Promise<void> => {
  await migrationService.runMigrations();
};

export const checkDatabaseVersion = async (): Promise<string> => {
  return await migrationService.getDatabaseVersion();
};