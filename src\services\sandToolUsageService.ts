// 沙具使用记录服务
import type { SandToolUsageRecord } from '../types/sandtool';

class SandToolUsageService {
  private usageRecords: SandToolUsageRecord[] = [];
  private idCounter = 1;

  // 记录沙具使用
  async recordUsage(params: {
    toolIds: string[];
    sessionType: '个案' | '团体';
    caseId?: string;
    groupSessionId?: string;
    clientName?: string;
    therapistName: string;
    duration?: number;
    notes?: string;
  }): Promise<void> {
    const { toolIds, sessionType, caseId, groupSessionId, clientName, therapistName, duration, notes } = params;
    
    // 为每个使用的沙具创建记录
    for (const toolId of toolIds) {
      const record: SandToolUsageRecord = {
        id: `usage-${this.idCounter++}`,
        toolId,
        toolName: '', // 这里应该根据toolId查找沙具名称
        sessionId: caseId || groupSessionId,
        sessionType,
        caseId,
        groupSessionId,
        clientName,
        therapistName,
        usageDate: new Date().toISOString().split('T')[0],
        duration,
        notes,
        returnCondition: '良好' // 默认返还状态
      };
      
      this.usageRecords.push(record);
    }
    
    // 更新沙具的最后使用时间和使用次数
    await this.updateToolUsageInfo(toolIds);
  }

  // 更新沙具的使用信息
  private async updateToolUsageInfo(toolIds: string[]): Promise<void> {
    // 这里应该调用沙具服务来更新沙具信息
    // 由于当前使用的是模拟数据，我们可以发送事件或调用回调
    const today = new Date().toISOString().split('T')[0];
    
    // 触发自定义事件，通知沙具管理组件更新数据
    const event = new CustomEvent('sandToolUsed', {
      detail: { toolIds, date: today }
    });
    window.dispatchEvent(event);
  }

  // 获取沙具的使用历史
  async getToolUsageHistory(toolId: string): Promise<SandToolUsageRecord[]> {
    return this.usageRecords.filter(record => record.toolId === toolId);
  }

  // 获取个案的沙具使用记录
  async getCaseUsageRecords(caseId: string): Promise<SandToolUsageRecord[]> {
    return this.usageRecords.filter(record => record.caseId === caseId);
  }

  // 获取团体活动的沙具使用记录
  async getGroupSessionUsageRecords(groupSessionId: string): Promise<SandToolUsageRecord[]> {
    return this.usageRecords.filter(record => record.groupSessionId === groupSessionId);
  }

  // 获取所有使用记录
  async getAllUsageRecords(): Promise<SandToolUsageRecord[]> {
    return [...this.usageRecords];
  }

  // 删除使用记录
  async deleteUsageRecord(recordId: string): Promise<boolean> {
    const index = this.usageRecords.findIndex(record => record.id === recordId);
    if (index > -1) {
      this.usageRecords.splice(index, 1);
      return true;
    }
    return false;
  }

  // 获取使用统计
  async getUsageStatistics(): Promise<{
    totalUsages: number;
    mostUsedTools: Array<{ toolId: string; count: number }>;
    usageByMonth: Array<{ month: string; count: number }>;
    usageBySessionType: { 个案: number; 团体: number };
  }> {
    const totalUsages = this.usageRecords.length;
    
    // 统计最常用的沙具
    const toolUsageCount = new Map<string, number>();
    this.usageRecords.forEach(record => {
      const count = toolUsageCount.get(record.toolId) || 0;
      toolUsageCount.set(record.toolId, count + 1);
    });
    
    const mostUsedTools = Array.from(toolUsageCount.entries())
      .map(([toolId, count]) => ({ toolId, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // 按月统计使用情况
    const monthlyUsage = new Map<string, number>();
    this.usageRecords.forEach(record => {
      const month = record.usageDate.slice(0, 7); // YYYY-MM
      const count = monthlyUsage.get(month) || 0;
      monthlyUsage.set(month, count + 1);
    });
    
    const usageByMonth = Array.from(monthlyUsage.entries())
      .map(([month, count]) => ({ month, count }))
      .sort((a, b) => a.month.localeCompare(b.month));

    // 按会话类型统计
    const usageBySessionType = {
      个案: this.usageRecords.filter(r => r.sessionType === '个案').length,
      团体: this.usageRecords.filter(r => r.sessionType === '团体').length
    };

    return {
      totalUsages,
      mostUsedTools,
      usageByMonth,
      usageBySessionType
    };
  }
}

export const sandToolUsageService = new SandToolUsageService();
export default sandToolUsageService;
