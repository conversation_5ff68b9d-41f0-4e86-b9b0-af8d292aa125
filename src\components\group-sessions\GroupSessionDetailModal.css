/* 团体活动详情模态框样式 - 重新设计 */

.session-detail-wrapper {
  padding: 0;
  max-height: 75vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.therapist-name {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 信息卡片网格 */
.info-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

/* 信息卡片 */
.info-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

.card-header svg {
  color: #3b82f6;
  flex-shrink: 0;
}

.card-content {
  padding: 16px;
}

/* 信息行 */
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.info-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.info-row .label {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
  flex-shrink: 0;
}

.info-row .value {
  font-size: 13px;
  color: #1e293b;
  font-weight: 600;
  text-align: right;
}

/* 详细信息区域 */
.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.detail-section.half-width {
  flex: 1;
}

.detail-row {
  display: flex;
  gap: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  margin: 0;
}

.section-title svg {
  color: #3b82f6;
  flex-shrink: 0;
}

.section-content {
  padding: 16px;
  font-size: 14px;
  color: #475569;
  line-height: 1.6;
}

.section-content.notes {
  background: #fef3c7;
  border-left: 4px solid #f59e0b;
  color: #92400e;
}

/* 材料标签 */
.materials-tags {
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.material-tag {
  display: inline-block;
  padding: 4px 8px;
  background: #f1f5f9;
  color: #475569;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

/* 参与者网格 */
.participants-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.participant-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.participant-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.participant-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.participant-meta {
  font-size: 12px;
  color: #64748b;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  margin: 20px -24px -24px -24px;
  border-radius: 0 0 8px 8px;
}

.button-group {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-row {
    flex-direction: column;
  }
  
  .participants-grid {
    grid-template-columns: 1fr;
  }
  
  .status-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .action-buttons {
    flex-direction: column-reverse;
    gap: 12px;
  }
  
  .button-group {
    width: 100%;
    justify-content: center;
  }
  
  .participant-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 滚动条样式 */
.session-detail-wrapper::-webkit-scrollbar {
  width: 6px;
}

.session-detail-wrapper::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.session-detail-wrapper::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.session-detail-wrapper::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
