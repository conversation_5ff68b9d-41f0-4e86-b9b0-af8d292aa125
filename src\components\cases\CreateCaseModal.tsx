import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { Button } from '../ui';
import { AlertCircle } from 'lucide-react';
import type { SimpleCase } from '../../types/case';

interface CreateCaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (caseData: Omit<SimpleCase, 'id' | 'createdAt' | 'updatedAt'>) => void;
}

const CreateCaseModal: React.FC<CreateCaseModalProps> = ({ 
  isOpen, 
  onClose, 
  onSubmit 
}) => {
  console.warn('个案管理功能已被禁用');

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title="新建个案">
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '40px 20px',
        textAlign: 'center'
      }}>
        <AlertCircle size={48} style={{ color: '#f59e0b', marginBottom: '16px' }} />
        <h3 style={{ margin: '0 0 12px 0', fontSize: '18px', fontWeight: '600' }}>
          个案管理功能暂时不可用
        </h3>
        <p style={{ margin: '0 0 24px 0', color: '#6b7280', lineHeight: '1.5' }}>
          个案管理功能正在维护中，暂时无法创建新个案。
          <br />
          如有紧急需求，请联系系统管理员。
        </p>
        <Button onClick={onClose}>
          关闭
        </Button>
      </div>
    </BaseModal>
  );
};

export { CreateCaseModal };
export default CreateCaseModal;
