import type { Visitor } from '../types/visitor';
import { electronDataManager } from './electronDataManager';

export class VisitorService {
  
  // 获取所有来访者
  async getAllVisitors(): Promise<Visitor[]> {
    return await electronDataManager.getAllVisitors();
  }

  // 获取单个来访者
  async getVisitor(id: string): Promise<Visitor | null> {
    return await electronDataManager.getVisitor(id);
  }

  // 获取单个来访者(别名方法，兼容现有代码)
  async getById(id: string): Promise<Visitor | null> {
    return await this.getVisitor(id);
  }

  // 创建来访者
  async createVisitor(visitorData: Omit<Visitor, 'id' | 'createdAt' | 'updatedAt'>): Promise<Visitor> {
    const visitor: Visitor = {
      ...visitorData,
      id: `visitor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    };

    await electronDataManager.saveVisitor(visitor);
    return visitor;
  }

  // 更新来访者
  async updateVisitor(id: string, updates: Partial<Visitor>): Promise<Visitor | null> {
    const existingVisitor = await this.getVisitor(id);
    if (!existingVisitor) return null;

    const updatedVisitor: Visitor = {
      ...existingVisitor,
      ...updates,
      updatedAt: new Date().toISOString().split('T')[0]
    };

    await electronDataManager.saveVisitor(updatedVisitor);
    return updatedVisitor;
  }

  // 删除来访者
  async deleteVisitor(id: string): Promise<boolean> {
    try {
      await electronDataManager.deleteVisitor(id);
      return true;
    } catch (error) {
      console.error('Failed to delete visitor:', error);
      return false;
    }
  }

  // 搜索来访者
  async searchVisitors(query: string): Promise<Visitor[]> {
    const allVisitors = await this.getAllVisitors();
    const searchTerm = query.toLowerCase();
    
    return allVisitors.filter(visitor =>
      visitor.name.toLowerCase().includes(searchTerm) ||
      visitor.phone.includes(searchTerm) ||
      (visitor.email && visitor.email.toLowerCase().includes(searchTerm)) ||
      (visitor.occupation && visitor.occupation.toLowerCase().includes(searchTerm))
    );
  }

  // 按条件筛选来访者
  async filterVisitors(filters: {
    status?: string;
    gender?: string;
    ageRange?: string;
  }): Promise<Visitor[]> {
    const allVisitors = await this.getAllVisitors();
    let filtered = [...allVisitors];

    // 状态过滤
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(visitor => visitor.status === filters.status);
    }

    // 性别过滤
    if (filters.gender && filters.gender !== 'all') {
      filtered = filtered.filter(visitor => visitor.gender === filters.gender);
    }

    // 年龄范围过滤
    if (filters.ageRange && filters.ageRange !== 'all') {
      filtered = filtered.filter(visitor => {
        switch (filters.ageRange) {
          case 'under-25':
            return visitor.age < 25;
          case '25-35':
            return visitor.age >= 25 && visitor.age <= 35;
          case '36-45':
            return visitor.age >= 36 && visitor.age <= 45;
          case 'over-45':
            return visitor.age > 45;
          default:
            return true;
        }
      });
    }

    return filtered;
  }

  // 获取来访者统计信息
  async getVisitorStats() {
    const allVisitors = await this.getAllVisitors();
    
    return {
      total: allVisitors.length,
      active: allVisitors.filter(v => v.status === '活跃').length,
      paused: allVisitors.filter(v => v.status === '暂停').length,
      completed: allVisitors.filter(v => v.status === '完成').length,
      male: allVisitors.filter(v => v.gender === '男').length,
      female: allVisitors.filter(v => v.gender === '女').length
    };
  }
}

// 单例实例
export const visitorService = new VisitorService();
