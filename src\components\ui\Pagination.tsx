import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { clsx } from 'clsx';
import './Pagination.css';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showSizeChanger?: boolean;
  pageSize?: number;
  onPageSizeChange?: (size: number) => void;
  pageSizeOptions?: number[];
  showQuickJumper?: boolean;
  showTotal?: boolean;
  total?: number;
  className?: string;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showSizeChanger = false,
  pageSize = 12,
  onPageSizeChange,
  pageSizeOptions = [10, 12, 20, 50],
  showQuickJumper = false,
  showTotal = true,
  total = 0,
  className
}) => {
  const [jumpValue, setJumpValue] = React.useState('');

  // 生成页码数组
  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisible = 7; // 最多显示7个页码

    if (totalPages <= maxVisible) {
      // 如果总页数小于等于最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 复杂分页逻辑
      if (currentPage <= 4) {
        // 当前页在前面
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // 当前页在后面
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const handleJump = () => {
    const page = parseInt(jumpValue);
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
      setJumpValue('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJump();
    }
  };

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className={clsx('pagination-container', className)}>
      {/* 总数显示 */}
      {showTotal && (
        <div className="pagination-total">
          共 {total} 条数据
        </div>
      )}

      {/* 分页控件 */}
      <div className="pagination-controls">
        {/* 上一页 */}
        <button
          className={clsx('pagination-btn', 'pagination-prev', {
            'disabled': currentPage === 1
          })}
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          title="上一页"
        >
          <ChevronLeft size={16} />
        </button>

        {/* 页码 */}
        <div className="pagination-pages">
          {generatePageNumbers().map((page, index) => {
            if (page === '...') {
              return (
                <span key={`ellipsis-${index}`} className="pagination-ellipsis">
                  <MoreHorizontal size={16} />
                </span>
              );
            }

            return (
              <button
                key={page}
                className={clsx('pagination-page', {
                  'active': page === currentPage
                })}
                onClick={() => onPageChange(page as number)}
              >
                {page}
              </button>
            );
          })}
        </div>

        {/* 下一页 */}
        <button
          className={clsx('pagination-btn', 'pagination-next', {
            'disabled': currentPage === totalPages
          })}
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          title="下一页"
        >
          <ChevronRight size={16} />
        </button>
      </div>

      {/* 每页条数选择器 */}
      {showSizeChanger && onPageSizeChange && (
        <div className="pagination-size-changer">
          <span className="size-changer-label">每页</span>
          <select
            value={pageSize}
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            className="size-changer-select"
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
          <span className="size-changer-label">条</span>
        </div>
      )}

      {/* 快速跳转 */}
      {showQuickJumper && (
        <div className="pagination-jumper">
          <span className="jumper-label">跳至</span>
          <input
            type="number"
            min={1}
            max={totalPages}
            value={jumpValue}
            onChange={(e) => setJumpValue(e.target.value)}
            onKeyPress={handleKeyPress}
            className="jumper-input"
            placeholder="页码"
          />
          <button
            onClick={handleJump}
            className="jumper-btn"
            disabled={!jumpValue}
          >
            确定
          </button>
        </div>
      )}
    </div>
  );
};

export { Pagination };