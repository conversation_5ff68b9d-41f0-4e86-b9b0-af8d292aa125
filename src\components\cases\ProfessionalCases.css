/* 专业个案管理样式 */

/* 页面头部样式 */
.professional-cases-header {
  border-bottom: 2px solid #e2e8f0;
  margin-bottom: var(--spacing-xl);
}

.professional-cases-header .page-title {
  color: #1e293b;
  font-weight: 600;
  font-size: 28px;
}

.professional-cases-header .page-subtitle {
  color: #64748b;
  font-size: 16px;
  margin-top: 4px;
}

/* 统计概览样式 */
.stats-overview {
  margin-bottom: var(--spacing-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item.danger {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #fca5a5;
}

.stat-item.warning {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #fbbf24;
}

.stat-item.info {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #60a5fa;
}

.stat-item.success {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border: 1px solid #4ade80;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-item.danger .stat-number { color: var(--primary-blue); }
.stat-item.warning .stat-number { color: #d97706; }
.stat-item.info .stat-number { color: var(--primary-blue); }
.stat-item.success .stat-number { color: #059669; }

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 筛选器样式 */
.professional-filters {
  margin-bottom: var(--spacing-lg);
}

.filter-row {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-end;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 300px;
}

.filter-controls {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.filter-controls > * {
  min-width: 140px;
}

/* 批量操作工具栏样式 */
.batch-toolbar {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  margin-bottom: var(--spacing-lg);
}

.batch-toolbar .card-content {
  padding: 12px 16px;
}

/* 专业个案表格样式 */
.professional-cases-table {
  font-size: 14px;
}

.professional-cases-table thead {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.professional-cases-table th {
  font-weight: 600;
  color: #374151;
  padding: 12px 8px;
  border-bottom: 2px solid #e5e7eb;
  text-align: left;
}

.professional-cases-table td {
  padding: 12px 8px;
  vertical-align: middle;
  border-bottom: 1px solid #f3f4f6;
}

/* 表格行状态样式 */
.professional-cases-table tbody tr {
  transition: all 0.2s ease;
}

.professional-cases-table tbody tr:nth-child(even) {
  background-color: #f8fafc !important;
}

.professional-cases-table tbody tr:hover {
  background-color: #f1f5f9 !important;
}

.professional-cases-table tbody tr:nth-child(even):hover {
  background-color: #f1f5f9 !important;
}

.professional-cases-table tbody tr.selected {
  background-color: #eff6ff !important;
  border-color: #3b82f6;
}

.professional-cases-table tbody tr.selected:hover {
  background-color: #dbeafe !important;
}

.professional-cases-table tbody tr.overdue-row {
  background-color: #f8fafc !important;
}

.professional-cases-table tbody tr.overdue-row:hover {
  background-color: #f1f5f9 !important;
}

.professional-cases-table tbody tr.high-risk-row {
  background-color: #f8fafc !important;
}

.professional-cases-table tbody tr.high-risk-row:hover {
  background-color: #f1f5f9 !important;
}

/* 选择按钮样式 */
.select-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.select-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* 来访者信息样式 */
.visitor-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.visitor-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #1f2937;
}

.star-icon {
  color: #f59e0b;
  fill: currentColor;
}

/* 问题摘要样式 */
.problem-summary {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #4b5563;
  cursor: help;
}

.problem-summary:hover {
  color: #1f2937;
}

/* 治疗方法样式 */
.therapy-method {
  font-size: 12px;
  font-weight: 500;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
}

/* 危机等级徽章样式 */
.crisis-badge {
  font-weight: 600;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 进展状态徽章样式 */
.progress-badge {
  font-weight: 600;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 最后咨询时间样式 */
.last-session {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #6b7280;
}

.last-session.overdue {
  color: #2563eb;
  font-weight: 500;
  background-color: #eff6ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #bfdbfe;
}

/* 下次预约样式 */
.next-appointment {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #059669;
}

.no-appointment {
  color: #9ca3af;
  font-style: italic;
}

/* 咨询次数样式 */
.session-count {
  font-weight: 500;
  color: #374151;
  text-align: center;
  padding: 4px 8px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons .button {
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-buttons .button:hover {
  background-color: #f3f4f6;
}

.delete-button:hover {
  background-color: #eff6ff !important;
  color: #2563eb !important;
}

/* 分页样式 */
.pagination-wrapper {
  padding: var(--spacing-lg);
  border-top: 1px solid #e5e7eb;
  background: #fafafa;
}

/* 表格列宽设置 */
.professional-cases-table {
  table-layout: fixed;
  width: 100%;
}

.professional-cases-table th:nth-child(1),
.professional-cases-table td:nth-child(1) {
  width: 50px; /* 选择框 */
}

.professional-cases-table th:nth-child(2),
.professional-cases-table td:nth-child(2) {
  width: 120px; /* 来访者 */
}

.professional-cases-table th:nth-child(3),
.professional-cases-table td:nth-child(3) {
  width: 180px; /* 问题类型 */
}

.professional-cases-table th:nth-child(4),
.professional-cases-table td:nth-child(4) {
  width: 130px; /* 治疗方法 */
}

.professional-cases-table th:nth-child(5),
.professional-cases-table td:nth-child(5) {
  width: 100px; /* 危机等级 */
}

.professional-cases-table th:nth-child(6),
.professional-cases-table td:nth-child(6) {
  width: 100px; /* 治疗进展 */
}

.professional-cases-table th:nth-child(7),
.professional-cases-table td:nth-child(7) {
  width: 100px; /* 最后咨询 */
}

.professional-cases-table th:nth-child(8),
.professional-cases-table td:nth-child(8) {
  width: 100px; /* 下次预约 */
}

.professional-cases-table th:nth-child(9),
.professional-cases-table td:nth-child(9) {
  width: 80px; /* 总次数 */
}

.professional-cases-table th:nth-child(10),
.professional-cases-table td:nth-child(10) {
  width: 120px; /* 操作 */
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .professional-cases-table {
    font-size: 12px;
  }
  
  .professional-cases-table th,
  .professional-cases-table td {
    padding: 8px 4px;
  }
  
  .filter-controls {
    grid-template-columns: 1fr;
  }
  
  .filter-controls > * {
    min-width: auto;
  }
}

/* 专业主题色彩 */
:root {
  --crisis-high: #2563eb;
  --crisis-medium: #d97706;
  --crisis-low: #059669;
  --progress-improve: #059669;
  --progress-stable: #2563eb;
  --progress-decline: #3b82f6;
  --overdue-warning: #3b82f6;
  --professional-primary: #1e40af;
  --professional-secondary: #64748b;
}
