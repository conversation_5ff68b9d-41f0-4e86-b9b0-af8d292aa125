const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 安全地暴露 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 数据库操作
  dbQuery: (sql, params) => ipcRenderer.invoke('db-query', sql, params),
  dbRun: (sql, params) => ipcRenderer.invoke('db-run', sql, params),
  
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: (name) => ipcRenderer.invoke('get-app-path', name),
  
  // 浏览器功能
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showItemInFolder: (path) => ipcRenderer.invoke('show-item-in-folder', path),
  
  // 系统通知
  showNotification: (title, options) => ipcRenderer.invoke('show-notification', title, options),
  
  // 平台信息
  platform: process.platform,
  isElectron: true
});

// 窗口加载完成后通知主进程
window.addEventListener('DOMContentLoaded', () => {
  console.log('Electron preload script loaded');
});
