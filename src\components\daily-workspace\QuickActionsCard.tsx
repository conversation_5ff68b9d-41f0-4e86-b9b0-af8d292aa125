import React, { useState, useEffect } from 'react';
import { Plus, Calendar, Users, Wrench, StickyNote, FileText } from 'lucide-react';
import { notesService } from '../../services';
import { MockBrowserServices } from '../../data/mockDailyWorkspace';

// 检测是否在浏览器环境
const isBrowser = typeof window !== 'undefined' && !window.electronAPI;

export interface QuickActionsCardProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help' | 'dev-browser') => void;
}

export const QuickActionsCard: React.FC<QuickActionsCardProps> = ({ onNavigate }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [noteContent, setNoteContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [latestNote, setLatestNote] = useState<string | null>(null);

  // 快捷操作配置
  const quickActions = [
    { 
      icon: Calendar, 
      label: '新增预约', 
      color: '#3b82f6',
      page: 'schedule' as const
    },
    { 
      icon: Users, 
      label: '添加来访者', 
      color: '#10b981',
      page: 'visitors' as const
    },
    { 
      icon: FileText, 
      label: '创建个案', 
      color: '#f59e0b',
      page: 'cases' as const
    },
    { 
      icon: Wrench, 
      label: '沙具管理', 
      color: '#8b5cf6',
      page: 'sandtools' as const
    }
  ];

  // 获取最新笔记
  const loadLatestNote = async () => {
    try {
      if (isBrowser) {
        const note = await MockBrowserServices.getLatestQuickNote();
        setLatestNote(note ? note.content : null);
      } else {
        const today = new Date().toISOString().split('T')[0];
        const note = await notesService.getLatestQuickNote(today);
        setLatestNote(note ? note.content : null);
      }
    } catch (error) {
      console.error('获取最新笔记失败:', error);
    }
  };

  // 保存紧急笔记
  const handleSaveNote = async () => {
    if (!noteContent.trim()) return;

    try {
      setIsSubmitting(true);
      
      if (isBrowser) {
        await MockBrowserServices.addQuickNote(noteContent.trim());
      } else {
        const today = new Date().toISOString().split('T')[0];
        await notesService.addQuickNote({
          content: noteContent.trim(),
          date: today,
          pinned: false
        });
      }
      
      setNoteContent('');
      setIsModalOpen(false);
      
      // 刷新最新笔记显示
      await loadLatestNote();
    } catch (error) {
      console.error('保存笔记失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理快捷操作点击
  const handleActionClick = (page: string) => {
    if (onNavigate && page !== 'emergency-note') {
      onNavigate(page as any);
    } else if (page === 'emergency-note') {
      setIsModalOpen(true);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadLatestNote();
  }, []);

  return (
    <div className="daily-workspace-card">
      <div className="card-header">
        <div className="card-title">
          <Plus className="title-icon" />
          <span>快捷操作</span>
        </div>
      </div>

      <div className="card-content">
        {/* 快捷操作按钮 */}
        <div className="quick-actions-grid">
          {quickActions.map((action, index) => {
            const IconComponent = action.icon;
            return (
              <button
                key={index}
                className="quick-action-btn"
                onClick={() => handleActionClick(action.page)}
                style={{ '--action-color': action.color } as React.CSSProperties}
              >
                <IconComponent className="action-icon" />
                <span className="action-label">{action.label}</span>
              </button>
            );
          })}
        </div>

        {/* 紧急笔记按钮 */}
        <button 
          className="emergency-note-btn"
          onClick={() => setIsModalOpen(true)}
        >
          <StickyNote className="note-icon" />
          <span>紧急笔记</span>
        </button>

        {/* 最新笔记预览 */}
        {latestNote && (
          <div className="latest-note-preview">
            <div className="note-preview-header">
              <StickyNote className="preview-icon" />
              <span>最新笔记</span>
            </div>
            <div className="note-preview-content">
              {latestNote.length > 50 ? `${latestNote.substring(0, 50)}...` : latestNote}
            </div>
          </div>
        )}
      </div>

      {/* 紧急笔记模态框 */}
      {isModalOpen && (
        <div className="modal-overlay" onClick={() => setIsModalOpen(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>紧急笔记</h3>
              <button 
                className="modal-close"
                onClick={() => setIsModalOpen(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <textarea
                className="note-textarea"
                placeholder="记录紧急事项、重要提醒..."
                value={noteContent}
                onChange={(e) => setNoteContent(e.target.value)}
                autoFocus
              />
            </div>
            <div className="modal-footer">
              <button
                className="modal-btn modal-btn-cancel"
                onClick={() => setIsModalOpen(false)}
              >
                取消
              </button>
              <button
                className="modal-btn modal-btn-save"
                onClick={handleSaveNote}
                disabled={!noteContent.trim() || isSubmitting}
              >
                {isSubmitting ? '保存中...' : '保存'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
