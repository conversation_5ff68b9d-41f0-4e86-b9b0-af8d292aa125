/* 简洁风格日历组件 */
.liquid-calendar {
  position: relative;
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e2e8f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 悬停效果 - 简洁风格 */
.liquid-calendar:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: #cbd5e1;
}

/* 简洁淡入动画 */
@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(10px);
  }
  to { 
    opacity: 1; 
    transform: translateY(0);
  }
}

/* 移除背景装饰元素，保持简洁 */

/* 日历格子简洁动画 */
.calendar-day {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* 预约项简洁动画 */
.appointment-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  transform: translateX(0);
}

/* 日历头部 */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

.calendar-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.calendar-icon {
  width: 24px;
  height: 24px;
  color: rgba(96, 165, 250, 0.8);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.calendar-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-left: 8px;
}

/* 导航控件 */
.calendar-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background: #e9e9e9;
  color: #333;
  transform: translateY(-1px);
}

.nav-btn:active {
  transform: translateY(0) scale(0.98);
}

.current-month {
  font-size: 15px;
  font-weight: 500;
  color: #444;
  min-width: 120px;
  text-align: center;
}

/* 星期标题 */
.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

.weekday {
  padding: 10px 8px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 日历网格 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  position: relative;
  z-index: 1;
  padding: 8px;
}

/* 日历日期单元格 */
.calendar-day {
  position: relative;
  aspect-ratio: 1/1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 8px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}

.calendar-day::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.calendar-day:hover::before {
  opacity: 1;
}

.calendar-day:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: #4a90e2;
  /* transform: translateY(-3px) scale(1.02); */
  box-shadow: 
    0 6px 16px rgba(74, 144, 226, 0.2),
    inset 0 0 0 1px rgba(255, 255, 255, 0.8);
  z-index: 2;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 当前月份的日期 */
.calendar-day.today {
  background: linear-gradient(135deg, #e6f0ff, #f0f7ff);
  border: 2px solid #4a90e2;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
  position: relative;
  z-index: 1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(74, 144, 226, 0); }
  100% { box-shadow: 0 0 0 0 rgba(74, 144, 226, 0); }
}

.calendar-day.today .day-number {
  color: #0066cc;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.calendar-day.today::before {
  content: '';
  position: absolute;
  top: 4px;
  right: 4px;
  width: 6px;
  height: 6px;
  background: #0066cc;
  border-radius: 50%;
  z-index: 0;
}

/* 其他月份的日期 */
.calendar-day.other-month {
  opacity: 0.4;
  background: #fafafa;
}

.calendar-day.other-month .day-number {
  color: rgba(96, 165, 250, 0.3);
}

/* 日期数字 */
.day-number {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

/* 预约容器 */
.appointments-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-height: 48px;
  overflow: hidden;
}

/* 预约指示器 */
.appointment-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.appointment-item:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-color: #e0e0e0;
}

.appointment-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.appointment-indicator:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.appointment-time {
  font-size: 12px;
  font-weight: 500;
  color: #4a90e2;
  white-space: nowrap;
  background: #f0f7ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-feature-settings: 'tnum';
}

.appointment-meta {
.appointment-item:hover {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.appointment-time-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #3B82F6;
  font-weight: 600;
  font-size: 14px;
  min-width: 120px;
}

.appointment-details {
  flex: 1;
}

.appointment-subject {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.appointment-visitor {
  color: #64748b;
  font-size: 14px;
  margin-bottom: 4px;
}

.appointment-room {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #64748b;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .liquid-calendar {
    padding: 16px;
    border-radius: 16px;
  }
  
  .calendar-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .calendar-navigation {
    justify-content: center;
  }
  
  .calendar-day {
    min-height: 60px;
    padding: 4px;
  }
  
  .day-number {
    font-size: 14px;
  }
  
  .appointment-indicator {
    padding: 1px 4px;
    font-size: 8px;
  }
  
  .appointment-time {
    font-size: 8px;
  }
  
  .selected-date-info {
    padding: 12px;
  }
  
  .appointment-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .appointment-time-info {
    min-width: auto;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .liquid-calendar {
    padding: 12px;
  }
  
  .calendar-day {
    min-height: 50px;
    padding: 2px;
  }
  
  .day-number {
    font-size: 12px;
  }
  
  .weekday {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .appointment-indicator {
    display: none; /* 在极小屏幕上隐藏预约指示器，只显示点 */
  }
  
  .calendar-day.has-appointments::after {
    width: 6px;
    height: 6px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .liquid-calendar {
    background: linear-gradient(135deg, 
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.2) 50%,
      rgba(0, 0, 0, 0.3) 100%
    );
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .calendar-title h3,
  .current-month,
  .calendar-day.current-month .day-number {
    color: #f1f5f9;
  }
  
  .weekday {
    color: #94a3b8;
  }
  
  .selected-date-info h4,
  .appointment-subject {
    color: #f1f5f9;
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .liquid-calendar {
    border-width: 2px;
  }
  
  .calendar-day {
    border-width: 2px;
  }
  
  .appointment-indicator {
    border-width: 2px;
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  .liquid-calendar,
  .liquid-calendar::before,
  .calendar-day,
  .nav-btn,
  .appointment-indicator,
  .appointment-item {
    animation: none;
    transition: none;
  }
  
  .calendar-day.has-appointments::after {
    animation: none;
  }
}