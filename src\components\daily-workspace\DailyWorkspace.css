/* 今日心灵工作台样式 */
.daily-workspace {
  padding: 28px;
  background: #ffffff;
  border-radius: 8px;
  margin: 28px 0;
  border: 1px solid #e5e7eb;
}

.daily-workspace-header {
  text-align: left;
  margin-bottom: 24px;
}

.daily-workspace-header h2 {
  font-size: 20px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 8px 0;
}

.daily-workspace-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

.daily-workspace-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  align-items: start;
}

/* 卡片通用样式 */
.workspace-card {
  background: white;
  border-radius: 6px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  height: fit-content;
}

.workspace-card:hover {
  border-color: #d1d5db;
}

.workspace-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.workspace-card-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  color: #6b7280;
  flex-shrink: 0;
}

.workspace-card-title {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin: 0;
  flex: 1;
}

.workspace-card-content {
  color: #6b7280;
  line-height: 1.6;
  font-size: 14px;
}

/* 今日安排卡片特定样式 */
.schedule-card {
  grid-column: span 1;
}

.schedule-card .workspace-card-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.schedule-countdown {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.countdown-time {
  font-size: 2rem;
  font-weight: 700;
  color: #3b82f6;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.25rem;
}

.countdown-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.schedule-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.schedule-stat {
  flex: 1;
  text-align: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
}

.schedule-stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.schedule-stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.next-visitor {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 1rem;
}

.next-visitor-name {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 0.25rem;
}

.next-visitor-info {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.no-appointments {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-appointments-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.create-appointment-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.create-appointment-btn:hover {
  background: #2563eb;
}

/* 预约即将开始的警告样式 */
.schedule-card.upcoming-warning {
  border-color: #fbbf24;
  box-shadow: 0 0 0 1px #fbbf24, 0 4px 6px rgba(0, 0, 0, 0.1);
}

.schedule-card.upcoming-warning .countdown-time {
  color: #f59e0b;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 每日心语卡片特定样式 */
.message-card .workspace-card-icon {
  background: linear-gradient(135deg, #ec4899, #be185d);
  color: white;
}

.daily-message {
  margin-bottom: 1.5rem;
}

.message-quote {
  background: #fdf2f8;
  border-left: 4px solid #ec4899;
  padding: 1rem;
  border-radius: 0 8px 8px 0;
  margin-bottom: 1rem;
}

.message-content {
  font-style: italic;
  color: #1f2937;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.message-author {
  text-align: right;
  font-size: 0.875rem;
  color: #6b7280;
}

.message-selfcare {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  padding: 1rem;
  border-radius: 8px;
  color: #0c4a6e;
  line-height: 1.5;
}

.refresh-message-btn {
  background: #ec4899;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.refresh-message-btn:hover {
  background: #db2777;
}

.refresh-message-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 快速操作卡片特定样式 */
.actions-card .workspace-card-icon {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quick-action-btn {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quick-action-btn:hover {
  border-color: #10b981;
  background: #f0fdf4;
}

.quick-action-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #10b981;
  flex-shrink: 0;
}

.quick-action-text {
  flex-grow: 1;
}

.quick-action-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.quick-action-desc {
  font-size: 0.75rem;
  color: #6b7280;
}

.quick-note-preview {
  background: #fffbeb;
  border: 1px solid #fed7aa;
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 1rem;
}

.quick-note-time {
  font-size: 0.75rem;
  color: #92400e;
  margin-bottom: 0.25rem;
}

.quick-note-content {
  font-size: 0.875rem;
  color: #1f2937;
  line-height: 1.4;
}

/* 心情追踪卡片样式 */
.mood-tracker-card .workspace-card-icon {
  background: #fef2f2;
  color: #dc2626;
}

.mood-display {
  text-align: center;
}

.current-mood {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  font-weight: 400;
}

.mood-label {
  font-size: 16px;
}

.mood-note {
  background: #f9fafb;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
  font-style: italic;
  color: #6b7280;
}

.mood-prompt {
  text-align: center;
  margin-bottom: 16px;
  font-weight: 500;
  color: #374151;
}

.mood-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.mood-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 6px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #ffffff;
  cursor: pointer;
  font-size: 12px;
}

.mood-option:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.mood-option.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.mood-note-input {
  margin-top: 20px;
}

.mood-note-input textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  resize: vertical;
  min-height: 80px;
  font-size: 14px;
  line-height: 1.5;
}

.mood-actions {
  display: flex;
  gap: 6px;
  margin-top: 12px;
}

.save-mood-btn, .update-mood-btn {
  flex: 1;
  padding: 8px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.save-mood-btn:hover, .update-mood-btn:hover {
  background: #2563eb;
}

.cancel-mood-btn {
  flex: 1;
  padding: 8px 12px;
  background: #ffffff;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-mood-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

/* 正念练习卡片样式 */
.mindfulness-card .workspace-card-icon {
  background: #f0f9ff;
  color: #0369a1;
}

.session-count {
  font-size: 12px;
  color: #10b981;
  background: #ecfdf5;
  padding: 3px 6px;
  border-radius: 8px;
  margin-left: auto;
}

.mindfulness-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mindfulness-option {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #ffffff;
  cursor: pointer;
  text-align: left;
}

.mindfulness-option:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.option-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
}

.option-duration {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.option-description {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
}

.active-session {
  text-align: center;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.session-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.session-timer {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.breathing-guide {
  margin: 32px 0;
}

.breathing-circle {
  width: 80px;
  height: 80px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.breathing-text {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  text-align: center;
}

.meditation-guide, .gratitude-guide {
  margin: 24px 0;
}

.meditation-text, .gratitude-text {
  text-align: center;
}

.meditation-text p, .gratitude-text p {
  margin: 8px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
}

.session-controls {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.control-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
}

.control-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.stop-btn {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.stop-btn:hover {
  background: #fee2e2;
}

/* 成就卡片样式 */
.achievement-card .workspace-card-icon {
  background: #fef3c7;
  color: #d97706;
}

.achievement-summary {
  font-size: 12px;
  color: #10b981;
  background: #ecfdf5;
  padding: 3px 6px;
  border-radius: 8px;
  margin-left: auto;
}

.daily-progress {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.progress-bar {
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #ffffff;
  border: 1px solid #f3f4f6;
  border-radius: 4px;
}

.achievement-item.completed {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.achievement-icon-wrapper {
  position: relative;
  flex-shrink: 0;
}

.achievement-emoji {
  font-size: 20px;
  display: block;
}

.completion-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 14px;
  height: 14px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e7eb;
}

.achievement-info {
  flex: 1;
  min-width: 0;
}

.achievement-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.achievement-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.achievement-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar-small {
  flex: 1;
  height: 3px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill-small {
  height: 100%;
  border-radius: 2px;
}

.progress-text {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

.quick-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 6px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #6b7280;
}

.stat-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.celebration {
  text-align: center;
  padding: 12px;
  background: #fef3c7;
  border-radius: 4px;
  margin-top: 12px;
}

.celebration-message {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
  margin: 0;
}

/* 健康贴士卡片样式 */
.wellbeing-card .workspace-card-icon {
  background: #f0fdf4;
  color: #059669;
}

.refresh-tip-btn {
  padding: 4px;
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  margin-left: auto;
}

.refresh-tip-btn:hover {
  background: #f9fafb;
  color: #374151;
}

.tip-content {
  margin-bottom: 20px;
}

.tip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tip-category {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 3px 6px;
  border-radius: 8px;
}

.tip-emoji {
  font-size: 24px;
}

.tip-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.tip-text {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.tip-action-btn {
  width: 100%;
  padding: 8px 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 12px;
}

.tip-action-btn:hover {
  background: #2563eb;
}

.tip-footer {
  text-align: center;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
}

.tip-hint {
  font-size: 11px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .workspace-cards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .workspace-card {
    padding: 16px;
  }
  
  .workspace-card-header h3 {
    font-size: 16px;
  }
  
  .workspace-card-header p {
    font-size: 13px;
  }
  
  /* 心情追踪卡片响应式 */
  .mood-options {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  .mood-option {
    padding: 8px;
  }
  
  .mood-emoji {
    font-size: 20px;
  }
  
  /* 正念卡片响应式 */
  .mindfulness-options {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .mindfulness-option {
    padding: 12px;
  }
  
  /* 成就卡片响应式 */
  .achievements-list {
    gap: 8px;
  }
  
  .achievement-item {
    padding: 10px;
  }
  
  .achievement-emoji {
    font-size: 20px;
  }
  
  .quick-stats {
    gap: 6px;
  }
  
  .stat-item {
    padding: 6px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .daily-workspace {
    margin: 1rem 0;
    padding: 0 0.5rem;
  }
  
  .daily-workspace-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .schedule-card {
    grid-column: span 1;
  }
  
  .workspace-card {
    padding: 1rem;
  }
  
  .schedule-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .countdown-time {
    font-size: 1.5rem;
  }
  
  .workspace-card-header h3 {
    font-size: 15px;
  }
  
  .workspace-card-header p {
    font-size: 12px;
  }
  
  .workspace-card-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  /* 心情追踪卡片移动端 */
  .mood-options {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }
  
  .mood-option {
    padding: 6px;
    font-size: 11px;
  }
  
  .mood-emoji {
    font-size: 18px;
  }
  
  .mood-note-input {
    font-size: 13px;
    padding: 8px;
  }
  
  .mood-actions {
    gap: 8px;
  }
  
  .save-btn, .update-btn, .cancel-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  /* 正念卡片移动端 */
  .session-timer {
    font-size: 20px;
  }
  
  .session-controls {
    gap: 8px;
  }
  
  .start-btn, .pause-btn, .stop-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  /* 成就卡片移动端 */
  .achievement-item {
    padding: 8px;
    gap: 8px;
  }
  
  .achievement-emoji {
    font-size: 18px;
  }
  
  .achievement-title {
    font-size: 13px;
  }
  
  .achievement-description {
    font-size: 11px;
  }
  
  .progress-text {
    font-size: 10px;
  }
  
  .quick-stats {
    gap: 4px;
  }
  
  .stat-item {
    padding: 4px;
    font-size: 10px;
  }
  
  /* 健康贴士卡片移动端 */
  .tip-title {
    font-size: 14px;
  }
  
  .tip-text {
    font-size: 13px;
  }
  
  .tip-action-btn {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .tip-category {
    font-size: 11px;
    padding: 3px 6px;
  }
}

@media (max-width: 480px) {
  .daily-workspace-section {
    padding: 12px;
  }
  
  .workspace-section-header h2 {
    font-size: 20px;
  }
  
  .workspace-section-header p {
    font-size: 13px;
  }
  
  .workspace-card {
    padding: 12px;
  }
  
  .workspace-card-header h3 {
    font-size: 14px;
  }
  
  .workspace-card-header p {
    font-size: 11px;
  }
  
  /* 心情追踪卡片小屏幕 */
  .mood-options {
    grid-template-columns: 1fr;
    gap: 4px;
  }
  
  .mood-option {
    padding: 8px;
    font-size: 12px;
  }
  
  .mood-emoji {
    font-size: 16px;
  }
  
  /* 正念卡片小屏幕 */
  .session-timer {
    font-size: 18px;
  }
  
  .breathing-guide {
    width: 60px;
    height: 60px;
  }
  
  /* 成就卡片小屏幕 */
  .achievement-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .achievement-icon-wrapper {
    align-self: center;
  }
  
  .achievement-info {
    text-align: center;
  }
  
  .quick-stats {
    flex-direction: column;
    gap: 4px;
  }
  
  .stat-item {
    justify-content: flex-start;
    padding: 6px 8px;
  }
}

/* 加载状态 */
.workspace-card.loading {
  opacity: 0.7;
}

.loading-skeleton {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  height: 1rem;
  margin-bottom: 0.5rem;
}

.loading-skeleton.wide {
  width: 100%;
}

.loading-skeleton.medium {
  width: 70%;
}

.loading-skeleton.narrow {
  width: 50%;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
