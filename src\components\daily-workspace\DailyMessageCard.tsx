import React, { useState, useEffect } from 'react';
import { Heart, RefreshCw } from 'lucide-react';
import { dailyMessageService } from '../../services';
import { MockBrowserServices } from '../../data/mockDailyWorkspace';
import type { DailyMessage } from '../../services/dailyMessageService';

// 检测是否在浏览器环境
const isBrowser = typeof window !== 'undefined' && !window.electronAPI;

export interface DailyMessageCardProps {}

export const DailyMessageCard: React.FC<DailyMessageCardProps> = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [messages, setMessages] = useState<{
    quote: DailyMessage | null;
    selfcare: DailyMessage | null;
  }>({
    quote: null,
    selfcare: null
  });

  // 获取今日消息
  const loadTodayMessages = async () => {
    try {
      setLoading(true);
      
      if (isBrowser) {
        // 浏览器环境使用模拟数据
        const message = await MockBrowserServices.getTodayMessage();
        // 将模拟消息转换为DailyMessage格式
        const dailyMessage: DailyMessage = {
          id: message.id,
          type: message.category === 'motivation' ? 'quote' : 'selfcare',
          content: message.message,
          locale: 'zh-CN',
          weight: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        // 根据类型设置消息
        if (message.category === 'motivation') {
          setMessages({
            quote: dailyMessage,
            selfcare: null
          });
        } else {
          setMessages({
            quote: null,
            selfcare: dailyMessage
          });
        }
      } else {
        // Electron环境使用真实服务
        const today = new Date().toISOString().split('T')[0];
        const todayMessages = await dailyMessageService.getTodayMessage(today);
        setMessages(todayMessages);
      }
    } catch (error) {
      console.error('加载今日心语失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新心语
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      
      if (isBrowser) {
        // 浏览器环境使用模拟数据
        const message = await MockBrowserServices.refreshTodayMessage();
        const dailyMessage: DailyMessage = {
          id: message.id,
          type: message.category === 'motivation' ? 'quote' : 'selfcare',
          content: message.message,
          locale: 'zh-CN',
          weight: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        // 根据类型更新消息
        if (message.category === 'motivation') {
          setMessages(prev => ({
            ...prev,
            quote: dailyMessage
          }));
        } else {
          setMessages(prev => ({
            ...prev,
            selfcare: dailyMessage
          }));
        }
      } else {
        // Electron环境使用真实服务
        const today = new Date().toISOString().split('T')[0];
        const refreshedMessage = await dailyMessageService.refreshTodayMessage(today);
        
        if (refreshedMessage) {
          // 更新对应类型的消息
          setMessages(prev => ({
            ...prev,
            [refreshedMessage.type]: refreshedMessage
          }));
        }
      }
    } catch (error) {
      console.error('刷新心语失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadTodayMessages();
  }, []);

  if (loading) {
    return (
      <div className="workspace-card message-card loading">
        <div className="workspace-card-header">
          <div className="workspace-card-icon">
            <Heart size={20} />
          </div>
          <h3 className="workspace-card-title">每日心语</h3>
        </div>
        <div className="workspace-card-content">
          <div className="loading-skeleton wide"></div>
          <div className="loading-skeleton medium"></div>
          <div className="loading-skeleton narrow"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="workspace-card message-card">
      <div className="workspace-card-header">
        <div className="workspace-card-icon">
          <Heart size={20} />
        </div>
        <h3 className="workspace-card-title">每日心语</h3>
      </div>
      
      <div className="workspace-card-content">
        <div className="daily-message">
          {/* 心理学名言 */}
          {messages.quote && (
            <div className="message-quote">
              <div className="message-content">
                "{messages.quote.content}"
              </div>
              {messages.quote.author && (
                <div className="message-author">
                  — {messages.quote.author}
                </div>
              )}
            </div>
          )}

          {/* 自我关怀提醒 */}
          {messages.selfcare && (
            <div className="message-selfcare">
              💝 {messages.selfcare.content}
            </div>
          )}

          {/* 无消息时的提示 */}
          {!messages.quote && !messages.selfcare && (
            <div className="no-appointments">
              <div className="no-appointments-icon">💭</div>
              <div>暂无心语内容</div>
            </div>
          )}
        </div>

        {/* 刷新按钮 */}
        <button 
          className="refresh-message-btn"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw 
            size={16} 
            className={refreshing ? 'animate-spin' : ''} 
          />
          {refreshing ? '正在刷新...' : '换一句话'}
        </button>
      </div>
    </div>
  );
};
