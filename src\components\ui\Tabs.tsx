import React, { useState } from 'react';
import './Tabs.css';

interface TabItem {
  key: string;
  label: string;
  content: React.ReactNode;
}

interface TabsProps {
  items: TabItem[];
  defaultActiveKey?: string;
  onChange?: (activeKey: string) => void;
  className?: string;
}

export const Tabs: React.FC<TabsProps> = ({
  items,
  defaultActiveKey,
  onChange,
  className = ''
}) => {
  const [activeKey, setActiveKey] = useState(defaultActiveKey || items[0]?.key);

  const handleTabClick = (key: string) => {
    setActiveKey(key);
    onChange?.(key);
  };

  return (
    <div className={`tabs ${className}`}>
      <div className="tabs-header">
        <div className="tabs-nav">
          {items.map((item) => (
            <button
              key={item.key}
              className={`tab-button ${activeKey === item.key ? 'active' : ''}`}
              onClick={() => handleTabClick(item.key)}
            >
              {item.label}
            </button>
          ))}
        </div>
      </div>
      
      {items.map((item) => (
        <div
          key={item.key}
          className={`tab-content ${activeKey === item.key ? 'active' : ''}`}
        >
          {item.content}
        </div>
      ))}
    </div>
  );
};

export default Tabs;
