/**
 * 浏览器服务 - 处理外部链接和系统交互
 * 注意：这是开发期间的临时功能，封版时需要移除
 */

class BrowserService {
  /**
   * 检查是否在 Electron 环境中
   */
  isElectronEnvironment(): boolean {
    return typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
  }

  /**
   * 在系统默认浏览器中打开 URL
   * @param url 要打开的URL
   */
  async openExternal(url: string): Promise<boolean> {
    try {
      if (!url) {
        console.warn('BrowserService: URL 不能为空');
        return false;
      }

      // 验证 URL 格式
      if (!this.isValidUrl(url)) {
        console.warn('BrowserService: 无效的 URL 格式:', url);
        return false;
      }

      if (this.isElectronEnvironment()) {
        // Electron 环境：使用 IPC 调用
        const result = await window.electronAPI!.openExternal(url);
        if (result.success) {
          console.log('BrowserService: 成功在外部浏览器中打开:', url);
          return true;
        } else {
          console.error('BrowserService: 打开外部链接失败:', result.error);
          return false;
        }
      } else {
        // Web 环境：使用 window.open
        window.open(url, '_blank', 'noopener,noreferrer');
        console.log('BrowserService: 在新窗口中打开:', url);
        return true;
      }
    } catch (error) {
      console.error('BrowserService: 打开外部链接时出错:', error);
      return false;
    }
  }

  /**
   * 在文件管理器中显示文件
   * @param filePath 文件路径
   */
  async showItemInFolder(filePath: string): Promise<boolean> {
    try {
      if (!filePath) {
        console.warn('BrowserService: 文件路径不能为空');
        return false;
      }

      if (this.isElectronEnvironment()) {
        const result = await window.electronAPI!.showItemInFolder(filePath);
        if (result.success) {
          console.log('BrowserService: 成功在文件管理器中显示:', filePath);
          return true;
        } else {
          console.error('BrowserService: 显示文件失败:', result.error);
          return false;
        }
      } else {
        console.warn('BrowserService: showItemInFolder 仅在 Electron 环境中可用');
        return false;
      }
    } catch (error) {
      console.error('BrowserService: 显示文件时出错:', error);
      return false;
    }
  }

  /**
   * 验证 URL 格式
   */
  private isValidUrl(string: string): boolean {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  /**
   * 快捷方法：打开百度
   */
  async openBaidu(): Promise<boolean> {
    return this.openExternal('https://www.baidu.com');
  }

  /**
   * 快捷方法：打开 GitHub
   */
  async openGitHub(): Promise<boolean> {
    return this.openExternal('https://github.com');
  }

  /**
   * 快捷方法：打开 Google
   */
  async openGoogle(): Promise<boolean> {
    return this.openExternal('https://www.google.com');
  }

  /**
   * 快捷方法：搜索内容
   */
  async search(query: string, engine: 'baidu' | 'google' = 'baidu'): Promise<boolean> {
    const encodedQuery = encodeURIComponent(query);
    const urls = {
      baidu: `https://www.baidu.com/s?wd=${encodedQuery}`,
      google: `https://www.google.com/search?q=${encodedQuery}`
    };
    
    return this.openExternal(urls[engine]);
  }
}

// 创建单例实例
export const browserService = new BrowserService();

// 默认导出
export default browserService;

// 便捷的全局函数
export const openExternal = (url: string) => browserService.openExternal(url);
export const showItemInFolder = (path: string) => browserService.showItemInFolder(path);
export const openBaidu = () => browserService.openBaidu();
export const openGitHub = () => browserService.openGitHub();
export const search = (query: string, engine?: 'baidu' | 'google') => browserService.search(query, engine);
