/* 增强版批量导入来访者模态框样式 */

.enhanced-batch-import-modal {
  --primary-color: #3b82f6;
  --primary-light: #dbeafe;
  --success-color: #10b981;
  --success-light: #d1fae5;
  --error-color: #ef4444;
  --error-light: #fee2e2;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --bg-white: #ffffff;
  --bg-gray-50: #f9fafb;
  --bg-gray-100: #f3f4f6;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
}

/* 步骤导航 */
.step-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-gray-50);
}

.step-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.step-current {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--primary-light);
  border-radius: var(--radius-sm);
}

/* 引导步骤样式 */
.batch-import-guide {
  padding: var(--spacing-2xl);
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.guide-header {
  margin-bottom: var(--spacing-2xl);
}

.guide-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff9400, #ffb347);
  border-radius: 50%;
  color: white;
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

.guide-header h3 {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  line-height: 1.2;
}

.guide-header p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

.guide-features {
  display: grid;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.feature-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.feature-item svg {
  color: var(--success-color);
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.3;
}

.feature-item p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.guide-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* 输入步骤样式 */
.batch-import-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  height: 70vh;
  overflow: hidden;
  padding: var(--spacing-sm) 0;
}

/* 状态栏 */
.input-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: linear-gradient(135deg, var(--bg-white), var(--bg-gray-50));
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-sm);
  flex-shrink: 0;
}

.status-info {
  display: flex;
  gap: var(--spacing-xl);
  align-items: center;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.status-value.valid {
  color: var(--success-color);
}

.status-value.error {
  color: var(--error-color);
}

.status-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* 增强表格容器 */
.enhanced-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-white);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-sm);
  min-height: 0;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
}

/* 增强表格样式 */
.enhanced-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  min-width: 1400px;
}

.enhanced-table thead {
  background: linear-gradient(135deg, var(--bg-gray-100), var(--bg-gray-50));
  position: sticky;
  top: 0;
  z-index: 10;
}

.enhanced-table th {
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: center;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  border-right: 1px solid var(--border-light);
  white-space: nowrap;
  font-size: 13px;
  line-height: 1.4;
}

.enhanced-table th:last-child {
  border-right: none;
}

.enhanced-table th.required {
  position: relative;
}

.enhanced-table th.required::before {
  content: '*';
  color: var(--error-color);
  font-weight: bold;
  margin-right: var(--spacing-xs);
}

.enhanced-table tbody tr {
  transition: all 0.2s ease;
  background: var(--bg-white);
}

.enhanced-table tbody tr:nth-child(even) {
  background: var(--bg-gray-50);
}

.enhanced-table tbody tr:hover {
  background: var(--primary-light) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.enhanced-table tbody tr.has-errors {
  background: var(--error-light) !important;
}

.enhanced-table tbody tr.has-errors:hover {
  background: #fecaca !important;
}

.enhanced-table td {
  padding: var(--spacing-md) var(--spacing-sm);
  border-bottom: 1px solid var(--border-light);
  border-right: 1px solid var(--border-light);
  vertical-align: middle;
  position: relative;
}

.enhanced-table td.col-actions {
  text-align: center;
  vertical-align: middle;
}

.enhanced-table td:last-child {
  border-right: none;
}

/* 列宽优化 */
.col-index {
  width: 50px;
  min-width: 50px;
  text-align: center;
}

.col-name {
  width: 120px;
  min-width: 120px;
}

.col-gender {
  width: 80px;
  min-width: 80px;
}

.col-age {
  width: 80px;
  min-width: 80px;
}

.col-phone {
  width: 140px;
  min-width: 140px;
}

.col-email {
  width: 180px;
  min-width: 180px;
}

.col-emergency-contact {
  width: 120px;
  min-width: 120px;
}

.col-emergency-phone {
  width: 140px;
  min-width: 140px;
}

.col-occupation {
  width: 100px;
  min-width: 100px;
}

.col-education {
  width: 100px;
  min-width: 100px;
}

.col-address {
  width: 160px;
  min-width: 160px;
}

.col-notes {
  width: 160px;
  min-width: 160px;
}

.col-actions {
  width: 60px;
  min-width: 60px;
  text-align: center;
}

/* 行号样式 */
.row-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--bg-gray-100);
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
}

/* 表格内表单控件优化 */
.enhanced-table .form-group {
  margin: 0;
}

.enhanced-table .form-input,
.enhanced-table .form-select {
  width: 100%;
  min-height: 36px;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 13px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-white);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.enhanced-table .form-input:focus,
.enhanced-table .form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-sm);
  transform: translateY(-1px);
}

.enhanced-table .form-input.error,
.enhanced-table .form-select.error {
  border-color: var(--error-color);
  background: var(--error-light);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.enhanced-table .form-input.error:focus,
.enhanced-table .form-select.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15), var(--shadow-sm);
}

.enhanced-table .form-input::placeholder {
  color: var(--text-tertiary);
  font-size: 12px;
}

/* 删除按钮优化 */
.delete-btn {
  padding: 0 !important;
  margin: 0 auto !important;
  min-height: 32px !important;
  width: 32px !important;
  height: 32px !important;
  color: var(--error-color) !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
  background: var(--bg-white) !important;
  border-radius: var(--radius-sm) !important;
  transition: all 0.2s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: var(--shadow-sm) !important;
  position: relative !important;
  overflow: hidden !important;
}

.delete-btn .btn-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.delete-btn .btn-icon svg {
  width: 16px !important;
  height: 16px !important;
  display: block !important;
  margin: 0 auto !important;
  flex-shrink: 0 !important;
}

.delete-btn:hover:not(:disabled) {
  background: var(--error-light) !important;
  border-color: var(--error-color) !important;
  color: #dc2626 !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

.delete-btn:active:not(:disabled) {
  transform: translateY(0) !important;
}

.delete-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
}

/* 帮助卡片 */
.help-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  background: linear-gradient(135deg, var(--bg-white), var(--bg-gray-50));
}

.help-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  color: var(--primary-color);
  font-size: 14px;
}

.help-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.help-section h5 {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.3;
}

.help-section p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* 滚动条优化 */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: var(--bg-gray-50);
  border-radius: var(--radius-sm);
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-sm);
  transition: background 0.2s ease;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .enhanced-table {
    min-width: 1200px;
  }
  
  .col-email {
    width: 160px;
    min-width: 160px;
  }
  
  .col-address {
    width: 140px;
    min-width: 140px;
  }
  
  .col-notes {
    width: 120px;
    min-width: 120px;
  }
}

@media (max-width: 1200px) {
  .batch-import-input {
    height: 60vh;
  }
  
  .input-status-bar {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .status-info {
    justify-content: center;
  }
  
  .status-actions {
    justify-content: center;
  }
  
  .enhanced-table {
    min-width: 1000px;
    font-size: 13px;
  }
  
  .enhanced-table th,
  .enhanced-table td {
    padding: var(--spacing-sm) var(--spacing-xs);
  }
  
  .enhanced-table .form-input,
  .enhanced-table .form-select {
    min-height: 32px;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .batch-import-guide {
    padding: var(--spacing-lg);
  }
  
  .guide-header h3 {
    font-size: 24px;
  }
  
  .guide-actions {
    flex-direction: column;
  }
  
  .batch-import-input {
    height: 50vh;
  }
  
  .help-content {
    grid-template-columns: 1fr;
  }
}

/* 加载状态 */
.enhanced-batch-import-modal.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-import-guide,
.batch-import-input {
  animation: fadeIn 0.3s ease-out;
}

.feature-item {
  animation: fadeIn 0.3s ease-out;
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }

/* 表单错误提示隐藏 */
.enhanced-table .form-error {
  display: none;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .enhanced-batch-import-modal {
    --border-color: #000000;
    --text-secondary: #000000;
    --bg-gray-50: #ffffff;
  }
  
  .enhanced-table th {
    border-bottom-width: 3px;
  }
  
  .enhanced-table .form-input:focus,
  .enhanced-table .form-select:focus {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-table tbody tr,
  .feature-item,
  .enhanced-table .form-input,
  .enhanced-table .form-select,
  .delete-btn {
    transition: none;
  }
  
  .batch-import-guide,
  .batch-import-input,
  .feature-item {
    animation: none;
  }
  
  .enhanced-table tbody tr:hover,
  .feature-item:hover,
  .delete-btn:hover:not(:disabled) {
    transform: none;
  }
}