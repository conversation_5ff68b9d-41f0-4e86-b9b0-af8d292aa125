import { electronDataManager } from './electronDataManager';
import type { SimpleCase } from '../types/case';
import type { Visitor } from '../types/visitor';
import type { GroupSession } from '../types/groupSession';

export class StatisticsService {
  async getOverviewStats() {
    const [allCases, allVisitors, allSessions] = await Promise.all([
      electronDataManager.getAllCases(),
      electronDataManager.getAllVisitors(),
      electronDataManager.getAllGroupSessions()
    ]);

    return {
      cases: {
        total: allCases.length,
        urgent: allCases.filter((c: SimpleCase) => c.crisis === '⚠️').length,
        starred: allCases.filter((c: SimpleCase) => c.star).length
      },
      visitors: {
        total: allVisitors.length,
        thisMonth: allVisitors.filter((v: Visitor) => {
          const createdDate = new Date(v.createdAt);
          const now = new Date();
          return createdDate.getMonth() === now.getMonth() && 
                 createdDate.getFullYear() === now.getFullYear();
        }).length
      },
      sessions: {
        total: allSessions.length,
        active: allSessions.filter((s: GroupSession) => s.status === '进行中').length,
        upcoming: allSessions.filter((s: GroupSession) => s.status === '计划中').length
      }
    };
  }

  async getMonthlyStats() {
    const [allCases, allVisitors, allSessions] = await Promise.all([
      electronDataManager.getAllCases(),
      electronDataManager.getAllVisitors(),
      electronDataManager.getAllGroupSessions()
    ]);

    const now = new Date();
    const monthsData = [];

    for (let i = 11; i >= 0; i--) {
      const targetDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const month = targetDate.toLocaleString('zh-CN', { month: 'short' });
      
      const casesCount = allCases.filter((c: SimpleCase) => {
        const caseDate = new Date(c.createdAt);
        return caseDate.getMonth() === targetDate.getMonth() && 
               caseDate.getFullYear() === targetDate.getFullYear();
      }).length;

      const visitorsCount = allVisitors.filter((v: Visitor) => {
        const visitorDate = new Date(v.createdAt);
        return visitorDate.getMonth() === targetDate.getMonth() && 
               visitorDate.getFullYear() === targetDate.getFullYear();
      }).length;

      monthsData.push({
        month,
        cases: casesCount,
        visitors: visitorsCount
      });
    }

    return monthsData;
  }

  async getSessionStats() {
    const [allCases, allVisitors] = await Promise.all([
      electronDataManager.getAllCases(),
      electronDataManager.getAllVisitors()
    ]);

    // 计算咨询时长分布
    const durationStats = allCases.reduce((acc: any, case_: SimpleCase) => {
      const duration = case_.duration || 60;
      const range = this.getDurationRange(duration);
      acc[range] = (acc[range] || 0) + 1;
      return acc;
    }, {});

    // 计算年龄分布
    const ageStats = allVisitors.reduce((acc: any, visitor: Visitor) => {
      const ageRange = this.getAgeRange(visitor.age);
      acc[ageRange] = (acc[ageRange] || 0) + 1;
      return acc;
    }, {});

    // 计算危机等级分布
    const crisisStats = allCases.reduce((acc: any, case_: SimpleCase) => {
      const crisis = case_.crisis || '✅';
      acc[crisis] = (acc[crisis] || 0) + 1;
      return acc;
    }, {});

    return {
      duration: durationStats,
      age: ageStats,
      crisis: crisisStats
    };
  }

  private getDurationRange(duration: number): string {
    if (duration <= 30) return '30分钟以下';
    if (duration <= 60) return '30-60分钟';
    if (duration <= 90) return '60-90分钟';
    return '90分钟以上';
  }

  private getAgeRange(age: number): string {
    if (age < 6) return '学龄前';
    if (age < 12) return '儿童';
    if (age < 18) return '青少年';
    if (age < 30) return '青年';
    if (age < 50) return '中年';
    return '老年';
  }

  async exportStatistics(format: 'csv' | 'excel' = 'excel') {
    const overviewStats = await this.getOverviewStats();
    const monthlyStats = await this.getMonthlyStats();
    const sessionStats = await this.getSessionStats();

    const exportData = {
      overview: overviewStats,
      monthly: monthlyStats,
      sessions: sessionStats,
      exportDate: new Date().toISOString(),
      format
    };

    // 在浏览器环境下，只是返回数据
    const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
    if (!isElectron) {
      console.log('浏览器环境：模拟导出统计数据', exportData);
      return exportData;
    }

    // 在桌面端可以实际导出文件
    return exportData;
  }
}

export const statisticsService = new StatisticsService();
