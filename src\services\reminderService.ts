// 系统提醒服务
import type { Appointment } from '../types/schedule';

export class ReminderService {
  private static instance: ReminderService;
  private reminders: Map<string, NodeJS.Timeout> = new Map();

  static getInstance(): ReminderService {
    if (!ReminderService.instance) {
      ReminderService.instance = new ReminderService();
    }
    return ReminderService.instance;
  }

  /**
   * 为预约设置提醒
   */
  setReminder(appointment: Appointment): void {
    if (!appointment.reminderEnabled) {
      return;
    }

    // 清除已存在的提醒
    this.clearReminder(appointment.id);

    const reminderTime = this.calculateReminderTime(appointment);
    if (reminderTime <= Date.now()) {
      return; // 已过期，不设置提醒
    }

    const timeoutId = setTimeout(() => {
      this.showNotification(appointment);
      this.reminders.delete(appointment.id);
    }, reminderTime - Date.now());

    this.reminders.set(appointment.id, timeoutId);
  }

  /**
   * 清除指定预约的提醒
   */
  clearReminder(appointmentId: string): void {
    const timeoutId = this.reminders.get(appointmentId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.reminders.delete(appointmentId);
    }
  }

  /**
   * 清除所有提醒
   */
  clearAllReminders(): void {
    this.reminders.forEach((timeoutId) => {
      clearTimeout(timeoutId);
    });
    this.reminders.clear();
  }

  /**
   * 显示系统通知
   */
  private async showNotification(appointment: Appointment): Promise<void> {
    const title = '预约提醒';
    const body = `您有一个预约即将开始：\n${appointment.visitorName} - ${appointment.subject}\n时间：${appointment.date} ${appointment.startTime}`;

    // 检查是否为Electron环境
    if (window.electronAPI) {
      // 桌面端：使用Electron的系统通知
      try {
        await (window.electronAPI as any).showNotification(title, {
          body,
          icon: 'logo.png', // 使用应用图标
          tag: `appointment-${appointment.id}`,
          requireInteraction: true, // 需要用户交互才能关闭
          actions: [
            { action: 'view', title: '查看详情' },
            { action: 'dismiss', title: '关闭' }
          ]
        });
      } catch (error) {
        console.error('桌面端通知失败:', error);
        this.showFallbackAlert(appointment);
      }
    } else {
      // 浏览器端：检查通知权限
      if ('Notification' in window) {
        if (Notification.permission === 'granted') {
          this.createBrowserNotification(title, body, appointment);
        } else if (Notification.permission === 'default') {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            this.createBrowserNotification(title, body, appointment);
          } else {
            this.showFallbackAlert(appointment);
          }
        } else {
          this.showFallbackAlert(appointment);
        }
      } else {
        this.showFallbackAlert(appointment);
      }
    }
  }

  /**
   * 创建浏览器通知
   */
  private createBrowserNotification(title: string, body: string, appointment: Appointment): void {
    const notification = new Notification(title, {
      body,
      icon: '/logo.png',
      tag: `appointment-${appointment.id}`,
      requireInteraction: true
    });

    notification.onclick = () => {
      // 聚焦到应用窗口
      window.focus();
      notification.close();
      
      // 可以添加导航到预约详情的逻辑
      this.navigateToAppointment(appointment.id);
    };

    // 5秒后自动关闭
    setTimeout(() => {
      notification.close();
    }, 5000);
  }

  /**
   * 备用提醒方式（弹窗）
   */
  private showFallbackAlert(appointment: Appointment): void {
    const message = `预约提醒\n\n${appointment.visitorName} - ${appointment.subject}\n时间：${appointment.date} ${appointment.startTime}\n\n请及时处理！`;
    
    // 使用系统alert作为最后的备用方案
    alert(message);
  }

  /**
   * 导航到预约详情
   */
  private navigateToAppointment(appointmentId: string): void {
    // 这里可以触发路由跳转或其他导航逻辑
    console.log(`导航到预约详情: ${appointmentId}`);
    
    // 如果需要，可以发送自定义事件
    window.dispatchEvent(new CustomEvent('navigateToAppointment', {
      detail: { appointmentId }
    }));
  }

  /**
   * 计算提醒时间
   */
  private calculateReminderTime(appointment: Appointment): number {
    const appointmentDateTime = new Date(`${appointment.date} ${appointment.startTime}`);
    const reminderMinutes = appointment.reminderTime || 15;
    return appointmentDateTime.getTime() - (reminderMinutes * 60 * 1000);
  }

  /**
   * 批量设置多个预约的提醒
   */
  setMultipleReminders(appointments: Appointment[]): void {
    appointments.forEach(appointment => {
      if (appointment.reminderEnabled) {
        this.setReminder(appointment);
      }
    });
  }

  /**
   * 获取当前活动的提醒数量
   */
  getActiveRemindersCount(): number {
    return this.reminders.size;
  }

  /**
   * 检查指定预约是否有活动的提醒
   */
  hasActiveReminder(appointmentId: string): boolean {
    return this.reminders.has(appointmentId);
  }
}

// 导出单例实例
export const reminderService = ReminderService.getInstance();
