import React, { useState, useEffect } from 'react';
import { Clock, Calendar } from 'lucide-react';
import { scheduleService } from '../../services';
import { MockBrowserServices } from '../../data/mockDailyWorkspace';
import type { Appointment } from '../../types/schedule';

// 检测是否在浏览器环境
const isBrowser = typeof window !== 'undefined' && !window.electronAPI;

interface TodayScheduleCardProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help' | 'dev-browser') => void;
}

export const TodayScheduleCard: React.FC<TodayScheduleCardProps> = ({ onNavigate }) => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    confirmed: 0,
    pending: 0,
    cancelled: 0,
    nextAppointment: null as Appointment | null
  });
  const [timeToNext, setTimeToNext] = useState({
    appointment: null as Appointment | null,
    timeRemaining: 0,
    isUpcoming: false
  });

  // 格式化倒计时显示
  const formatCountdown = (seconds: number): string => {
    if (seconds <= 0) return '00:00:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取倒计时标签
  const getCountdownLabel = (appointment: Appointment | null, timeRemaining: number): string => {
    if (!appointment) return '暂无预约';
    if (timeRemaining <= 0) return '预约进行中';
    if (timeRemaining <= 600) return '即将开始';
    return '距离下次预约';
  };

  // 加载今日数据
  const loadTodayData = async () => {
    try {
      setLoading(true);
      
      if (isBrowser) {
        // 浏览器环境使用模拟数据
        const appointments = await MockBrowserServices.getTodayAppointments();
        const nextAppointment = await MockBrowserServices.getNextUpcomingAppointment();
        const timeRemaining = await MockBrowserServices.getTimeToNextAppointment();
        
        // 计算统计数据
        const stats = {
          total: appointments.length,
          completed: 0,
          confirmed: appointments.filter(a => a.status === '已确认').length,
          pending: 0,
          cancelled: 0,
          nextAppointment
        };
        
        setStats(stats);
        setTimeToNext({
          appointment: nextAppointment,
          timeRemaining: timeRemaining || 0,
          isUpcoming: timeRemaining !== null && timeRemaining <= 600
        });
      } else {
        // Electron环境使用真实服务
        const [todayStats, timeInfo] = await Promise.all([
          scheduleService.getTodayAppointmentStats(),
          scheduleService.getTimeToNextAppointment()
        ]);
        
        setStats(todayStats);
        setTimeToNext(timeInfo);
      }
    } catch (error) {
      console.error('加载今日数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 更新倒计时
  const updateCountdown = async () => {
    try {
      const timeInfo = await scheduleService.getTimeToNextAppointment();
      setTimeToNext(timeInfo);
    } catch (error) {
      console.error('更新倒计时失败:', error);
    }
  };

  // 处理创建预约按钮点击
  const handleCreateAppointment = () => {
    if (onNavigate) {
      onNavigate('schedule');
    }
  };

  // 处理查看今日日程按钮点击
  const handleViewSchedule = () => {
    if (onNavigate) {
      onNavigate('schedule');
    }
  };

  // 初始加载
  useEffect(() => {
    loadTodayData();
  }, []);

  // 设置倒计时更新定时器
  useEffect(() => {
    const timer = setInterval(updateCountdown, 1000);
    return () => clearInterval(timer);
  }, []);

  // 设置数据刷新定时器（每分钟）
  useEffect(() => {
    const refreshTimer = setInterval(loadTodayData, 60000);
    return () => clearInterval(refreshTimer);
  }, []);

  if (loading) {
    return (
      <div className={`workspace-card schedule-card loading`}>
        <div className="workspace-card-header">
          <div className="workspace-card-icon">
            <Clock size={20} />
          </div>
          <h3 className="workspace-card-title">今日安排</h3>
        </div>
        <div className="workspace-card-content">
          <div className="loading-skeleton wide"></div>
          <div className="loading-skeleton medium"></div>
          <div className="loading-skeleton narrow"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`workspace-card schedule-card ${timeToNext.isUpcoming ? 'upcoming-warning' : ''}`}>
      <div className="workspace-card-header">
        <div className="workspace-card-icon">
          <Clock size={20} />
        </div>
        <h3 className="workspace-card-title">今日安排</h3>
      </div>
      
      <div className="workspace-card-content">
        {/* 倒计时区域 */}
        <div className="schedule-countdown">
          <div className="countdown-time">
            {formatCountdown(timeToNext.timeRemaining)}
          </div>
          <div className="countdown-label">
            {getCountdownLabel(timeToNext.appointment, timeToNext.timeRemaining)}
          </div>
        </div>

        {/* 今日统计 */}
        <div className="schedule-stats">
          <div className="schedule-stat">
            <div className="schedule-stat-value">{stats.total}</div>
            <div className="schedule-stat-label">总预约</div>
          </div>
          <div className="schedule-stat">
            <div className="schedule-stat-value">{stats.completed}</div>
            <div className="schedule-stat-label">已完成</div>
          </div>
          <div className="schedule-stat">
            <div className="schedule-stat-value">{stats.confirmed}</div>
            <div className="schedule-stat-label">已确认</div>
          </div>
        </div>

        {/* 下一位来访者信息 */}
        {timeToNext.appointment ? (
          <div className="next-visitor">
            <div className="next-visitor-name">
              下一位：{timeToNext.appointment.visitorName}
            </div>
            <div className="next-visitor-info">
              {timeToNext.appointment.startTime} - {timeToNext.appointment.endTime}<br />
              {timeToNext.appointment.room} · {timeToNext.appointment.type}
              {timeToNext.appointment.urgency !== '普通' && (
                <span style={{ color: '#dc2626', fontWeight: 600 }}>
                  · {timeToNext.appointment.urgency}
                </span>
              )}
            </div>
          </div>
        ) : (
          <div className="no-appointments">
            <div className="no-appointments-icon">📅</div>
            <div>今日暂无预约安排</div>
            <button 
              className="create-appointment-btn"
              onClick={handleCreateAppointment}
            >
              <Calendar size={16} />
              快速创建预约
            </button>
          </div>
        )}

        {/* 查看详细日程按钮 */}
        {stats.total > 0 && (
          <button 
            className="create-appointment-btn"
            onClick={handleViewSchedule}
            style={{ marginTop: '1rem', width: '100%' }}
          >
            查看今日详细日程
          </button>
        )}
      </div>
    </div>
  );
};
