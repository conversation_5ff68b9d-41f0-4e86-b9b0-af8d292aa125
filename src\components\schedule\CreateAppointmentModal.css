/* 创建预约弹窗样式 - 优化布局版 */

/* 模态框基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: 8px;
}

.modal-container {
  background: var(--bg-primary);
  border-radius: 6px;
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  flex-shrink: 0;
}

.modal-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 标签导航 */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-secondary);
  flex-shrink: 0;
}

.tab-button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.tab-button:hover {
  color: var(--text-primary);
  background: rgba(79, 156, 249, 0.05);
}

.tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
  background: var(--bg-primary);
}

/* 内容区域 */
.modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  min-height: 0;
}

.tab-pane {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

.tab-pane > :last-child {
  margin-bottom: 0 !important;
}

/* 表单样式 - 优化布局 */
.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 6px 0;
  padding-bottom: 2px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.form-row.single,
.form-grid.single {
  grid-template-columns: 1fr;
}

.form-row.triple,
.form-grid.triple {
  grid-template-columns: 1fr 1fr 1fr;
}

.form-group {
  margin-bottom: 12px;
}

.form-group.single {
  grid-column: 1 / -1;
}

.calculated-time {
  font-size: 13px;
  color: var(--text-primary);
  padding: 4px 6px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 3px;
  font-weight: var(--font-medium);
}

.checkbox-group {
  margin-bottom: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--text-primary);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 14px;
  height: 14px;
  margin: 0;
}

/* 快速模板样式 */
.quick-templates {
  margin-bottom: 8px;
  padding: 6px;
  background: var(--bg-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-light);
}

.quick-templates label {
  display: block;
  font-size: 13px;
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 3px;
}

.template-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.template-btn {
  padding: 2px 6px;
  border: 1px solid var(--border-medium);
  background: var(--bg-primary);
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: var(--transition-fast);
}

.template-btn:hover {
  background: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 4px;
}

.form-label.required::after {
  content: " *";
  color: var(--error);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 10px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: 4px;
  transition: var(--transition-fast);
  margin: 0;
  min-height: 40px;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(79, 156, 249, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-error {
  color: var(--error);
  font-size: 12px;
  margin-top: 2px;
}

/* 底部按钮 */
.modal-footer {
  padding: 8px 12px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 4px;
  }
  
  .modal-container {
    max-width: 100%;
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 8px 10px;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .tab-content {
    padding: 8px;
  }
  
  .form-grid,
  .form-grid.triple {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .form-section {
    margin-bottom: 16px;
  }
  
  .modal-footer {
    padding: 6px 8px;
  }
}

@media (max-width: 480px) {
  .tab-button {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .form-label,
  .form-input,
  .form-select,
  .form-textarea {
    font-size: 12px;
  }
  
  .form-grid {
    gap: 6px;
  }
  
  .form-section {
    margin-bottom: 12px;
  }
}
