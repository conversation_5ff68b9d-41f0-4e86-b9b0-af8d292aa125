/* 究极按钮样式修复 - 最高优先级 */

/* 使用极高特异性确保样式生效 */
.schedule-layout .card-actions button.btn.btn-ghost,
.schedule-layout .appointment-item button.btn.btn-ghost,
.schedule-layout button.btn.btn-ghost {
  background: transparent !important;
  border: 1px solid transparent !important;
  color: #3B82F6 !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.12s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
  text-decoration: none !important;
  white-space: nowrap !important;
}

.schedule-layout .card-actions button.btn.btn-ghost:hover,
.schedule-layout .appointment-item button.btn.btn-ghost:hover,
.schedule-layout button.btn.btn-ghost:hover {
  background: #f1f5f9 !important;
  color: #2563EB !important;
}

.schedule-layout .card-actions button.btn.btn-primary,
.schedule-layout .appointment-item button.btn.btn-primary,
.schedule-layout button.btn.btn-primary {
  background: #3B82F6 !important;
  border: 1px solid #3B82F6 !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.12s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
  text-decoration: none !important;
  white-space: nowrap !important;
}

.schedule-layout .card-actions button.btn.btn-primary:hover,
.schedule-layout .appointment-item button.btn.btn-primary:hover,
.schedule-layout button.btn.btn-primary:hover {
  background: #2563EB !important;
  border-color: #2563EB !important;
}

.schedule-layout .card-actions button.btn.btn-secondary,
.schedule-layout .appointment-item button.btn.btn-secondary,
.schedule-layout button.btn.btn-secondary {
  background: white !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.12s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
  text-decoration: none !important;
  white-space: nowrap !important;
}

.schedule-layout .card-actions button.btn.btn-secondary:hover,
.schedule-layout .appointment-item button.btn.btn-secondary:hover,
.schedule-layout button.btn.btn-secondary:hover {
  background: #f9fafb !important;
}

/* 确保图标正确显示 */
.schedule-layout button.btn .btn-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

/* 确保按钮容器正确布局 */
.schedule-layout .card-actions {
  display: flex !important;
  gap: 8px !important;
  align-items: center !important;
  flex-wrap: wrap !important;
}
