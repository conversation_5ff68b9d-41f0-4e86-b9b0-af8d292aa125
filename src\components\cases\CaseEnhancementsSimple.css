/* 简洁版个案管理增强样式 */

/* 通用面板样式 */
.simple-alert-panel,
.simple-reminder-panel,
.simple-workload-panel,
.simple-development-panel {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 面板头部 */
.panel-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  font-weight: 500;
  font-size: 14px;
  color: #374151;
}

/* 面板内容 */
.panel-content {
  padding: 12px 16px;
}

/* 面板项目 */
.panel-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f9fafb;
}

.panel-item:last-child {
  border-bottom: none;
}

.item-name {
  font-weight: 500;
  color: #111827;
  font-size: 14px;
}

.item-info {
  font-size: 12px;
  color: #6b7280;
  margin-left: auto;
  margin-right: 12px;
}

.item-action {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  background: #ffffff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  color: #374151;
}

.item-action:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.item-action.urgent {
  background: #dc2626;
  color: #ffffff;
  border-color: #dc2626;
}

.item-action.urgent:hover {
  background: #b91c1c;
}

/* 面板更多信息 */
.panel-more {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
  margin-top: 8px;
}

/* 危机预警特殊样式 */
.simple-alert-panel {
  border-color: #fca5a5;
  background: #fef2f2;
}

.simple-alert-panel .panel-header {
  color: #dc2626;
  border-bottom-color: #fecaca;
}

/* 提醒面板特殊样式 */
.simple-reminder-panel {
  border-color: #93c5fd;
  background: #eff6ff;
}

.simple-reminder-panel .panel-header {
  color: #2563eb;
  border-bottom-color: #dbeafe;
}

/* 工作负荷指示器 */
.simple-workload-panel .panel-header {
  color: #059669;
}

.workload-stats {
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  line-height: 1.2;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

/* 专业发展提醒 */
.simple-development-panel {
  border-color: #a7f3d0;
  background: #f0fdf4;
}

.simple-development-panel.medium {
  border-color: #fde68a;
  background: #fffbeb;
}

.simple-development-panel.high {
  border-color: #fca5a5;
  background: #fef2f2;
}

.simple-development-panel .panel-header {
  color: #059669;
}

.simple-development-panel.medium .panel-header {
  color: #d97706;
}

.simple-development-panel.high .panel-header {
  color: #dc2626;
}

.simple-development-panel .panel-content p {
  margin: 0;
  font-size: 13px;
  color: #374151;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .item-info {
    margin-left: 0;
    margin-right: 0;
  }
  
  .workload-stats {
    flex-direction: column;
    gap: 8px;
  }
}
