/* 加载屏幕样式 - 白蓝橙配色方案 */
@import '../../styles/variables.css';

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  overflow: hidden;
}

/* 背景装饰 */
.loading-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.loading-circles {
  position: relative;
  width: 100%;
  height: 100%;
}

.circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  background: var(--primary-blue);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  background: var(--accent-amber);
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  background: var(--primary-blue-light);
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

/* 主要内容区域 */
.loading-content {
  text-align: center;
  z-index: 1;
  max-width: 400px;
  padding: var(--spacing-3xl);
}

/* Logo区域 */
.loading-logo {
  margin-bottom: var(--spacing-4xl);
}

.logo-icon {
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s ease-in-out infinite;
}

.loading-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-amber) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 加载信息 */
.loading-info {
  margin-bottom: var(--spacing-4xl);
}

.loading-main-text {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.loading-sub-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--leading-relaxed);
}

/* 加载动画 */
.loading-spinner-container {
  margin-bottom: var(--spacing-4xl);
  display: flex;
  justify-content: center;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: var(--primary-blue);
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: var(--accent-amber);
  animation-delay: 0.3s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: var(--primary-blue-light);
  animation-delay: 0.6s;
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 进度条 */
.loading-progress {
  margin-bottom: var(--spacing-3xl);
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-blue) 0%, var(--accent-amber) 100%);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* 加载点动画 */
.loading-dots {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-blue);
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
  background: var(--accent-amber);
}

.dot:nth-child(3) {
  animation-delay: 0s;
  background: var(--primary-blue-light);
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-content {
    padding: var(--spacing-2xl);
    max-width: 320px;
  }
  
  .loading-title {
    font-size: var(--text-xl);
  }
  
  .loading-main-text {
    font-size: var(--text-lg);
  }
  
  .loading-sub-text {
    font-size: var(--text-sm);
  }
  
  .circle-1 {
    width: 120px;
    height: 120px;
  }
  
  .circle-2 {
    width: 100px;
    height: 100px;
  }
  
  .circle-3 {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 480px) {
  .loading-content {
    padding: var(--spacing-xl);
  }
  
  .loading-spinner {
    width: 50px;
    height: 50px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .loading-screen {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  }
  
  .loading-main-text {
    color: var(--white);
  }
  
  .loading-sub-text {
    color: var(--gray-300);
  }
  
  .progress-text {
    color: var(--gray-400);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .loading-title {
    -webkit-text-fill-color: var(--text-primary);
    background: none;
  }
  
  .circle {
    opacity: 0.2;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .circle,
  .logo-icon,
  .spinner-ring,
  .dot,
  .progress-fill::after {
    animation: none;
  }
  
  .loading-spinner {
    opacity: 0.7;
  }
}