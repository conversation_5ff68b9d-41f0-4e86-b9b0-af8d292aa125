// 统一导出所有服务
export { electronDataManager as dataManager } from './electronDataManager';
export * as migrationService from './migrationService';
export { visitorService } from './visitorService';
export { caseService } from './caseService';
export { scheduleService } from './scheduleService';
export { reminderService } from './reminderService';
export { groupSessionService } from './groupSessionService';
export { statisticsService } from './statisticsService';
export * as helpDataService from './helpDataService';
export { notesService } from './notesService';
export { dailyMessageService } from './dailyMessageService';

// 开发期间临时服务 (封版时需要移除)
export { browserService } from './browserService';

// 初始化所有服务
export const initializeServices = async () => {
  console.log('正在初始化服务...');
  
  try {
    console.log('服务初始化完成');
  } catch (error) {
    console.error('服务初始化失败:', error);
    throw error;
  }
};

// 获取系统状态
export const getSystemStatus = async () => {
  try {
    return {
      isReady: true,
      database: { status: 'ready' },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      isReady: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
};