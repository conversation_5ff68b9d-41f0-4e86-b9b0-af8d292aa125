import React from 'react';
import { BaseModal } from '../common/BaseModal';
import { Button } from '../common/Button';
import { AlertCircle } from 'lucide-react';
import { SimpleCase } from '../../types/case';

interface CaseDetailModalProps {
  isOpen: boolean;
  case: SimpleCase;
  onClose: () => void;
  onEdit: (caseData: SimpleCase) => void;
}

const CaseDetailModal: React.FC<CaseDetailModalProps> = ({ 
  isOpen, 
  case: caseData, 
  onClose, 
  onEdit 
}) => {
  console.warn('个案管理功能已禁用');

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title="个案详情">
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '40px 20px',
        textAlign: 'center'
      }}>
        <AlertCircle size={48} style={{ color: '#f59e0b', marginBottom: '16px' }} />
        <h3 style={{ margin: '0 0 12px 0', fontSize: '18px', fontWeight: '600' }}>
          个案管理功能暂时不可用
        </h3>
        <p style={{ margin: '0 0 24px 0', color: '#6b7280', lineHeight: '1.5' }}>
          个案详情查看功能正在维护中，暂时无法查看个案信息。
          <br />
          如有紧急需求，请联系系统管理员。
        </p>
        <Button onClick={onClose}>
          关闭
        </Button>
      </div>
    </BaseModal>
  );
};

export default CaseDetailModal;
