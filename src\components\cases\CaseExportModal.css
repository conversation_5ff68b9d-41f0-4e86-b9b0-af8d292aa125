/* 个案导出模态框样式 - 简化版 */

.export-modal-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 75vh;
  overflow: auto;
  background: #ffffff;
  padding: 20px;
}

/* 导出区域样式 */
.export-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.export-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.export-section-title svg {
  color: #3b82f6;
}

/* 导出范围选择样式 */
.export-range-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.export-option-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.export-option-card:hover {
  border-color: #3b82f6;
}

.export-option-card input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.export-option-card:has(input[type="radio"]:checked) {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.option-subtitle {
  font-size: 12px;
  color: #6b7280;
}

/* 导出格式选择样式 */
.export-format-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

.export-format-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s;
  text-align: center;
}

.export-format-card:hover {
  border-color: #3b82f6;
}

.export-format-card input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.export-format-card:has(input[type="radio"]:checked) {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.format-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
}

.export-format-card:has(input[type="radio"]:checked) .format-icon {
  background: #3b82f6;
  color: white;
}

.format-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.format-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.format-subtitle {
  font-size: 12px;
  color: #6b7280;
}

.format-status {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  margin-top: 4px;
}

.format-status.available {
  background: #dcfce7;
  color: #166534;
}

.format-status.developing {
  background: #fef3c7;
  color: #92400e;
}

/* 文件名设置样式 */
.export-filename-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filename-input-wrapper {
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 2px;
}

.filename-input-wrapper:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.filename-input {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #374151;
  outline: none;
}

.filename-input::placeholder {
  color: #9ca3af;
}

.filename-extension {
  padding: 6px 10px;
  background: #f3f4f6;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

.filename-help {
  font-size: 12px;
  color: #6b7280;
  padding-left: 4px;
}

/* 导出预览样式 */
.export-preview-section {
  margin-top: 4px;
}

.export-preview-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
}

.preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #3b82f6;
  border-radius: 8px;
  color: white;
  flex-shrink: 0;
}

.preview-content {
  flex: 1;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e40af;
  margin-bottom: 2px;
}

.preview-subtitle {
  font-size: 12px;
  color: #3730a3;
}

/* 使用说明样式 */
.export-tips {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.tips-content h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.tips-content ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: none;
}

.tips-content li {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 6px;
  position: relative;
}

.tips-content li::before {
  content: '•';
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: -14px;
}

.tips-content li:last-child {
  margin-bottom: 0;
}

/* 操作按钮样式 */
.export-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .export-modal-container {
    padding: 16px;
    gap: 16px;
  }
  
  .export-range-options {
    grid-template-columns: 1fr;
  }
  
  .export-format-options {
    grid-template-columns: 1fr;
  }
  
  .export-actions {
    flex-direction: column-reverse;
  }
  
  .export-actions .btn {
    width: 100%;
    justify-content: center;
  }
}
