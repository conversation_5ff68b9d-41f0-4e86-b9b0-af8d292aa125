/* 标签页组件样式 */

.tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid var(--border-light, #e5e7eb);
  background: var(--bg-secondary, #FAFBFC);
  margin: 0 -24px 20px -24px;
  padding: 0 24px;
}

.tabs-nav {
  display: flex;
  gap: 0;
}

.tab-button {
  padding: 12px 20px;
  border: none;
  background: transparent;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.12s ease;
  position: relative;
}

.tab-button:hover {
  color: var(--text-primary, #111827);
  background: rgba(59, 130, 246, 0.04);
}

.tab-button.active {
  color: var(--primary-blue, #3B82F6);
  border-bottom-color: var(--primary-blue, #3B82F6);
  background: var(--bg-primary, #ffffff);
}

.tab-content {
  flex: 1;
  padding: 0;
  display: none;
}

.tab-content.active {
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-header {
    margin: 0 -16px 16px -16px;
    padding: 0 16px;
  }
  
  .tab-button {
    padding: 10px 16px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .tabs-header {
    margin: 0 -12px 12px -12px;
    padding: 0 12px;
  }
  
  .tabs-nav {
    width: 100%;
  }
  
  .tab-button {
    flex: 1;
    padding: 8px 12px;
    text-align: center;
  }
}
