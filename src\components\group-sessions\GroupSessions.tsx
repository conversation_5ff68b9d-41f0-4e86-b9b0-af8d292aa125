import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>ton,
  Badge,
  EmptyState,
  FilterBar,
  ActionButtons
} from '../ui/index';
import {
  Plus,
  Search,
  Users,
  Clock,
  MapPin,
  Play,
  Pause,
  UsersRound,
  BarChart3
} from 'lucide-react';
import type { GroupSession, GroupSessionFilters } from '../../types/groupSession';
import { groupSessions } from '../../data/mockGroupSessions';
import { GroupSessionDetailModal } from './GroupSessionDetailModal';
import { CreateGroupSessionModal } from './CreateGroupSessionModal';
import EditGroupSessionModal from './EditGroupSessionModal';
import './GroupSessions.css';

// 模拟数据
const GroupSessions: React.FC = () => {
  const [groupSessionsData, setGroupSessionsData] = useState<GroupSession[]>(groupSessions);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState<GroupSession | null>(null);
  const [editingSession, setEditingSession] = useState<GroupSession | null>(null);
  const [filters, setFilters] = useState<GroupSessionFilters>({
    search: '',
    status: '',
    sessionType: '',
    targetAge: '',
    therapist: '',
    dateRange: {
      start: '',
      end: ''
    }
  });

  // 筛选逻辑
  const filteredSessions = groupSessionsData.filter(session => {
    const matchesSearch = session.title.toLowerCase().includes(filters.search.toLowerCase()) ||
                         session.therapistName.toLowerCase().includes(filters.search.toLowerCase()) ||
                         session.location.toLowerCase().includes(filters.search.toLowerCase());

    const matchesStatus = !filters.status || session.status === filters.status;
    const matchesType = !filters.sessionType || session.sessionType === filters.sessionType;
    const matchesAge = !filters.targetAge || session.targetAge === filters.targetAge;
    const matchesTherapist = !filters.therapist || session.therapistName.includes(filters.therapist);

    return matchesSearch && matchesStatus && matchesType && matchesAge && matchesTherapist;
  });

  const handleCreateSession = (newSession: GroupSession) => {
    setGroupSessionsData([...groupSessionsData, newSession]);
    setShowCreateModal(false);
  };

  const handleUpdateFilters = (field: keyof GroupSessionFilters, value: string | { start: string; end: string }) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const handleViewSession = (session: GroupSession) => {
    setSelectedSession(session);
    setShowDetailModal(true);
  };

  // 编辑团体活动
  const handleEditSession = (session: GroupSession) => {
    setEditingSession(session);
    setShowEditModal(true);
  };

  // 更新团体活动
  const handleUpdateSession = (updatedSession: GroupSession) => {
    setGroupSessionsData(prev => prev.map(s => s.id === updatedSession.id ? updatedSession : s));
    setShowEditModal(false);
    setEditingSession(null);
  };

  // 删除团体活动
  const handleDeleteSession = (session: GroupSession) => {
    if (confirm(`确定要删除团体活动"${session.title}"吗？此操作不可恢复。`)) {
      setGroupSessionsData(prev => prev.filter(s => s.id !== session.id));
      console.log('删除团体活动:', session.title);
    }
  };

  const getStatusBadge = (status: GroupSession['status']) => {
    const variants = {
      '计划中': 'warning' as const,
      '进行中': 'success' as const,
      '已完成': 'gray' as const,
      '已取消': 'danger' as const,
      '暂停': 'warning' as const
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getProgressInfo = (session: GroupSession) => {
    if (session.totalSessions && session.currentSession !== undefined) {
      const progress = (session.currentSession / session.totalSessions) * 100;
      return {
        text: `${session.currentSession}/${session.totalSessions} 次`,
        percentage: Math.round(progress)
      };
    }
    return {
      text: `进行中`,
      percentage: 0
    };
  };

  // 统计数据
  const stats = {
    total: groupSessionsData.length,
    active: groupSessionsData.filter(s => s.status === '进行中').length,
    planned: groupSessionsData.filter(s => s.status === '计划中').length,
    completed: groupSessionsData.filter(s => s.status === '已完成').length,
    totalParticipants: groupSessionsData.reduce((sum, s) => sum + s.currentParticipants, 0)
  };

  return (
    <PageContainer>
      <PageHeader
        title="团沙管理"
        subtitle="管理团体沙盘治疗活动和参与者"
        actions={
          <div className="flex gap-md">
            <Button
              variant="secondary"
              leftIcon={<BarChart3 size={16} />}
              onClick={() => console.log('查看统计')}
            >
              统计报告
            </Button>
            <Button
              leftIcon={<Plus size={16} />}
              onClick={() => setShowCreateModal(true)}
            >
              创建团体活动
            </Button>
          </div>
        }
      />

      {/* 统计概览 - 横向排列优化 */}
      <div className="stats-overview mb-xl">
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.total}</div>
              <div className="stat-label">总团体数</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.active}</div>
              <div className="stat-label">进行中</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.planned}</div>
              <div className="stat-label">计划中</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.completed}</div>
              <div className="stat-label">已完成</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.totalParticipants}</div>
              <div className="stat-label">总参与者</div>
            </div>
          </CardContent>
          </Card>
        </div>

      {/* 搜索和筛选 */}
      <Card className="mb-xl">
        <CardContent>
          <FilterBar
            searchProps={{
              value: filters.search,
              onChange: (e) => handleUpdateFilters('search', e.target.value),
              placeholder: "搜索团体名称、治疗师或地点...",
              leftIcon: <Search size={16} />
            }}
            filters={[
              {
                value: filters.status,
                onChange: (e) => handleUpdateFilters('status', e.target.value),
                options: [
                  { value: '', label: '全部状态' },
                  { value: '计划中', label: '计划中' },
                  { value: '进行中', label: '进行中' },
                  { value: '已完成', label: '已完成' },
                  { value: '已取消', label: '已取消' },
                  { value: '暂停', label: '暂停' }
                ]
              },
              {
                value: filters.sessionType,
                onChange: (e) => handleUpdateFilters('sessionType', e.target.value),
                options: [
                  { value: '', label: '全部类型' },
                  { value: '开放式团体', label: '开放式团体' },
                  { value: '封闭式团体', label: '封闭式团体' },
                  { value: '主题团体', label: '主题团体' },
                  { value: '发展性团体', label: '发展性团体' }
                ]
              },
              {
                value: filters.targetAge,
                onChange: (e) => handleUpdateFilters('targetAge', e.target.value),
                options: [
                  { value: '', label: '全部年龄' },
                  { value: '儿童', label: '儿童' },
                  { value: '青少年', label: '青少年' },
                  { value: '成人', label: '成人' },
                  { value: '老年', label: '老年' },
                  { value: '混合', label: '混合' }
                ]
              }
            ]}
          />
        </CardContent>
      </Card>

      {/* 团体列表 */}
      {filteredSessions.length === 0 ? (
        <Card>
          <CardContent>
            <EmptyState
              icon={<UsersRound size={48} />}
              title="暂无团体活动"
              description="还没有创建任何团体沙盘治疗活动，点击上方按钮开始创建。"
              action={
                <Button
                  leftIcon={<Plus size={16} />}
                  onClick={() => setShowCreateModal(true)}
                >
                  创建团体活动
                </Button>
              }
            />
          </CardContent>
        </Card>
      ) : (
        <div className="group-sessions-grid">
          {filteredSessions.map(session => {
            const progress = getProgressInfo(session);
            return (
              <Card key={session.id} className="group-session-card">
                <CardContent>
                  <div className="session-card-header">
                    <div className="session-card-title">
                      <h3>{session.title}</h3>
                      {getStatusBadge(session.status)}
                    </div>
                    <div className="session-card-actions">
                      <ActionButtons
                        onView={() => handleViewSession(session)}
                        onEdit={() => handleEditSession(session)}
                        onDelete={() => handleDeleteSession(session)}
                        compact
                      />
                      {session.status === '计划中' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Play size={14} />}
                          onClick={() => console.log('开始', session.id)}
                        >
                          开始
                        </Button>
                      )}
                      {session.status === '进行中' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Pause size={14} />}
                          onClick={() => console.log('暂停', session.id)}
                        >
                          暂停
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="session-card-body">
                    <div className="session-description">
                      {session.description}
                    </div>

                    <div className="session-details-grid">
                      <div className="detail-item">
                        <div className="detail-label">治疗师</div>
                        <div className="detail-value">{session.therapistName}</div>
                      </div>

                      <div className="detail-item">
                        <div className="detail-label">类型</div>
                        <div className="detail-value">{session.sessionType}</div>
                      </div>

                      <div className="detail-item">
                        <div className="detail-label">目标年龄</div>
                        <div className="detail-value">
                          <span className="age-badge">{session.targetAge}</span>
                        </div>
                      </div>

                      <div className="detail-item">
                        <div className="detail-label">参与者</div>
                        <div className="detail-value">
                          <div className="participants-display">
                            <Users size={16} />
                            <span>{session.currentParticipants}/{session.maxParticipants}</span>
                          </div>
                        </div>
                      </div>

                      <div className="detail-item">
                        <div className="detail-label">进度</div>
                        <div className="detail-value">
                          <div className="progress-display">
                            <span className="progress-text">{progress.text}</span>
                            {progress.percentage > 0 && (
                              <div className="progress-bar-horizontal">
                                <div 
                                  className="progress-fill" 
                                  style={{ width: `${progress.percentage}%` }}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="detail-item">
                        <div className="detail-label">时间</div>
                        <div className="detail-value">
                          <div className="time-display">
                            <Clock size={14} />
                            <span>{session.frequency} {session.meetingTime}</span>
                          </div>
                        </div>
                      </div>

                      <div className="detail-item">
                        <div className="detail-label">地点</div>
                        <div className="detail-value">
                          <div className="location-display">
                            <MapPin size={14} />
                            <span>{session.location}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* 创建团体活动弹窗 */}
      {showCreateModal && (
        <CreateGroupSessionModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateSession}
        />
      )}

      {/* 团体详情弹窗 */}
      {showDetailModal && selectedSession && (
        <GroupSessionDetailModal
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedSession(null);
          }}
          session={selectedSession}
        />
      )}

      {/* 编辑团体活动弹窗 */}
      {showEditModal && editingSession && (
        <EditGroupSessionModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setEditingSession(null);
          }}
          session={editingSession}
          onUpdate={handleUpdateSession}
        />
      )}
    </PageContainer>
  );
};

export default GroupSessions;
