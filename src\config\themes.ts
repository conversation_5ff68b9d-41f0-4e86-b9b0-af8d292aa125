// 主题配置
export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
    status: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

export const defaultTheme: ThemeConfig = {
  name: '默认主题',
  colors: {
    primary: '#4F9CF9',
    secondary: '#2DD4BF',
    accent: '#F59E0B',
    background: '#f8fafc',
    surface: '#ffffff',
    text: {
      primary: '#0f172a',
      secondary: '#475569',
      tertiary: '#64748b'
    },
    status: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '0.75rem',
    lg: '1rem',
    xl: '1.25rem'
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem'
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)'
  }
};

// 深色主题
export const darkTheme: ThemeConfig = {
  ...defaultTheme,
  name: '深色主题',
  colors: {
    ...defaultTheme.colors,
    background: '#0f172a',
    surface: '#1e293b',
    text: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
      tertiary: '#94a3b8'
    }
  }
};

// 高对比度主题
export const highContrastTheme: ThemeConfig = {
  ...defaultTheme,
  name: '高对比度主题',
  colors: {
    primary: '#0066cc',
    secondary: '#009999',
    accent: '#ff6600',
    background: '#ffffff',
    surface: '#ffffff',
    text: {
      primary: '#000000',
      secondary: '#333333',
      tertiary: '#666666'
    },
    status: {
      success: '#008000',
      warning: '#ff8000',
      error: '#cc0000',
      info: '#0066cc'
    }
  }
};

export const themes = {
  default: defaultTheme,
  dark: darkTheme,
  highContrast: highContrastTheme
} as const;

export type ThemeName = keyof typeof themes;
