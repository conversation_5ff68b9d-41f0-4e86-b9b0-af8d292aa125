/* 帮助中心样式 */
@import '../../styles/variables.css';

.help-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* 搜索栏样式 */
.help-search-bar {
  margin-bottom: var(--spacing-3xl);
}

.help-search-input {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

.help-search-input input {
  width: 100%;
  padding-left: 44px;
  padding-right: 44px;
  font-size: var(--text-base);
  height: 48px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius);
  background: var(--bg-primary);
  transition: border-color var(--transition-fast);
}

.help-search-input input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 4px;
  transition: color var(--transition-fast);
}

.search-clear:hover {
  color: var(--danger-red);
}

.search-results-summary {
  text-align: center;
  margin-top: var(--spacing-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* 主布局 */
.help-layout {
  display: flex;
  gap: var(--spacing-3xl);
  align-items: flex-start;
}

/* 左侧导航 */
.help-sidebar {
  width: 320px;
  flex-shrink: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  position: sticky;
  top: var(--spacing-3xl);
  max-height: calc(100vh - 180px);
  overflow: hidden;
}

.help-sidebar.collapsed {
  width: 60px;
}

.help-sidebar-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
}

.sidebar-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

/* 导航组件样式 */
.help-navigation {
  padding: var(--spacing-md);
  overflow-y: auto;
  max-height: calc(100vh - 220px);
}

.navigation-category {
  margin-bottom: var(--spacing-lg);
}

.category-header {
  width: 100%;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  padding: var(--spacing-lg);
  border-radius: var(--radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--transition-normal);
  text-align: left;
  margin-bottom: var(--spacing-md);
}

.category-header:hover {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.category-header.active {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
  color: var(--text-inverse);
  border-color: var(--primary-blue);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.category-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  transition: color var(--transition-fast);
  margin: 0;
}

.category-header.active .category-title {
  color: var(--text-inverse);
}

.category-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.category-count {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.category-header.active .category-count {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
}

.category-header .toggle-icon {
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.category-header:hover .toggle-icon {
  color: var(--primary-blue);
  transform: scale(1.1);
}

.category-header.active .toggle-icon {
  color: var(--text-inverse);
}

.category-content {
  margin-top: var(--spacing-md);
  background: rgba(248, 250, 252, 0.5);
  border-radius: var(--radius);
  padding: var(--spacing-lg);
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all var(--transition-normal);
}

/* 简化的章节样式 - 作为分组显示，不需要展开/折叠 */
.navigation-section-simplified {
  margin-bottom: var(--spacing-xl);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.navigation-section-simplified:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.navigation-section-simplified:last-child {
  margin-bottom: 0;
}

.section-header-simplified {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-bottom: 1px solid var(--border-light);
}

.section-content-direct {
  padding: var(--spacing-md);
  background: var(--bg-primary);
}

.navigation-section {
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.section-header {
  width: 100%;
  background: transparent;
  border: none;
  padding: var(--spacing-md);
  border-radius: var(--radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--transition-normal);
  text-align: left;
  position: relative;
  border: 1px solid transparent;
}

.section-header:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.section-header.active {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--primary-blue);
  box-shadow: 0 2px 12px rgba(59, 130, 246, 0.2);
}

.section-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius);
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-blue);
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.section-header:hover .section-icon {
  background: var(--primary-blue);
  color: var(--text-inverse);
  transform: scale(1.05);
}

.section-header.active .section-icon {
  background: var(--primary-blue);
  color: var(--text-inverse);
}

.section-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.section-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  transition: color var(--transition-fast);
}

.section-header:hover .section-title {
  color: var(--primary-blue-dark);
}

.section-description {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  line-height: 1.3;
  transition: color var(--transition-fast);
}

.section-header:hover .section-description {
  color: var(--text-secondary);
}

.section-content {
  margin-top: var(--spacing-md);
  padding-left: var(--spacing-xl);
  border-left: 1px solid var(--border-light);
  margin-left: 16px;
}

.navigation-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius);
  cursor: pointer;
  margin-bottom: var(--spacing-xs);
  text-align: left;
  transition: background-color var(--transition-fast);
}

.navigation-item:hover {
  background: var(--bg-secondary);
}

.navigation-item.active {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.item-title {
  font-size: var(--text-sm);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-medium);
  transition: color var(--transition-fast);
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

.navigation-item:hover .item-title {
  color: var(--primary-blue-dark);
  background: transparent !important;
}

.navigation-item.active .item-title {
  color: var(--primary-blue-dark);
  font-weight: var(--font-semibold);
  background: transparent !important;
}

.item-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.item-tag {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background: rgba(248, 250, 252, 0.6) !important;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.navigation-item:hover .item-tag {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-blue);
}

.search-highlight {
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  color: var(--text-primary);
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: var(--font-medium);
}

/* 右侧内容区域 */
.help-content {
  flex: 1;
  min-width: 0;
}

/* 搜索结果导航样式 */
.help-search-navigation {
  padding: var(--spacing-md);
  overflow-y: auto;
  max-height: calc(100vh - 220px);
  background: var(--bg-primary);
}

.search-results-actions {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  text-align: center;
}

.search-results-nav-list {
  padding: var(--spacing-md) 0;
}

.search-result-nav-item {
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--bg-primary);
}

.search-result-nav-item:hover {
  border-color: var(--primary-blue);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.search-result-nav-item:last-child {
  margin-bottom: 0;
}

.result-nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.result-nav-title {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.result-match-badge {
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.result-match-badge.title {
  background: var(--success);
  color: var(--text-inverse);
}

.result-match-badge.tags {
  background: var(--warning);
  color: var(--text-inverse);
}

.result-match-badge.content {
  background: var(--info);
  color: var(--text-inverse);
}

.result-nav-breadcrumb {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.help-search-content-area {
  /* 搜索时右侧内容区域的样式 */
  min-height: 400px;
}

.search-suggestions {
  margin-top: var(--spacing-md);
  text-align: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.recent-viewed,
.search-history {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.recent-viewed:last-child,
.search-history:last-child,
.popular-searches:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.recent-items,
.history-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  justify-content: flex-start;
  margin-top: var(--spacing-sm);
}

.recent-viewed .suggestions-label,
.search-history .suggestions-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  text-align: left;
}

.recent-item,
.history-item {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  font-size: var(--text-sm);
  color: var(--success-dark);
  transition: all var(--transition-fast);
  font-weight: var(--font-medium);
}

.recent-item:hover,
.history-item:hover {
  background: linear-gradient(135deg, var(--success) 0%, var(--success-dark) 100%);
  color: var(--text-inverse);
  border-color: var(--success);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.popular-searches {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  align-items: center;
  justify-content: flex-start;
}

.suggestions-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-right: var(--spacing-sm);
  font-weight: var(--font-semibold);
  display: inline-block;
  align-self: center;
  flex-shrink: 0;
}

.popular-search-item {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-full);
  padding: var(--spacing-xs) var(--spacing-md);
  cursor: pointer;
  font-size: var(--text-sm);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  font-weight: var(--font-medium);
}

.popular-search-item:hover {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
  color: var(--text-inverse);
  border-color: var(--primary-blue);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 优化搜索建议样式 */
.help-search-results {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.search-results-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-results-header h2 {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.search-results-list {
  padding: var(--spacing-lg);
}

.search-result-item {
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
  border-radius: var(--radius);
  margin-bottom: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.search-result-item:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-md);
}

.search-result-item:last-child {
  margin-bottom: 0;
}

.result-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.result-item-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.result-match-type {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.result-match-type.title {
  background: var(--success);
  color: var(--text-inverse);
}

.result-match-type.tags {
  background: var(--warning);
  color: var(--text-inverse);
}

.result-match-type.content {
  background: var(--info);
  color: var(--text-inverse);
}

.result-item-breadcrumb {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.result-item-preview {
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-normal);
}

/* 内容显示样式 */
.help-content-container {
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.help-content-container:hover {
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* 空内容提示 */
.help-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 80px 20px;
  color: var(--text-secondary);
}

.help-empty-content .empty-icon {
  color: var(--border-light);
  margin-bottom: 16px;
}

.help-empty-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.help-empty-content p {
  font-size: 14px;
  line-height: 1.5;
  max-width: 300px;
}

/* 概览页面样式 */
.overview-items,
.overview-sections {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.overview-item,
.overview-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.overview-item:hover,
.overview-section:hover {
  background: var(--bg-hover);
  border-color: var(--primary-blue);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.overview-item:active,
.overview-section:active {
  transform: translateY(0);
}

.overview-title {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.overview-arrow {
  color: var(--text-tertiary);
  flex-shrink: 0;
}

.overview-section {
  padding: 20px;
}

.section-info {
  flex: 1;
}

.section-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 15px;
  margin-bottom: 4px;
}

.section-description {
  color: var(--text-secondary);
  font-size: 13px;
  line-height: 1.4;
}

.section-arrow {
  color: var(--text-tertiary);
  flex-shrink: 0;
  margin-left: 12px;
}

.section-overview h3,
.category-overview h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-light);
}

.help-breadcrumb {
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.breadcrumb-item {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius);
  transition: all var(--transition-fast);
}

.breadcrumb-link {
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--primary-blue);
  text-decoration: none;
}

.breadcrumb-link:hover {
  background: var(--bg-tertiary);
  color: var(--primary-blue-dark);
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.breadcrumb-separator {
  color: var(--text-tertiary);
  margin: 0 var(--spacing-xs);
}

.help-content-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.content-title {
  margin: 0 0 var(--spacing-md);
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.content-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  border-radius: var(--radius);
}

.content-tags {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.content-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  color: var(--primary-blue);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  transition: all var(--transition-fast);
}

.content-tag:hover {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: var(--text-inverse);
  border-color: var(--primary-blue);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.help-content-body {
  padding: var(--spacing-xl);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%);
  min-height: 400px;
}

.content-description {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary-blue);
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.content-description::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
}

.content-description p {
  margin: 0;
  font-size: var(--text-base);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
  position: relative;
  z-index: 1;
}

/* 步骤指南样式 */
.step-guide {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.step-item {
  margin-bottom: var(--spacing-3xl);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.step-number {
  width: 32px;
  height: 32px;
  background: var(--primary-blue);
  color: var(--text-inverse);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  flex-shrink: 0;
}

.step-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.step-content {
  padding: var(--spacing-xl);
}

.step-description {
  margin: 0 0 var(--spacing-lg);
  font-size: var(--text-base);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
}

.step-tips {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: rgba(16, 185, 129, 0.05);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: var(--radius);
}

.tips-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.tips-icon {
  color: var(--success);
}

.tips-header span {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--success-dark);
}

.tips-list {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.tip-item {
  margin-bottom: var(--spacing-sm);
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-normal);
}

.tip-item:last-child {
  margin-bottom: 0;
}

.step-warning {
  padding: var(--spacing-lg);
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: var(--radius);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.warning-icon {
  color: var(--warning);
  flex-shrink: 0;
  margin-top: 2px;
}

.warning-text {
  font-size: var(--text-sm);
  color: var(--warning-dark);
  line-height: var(--leading-normal);
}

/* FAQ样式 */
.faq-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.faq-item {
  margin-bottom: var(--spacing-3xl);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.faq-item:last-child {
  margin-bottom: 0;
}

.faq-question {
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.faq-question h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.faq-answer {
  padding: var(--spacing-xl);
}

.faq-answer p {
  margin: 0 0 var(--spacing-lg);
  font-size: var(--text-base);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
}

.faq-tips {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius);
}

.faq-tips strong {
  color: var(--text-primary);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-md);
  display: block;
}

.faq-steps {
  margin: var(--spacing-md) 0 0;
  padding-left: var(--spacing-lg);
}

.faq-steps li {
  margin-bottom: var(--spacing-sm);
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-normal);
}

/* 联系我们样式 */
.contact-section {
  padding: var(--spacing-lg);
}

.contact-intro {
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

.contact-intro h3 {
  margin: 0 0 var(--spacing-md);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.contact-intro p {
  margin: 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
}

.contact-card {
  padding: var(--spacing-xl);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.contact-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.contact-wechat {
  border-color: rgba(34, 197, 94, 0.3);
}

.contact-wechat:hover {
  border-color: var(--success);
}

.contact-email {
  border-color: rgba(59, 130, 246, 0.3);
}

.contact-email:hover {
  border-color: var(--primary-blue);
}

.contact-phone {
  border-color: rgba(245, 158, 11, 0.3);
}

.contact-phone:hover {
  border-color: var(--warning);
}

.contact-website {
  border-color: rgba(139, 92, 246, 0.3);
}

.contact-website:hover {
  border-color: var(--accent-purple);
}

.contact-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.contact-icon {
  padding: var(--spacing-md);
  border-radius: var(--radius);
  color: var(--text-inverse);
}

.contact-wechat .contact-icon {
  background: var(--success);
}

.contact-email .contact-icon {
  background: var(--primary-blue);
}

.contact-phone .contact-icon {
  background: var(--warning);
}

.contact-website .contact-icon {
  background: var(--accent-purple);
}

.contact-label {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.contact-body {
  text-align: center;
}

.contact-qr-section {
  margin-bottom: var(--spacing-lg);
}

.qr-placeholder {
  width: 120px;
  height: 120px;
  background: var(--bg-secondary);
  border: 2px dashed var(--border-medium);
  border-radius: var(--radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  color: var(--text-tertiary);
}

.qr-icon {
  margin-bottom: var(--spacing-sm);
}

.qr-text {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.qr-subtitle {
  margin: 0;
  font-size: var(--text-xs);
}

.qr-code {
  width: 120px;
  height: 120px;
  border-radius: var(--radius);
  margin: 0 auto var(--spacing-md);
  display: block;
  border: 1px solid var(--border-light);
}

.contact-value-section {
  margin-bottom: var(--spacing-lg);
}

.contact-value {
  margin: 0 0 var(--spacing-sm);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.contact-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-blue);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: color var(--transition-fast);
}

.contact-link:hover {
  color: var(--primary-blue-dark);
}

.contact-description {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
}

.contact-note {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.note-icon {
  color: var(--info);
  flex-shrink: 0;
  margin-top: 2px;
}

.contact-note p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
}

/* 相关内容头部推荐样式 */
.help-related-content-header {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 197, 253, 0.03) 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: var(--radius);
}

.help-related-content-header h4 {
  margin: 0 0 var(--spacing-sm);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.related-items-horizontal {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  align-items: center;
}

.related-item-compact {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.related-item-compact:hover {
  border-color: var(--primary-blue);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.related-title-compact {
  font-size: var(--text-xs);
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.more-content-hint {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-style: italic;
}

/* 原有的相关内容样式保持不变，用于底部完整列表 */
.help-related-content {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.help-related-content h3 {
  margin: 0 0 var(--spacing-lg);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.related-items {
  display: grid;
  gap: var(--spacing-md);
}

.related-item {
  width: 100%;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius);
  padding: var(--spacing-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all var(--transition-fast);
  text-align: left;
}

.related-item:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-sm);
}

.related-title {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.related-arrow {
  color: var(--text-tertiary);
  transition: transform var(--transition-fast);
}

.related-item:hover .related-arrow {
  transform: translateX(4px);
  color: var(--primary-blue);
}

/* 快速开始引导按钮 */
.help-quick-start {
  position: fixed;
  bottom: var(--spacing-3xl);
  left: var(--spacing-3xl);
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--success) 0%, var(--success-dark) 100%);
  color: var(--text-inverse);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  transition: all var(--transition-normal);
  z-index: var(--z-fixed);
  backdrop-filter: blur(10px);
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
}

.help-quick-start::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(5, 150, 105, 0.3));
  border-radius: var(--radius-full);
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.help-quick-start:hover {
  background: linear-gradient(135deg, var(--success-dark), var(--success));
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
  border-color: rgba(255, 255, 255, 0.5);
}

.help-quick-start:hover::before {
  opacity: 1;
}

.quick-start-text {
  margin: 0;
}

/* 返回顶部按钮 */
.help-back-to-top {
  position: fixed;
  bottom: var(--spacing-3xl);
  right: var(--spacing-3xl);
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: var(--text-inverse);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transition: all var(--transition-normal);
  z-index: var(--z-fixed);
  backdrop-filter: blur(10px);
}

.help-back-to-top::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.3));
  border-radius: var(--radius-full);
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.help-back-to-top:hover {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
  border-color: rgba(255, 255, 255, 0.5);
}

.help-back-to-top:hover::before {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .help-layout {
    flex-direction: column;
    gap: var(--spacing-xl);
  }

  .help-sidebar {
    width: 100%;
    position: static;
    max-height: none;
  }

  .help-sidebar.collapsed {
    width: 100%;
  }

  .help-navigation,
  .help-search-navigation {
    max-height: 300px; /* 在平板设备上限制导航高度 */
  }

  /* 平板设备上的相关内容优化 */
  .related-items-horizontal {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .help-container {
    padding: 0 var(--spacing-lg);
  }

  .help-search-input {
    max-width: 100%;
  }

  .help-search-input input {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 移动端导航优化 */
  .help-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: var(--z-modal);
    background: var(--bg-primary);
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
  }

  .help-sidebar.mobile-open {
    transform: translateX(0);
  }

  .help-sidebar-header {
    position: sticky;
    top: 0;
    background: var(--bg-primary);
    z-index: 10;
    border-bottom: 2px solid var(--border-light);
  }

  .mobile-nav-toggle {
    display: block;
    background: none;
    border: none;
    padding: var(--spacing-sm);
    cursor: pointer;
    color: var(--text-secondary);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
  }

  .mobile-nav-toggle:hover {
    background: var(--bg-secondary);
    color: var(--primary-blue);
  }

  /* 移动端搜索导航优化 */
  .help-search-navigation {
    padding: var(--spacing-sm);
  }

  .search-result-nav-item {
    padding: var(--spacing-sm);
  }

  /* 移动端内容区域优化 */
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .search-results-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .help-back-to-top {
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 44px;
    height: 44px;
  }

  .help-quick-start {
    bottom: var(--spacing-xl);
    left: var(--spacing-xl);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-xs);
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .step-number {
    align-self: flex-start;
  }

  /* 相关内容在移动端的优化 */
  .related-items-horizontal {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-xs);
  }

  .related-item-compact {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .help-content-header,
  .help-content-body,
  .help-related-content {
    padding: var(--spacing-lg);
  }

  .help-breadcrumb {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .content-title {
    font-size: var(--text-xl);
  }

  .step-content {
    padding: var(--spacing-lg);
  }

  .contact-card {
    padding: var(--spacing-lg);
  }

  /* 非常小的屏幕上隐藏移动端导航打开按钮 */
  .mobile-nav-open-btn {
    display: none;
  }

  /* 优化搜索建议在小屏幕上的显示 */
  .popular-searches,
  .recent-items,
  .history-items {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-xs);
  }

  .popular-search-item,
  .recent-item,
  .history-item {
    width: 100%;
    text-align: center;
    justify-content: center;
  }

  .suggestions-label {
    text-align: center;
    margin-bottom: var(--spacing-sm);
  }
}

/* 添加移动端导航打开按钮的隐藏/显示逻辑 */
.mobile-nav-open-btn {
  display: none;
}

@media (max-width: 768px) {
  .mobile-nav-open-btn {
    display: flex;
    position: fixed;
    top: var(--spacing-xl);
    left: var(--spacing-lg);
    width: 44px;
    height: 44px;
    background: var(--primary-blue);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-full);
    align-items: center;
    justify-content: center;
    z-index: var(--z-fixed);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transition: all var(--transition-normal);
  }

  .mobile-nav-open-btn:hover {
    background: var(--primary-blue-dark);
    transform: scale(1.05);
  }

  .mobile-nav-toggle {
    display: block;
  }
}

/* 默认隐藏移动端导航切换按钮 */
@media (min-width: 769px) {
  .mobile-nav-toggle {
    display: none;
  }
}
