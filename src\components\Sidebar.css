/* Sidebar组件样式 */
.sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid #e5e7eb;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  transition: width 0.2s ease;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.sidebar::-webkit-scrollbar {
  display: none;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 60px; /* Header高度 */
}

.menu-section {
  flex: 1;
  padding: 16px 10px;
}

.menu-section.bottom {
  flex: none;
  border-top: 1px solid #e5e7eb;
  padding: 16px 10px;
}

/* 开发工具区域样式 (临时) */
.menu-section.dev-tools {
  border-top: 1px solid #fbbf24;
  border-bottom: 1px solid #fbbf24;
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  margin: 8px 0;
  border-radius: 8px;
}

.section-title {
  font-size: 11px;
  font-weight: 600;
  color: #92400e;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 4px;
  opacity: 0.8;
}

.menu-section.dev-tools .menu-item {
  background: rgba(255, 255, 255, 0.3);
  color: #92400e;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.menu-section.dev-tools .menu-item:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #78350f;
}

.menu-section.dev-tools .menu-item.active {
  background: #f59e0b;
  color: white;
  border-color: #d97706;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 10px 14px;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  margin-bottom: 2px;
  text-align: left;
}

.menu-item:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.menu-item.active {
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  color: white;
}

.menu-item.active:hover {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.menu-item.collapsed {
  justify-content: center;
  padding: 10px;
  width: 40px;
  height: 40px;
  margin: 0 auto 4px auto;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.menu-label {
  white-space: nowrap;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
}
