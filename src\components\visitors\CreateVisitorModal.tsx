import React, { useState } from 'react';
import {
  FormModal,
  Input,
  Select,
  Textarea
} from '../ui/index';
import { visitorService } from '../../services/visitorService';
import type { Visitor, CreateVisitorForm } from '../../types/visitor';

interface CreateVisitorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (visitor: Visitor) => void;
}

const CreateVisitorModal: React.FC<CreateVisitorModalProps> = ({ 
  isOpen, 
  onClose, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState<CreateVisitorForm>({
    name: '',
    gender: '',
    age: '',
    phone: '',
    email: '',
    emergencyContact: '',
    emergencyPhone: '',
    occupation: '',
    education: '',
    address: '',
    notes: '',
    status: '活跃'
  });

  const [errors, setErrors] = useState<Partial<Record<keyof CreateVisitorForm, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof CreateVisitorForm, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof CreateVisitorForm, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入姓名';
    }

    if (!formData.age || Number(formData.age) < 1 || Number(formData.age) > 120) {
      newErrors.age = '请输入有效年龄（1-120）';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号码';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入有效的手机号码';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    if (!formData.emergencyContact.trim()) {
      newErrors.emergencyContact = '请输入紧急联系人';
    }

    if (!formData.emergencyPhone.trim()) {
      newErrors.emergencyPhone = '请输入紧急联系电话';
    } else if (!/^1[3-9]\d{9}$/.test(formData.emergencyPhone)) {
      newErrors.emergencyPhone = '请输入有效的紧急联系电话';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const visitorData = {
        ...formData,
        age: Number(formData.age),
        status: '活跃' as const
      };

      const newVisitor = await visitorService.createVisitor(visitorData);
      onSubmit(newVisitor);
      
      // 重置表单
      setFormData({
        name: '',
        gender: '',
        age: '',
        phone: '',
        email: '',
        emergencyContact: '',
        emergencyPhone: '',
        occupation: '',
        education: '',
        address: '',
        notes: '',
        status: '活跃'
      });
      
      onClose();
    } catch (error) {
      console.error('创建来访者失败:', error);
      alert('创建来访者失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="新增来访者"
      subtitle="请填写来访者的基本信息"
      size="lg"
      onSubmit={handleSubmit}
      submitText="创建来访者"
      isSubmitting={isSubmitting}
    >
      {/* 基本信息 */}
      <div className="form-section">
        <h4 className="form-section-title">基本信息</h4>
        
        <div className="form-grid-3">
          <Input
            label="姓名"
            required
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={errors.name}
            placeholder="请输入姓名"
          />
          
          <Select
            label="性别"
            required
            value={formData.gender}
            onChange={(e) => handleInputChange('gender', e.target.value as '男' | '女')}
            options={[
              { value: '男', label: '男' },
              { value: '女', label: '女' }
            ]}
          />

          <Input
            label="年龄"
            type="number"
            required
            value={formData.age}
            onChange={(e) => handleInputChange('age', e.target.value)}
            error={errors.age}
            placeholder="请输入年龄"
            min="1"
            max="120"
          />
        </div>
        
        <div className="form-grid-3">
          <Select
            label="状态"
            required
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value as '活跃' | '暂停' | '完成')}
            options={[
              { value: '活跃', label: '活跃' },
              { value: '暂停', label: '暂停' },
              { value: '完成', label: '完成' }
            ]}
          />
        </div>
      </div>

      {/* 联系信息 */}
      <div className="form-section">
        <h4 className="form-section-title">联系信息</h4>
        
        <div className="form-grid-2">
          <Input
            label="手机号码"
            required
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            error={errors.phone}
            placeholder="请输入手机号码"
          />
          
          <Input
            label="邮箱"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={errors.email}
            placeholder="请输入邮箱（可选）"
          />
        </div>
        
        <div className="form-grid-2">
          <Input
            label="紧急联系人"
            required
            value={formData.emergencyContact}
            onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
            error={errors.emergencyContact}
            placeholder="请输入紧急联系人姓名"
          />
          
          <Input
            label="紧急联系电话"
            required
            value={formData.emergencyPhone}
            onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
            error={errors.emergencyPhone}
            placeholder="请输入紧急联系电话"
          />
        </div>
      </div>

      {/* 其他信息 */}
      <div className="form-section">
        <h4 className="form-section-title">其他信息</h4>
        
        <div className="form-grid-2">
          <Input
            label="职业"
            value={formData.occupation}
            onChange={(e) => handleInputChange('occupation', e.target.value)}
            placeholder="请输入职业（可选）"
          />
          
          <Select
            label="学历"
            value={formData.education}
            onChange={(e) => handleInputChange('education', e.target.value)}
            options={[
              { value: '', label: '请选择学历' },
              { value: '小学', label: '小学' },
              { value: '初中', label: '初中' },
              { value: '高中', label: '高中' },
              { value: '中专', label: '中专' },
              { value: '大专', label: '大专' },
              { value: '本科', label: '本科' },
              { value: '硕士', label: '硕士' },
              { value: '博士', label: '博士' }
            ]}
          />
        </div>
        
        <Textarea
          label="地址"
          value={formData.address}
          onChange={(e) => handleInputChange('address', e.target.value)}
          placeholder="请输入详细地址（可选）"
          rows={2}
        />
        
        <Textarea
          label="备注"
          value={formData.notes}
          onChange={(e) => handleInputChange('notes', e.target.value)}
          placeholder="请输入备注信息（可选）"
          rows={3}
        />
      </div>
    </FormModal>
  );
};

export { CreateVisitorModal };
export default CreateVisitorModal;
