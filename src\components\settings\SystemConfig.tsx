import React from 'react';
import { Card } from '../ui/Card';
import { Input, Select, Textarea } from '../ui/Form';
import type { AppSettings } from '../../types/settings';

interface SystemConfigProps {
  settings: AppSettings;
  updateSettings: (path: string, value: any) => void;
  saving: boolean;
}

const SystemConfig: React.FC<SystemConfigProps> = ({ settings, updateSettings }) => {
  const { system } = settings;

  return (
    <div className="settings-form">
      {/* 机构信息 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">机构信息</h3>
          <p className="card-subtitle">机构基本信息设置</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Input
              label="机构名称"
              value={system.organization.name}
              onChange={(e) => updateSettings('system.organization.name', e.target.value)}
              placeholder="请输入机构名称"
              required
            />
            
            <Input
              label="联系电话"
              value={system.organization.phone}
              onChange={(e) => updateSettings('system.organization.phone', e.target.value)}
              placeholder="机构联系电话"
            />
            
            <Input
              label="邮箱地址"
              type="email"
              value={system.organization.email}
              onChange={(e) => updateSettings('system.organization.email', e.target.value)}
              placeholder="<EMAIL>"
            />
            
            <Input
              label="官方网站"
              value={system.organization.website || ''}
              onChange={(e) => updateSettings('system.organization.website', e.target.value)}
              placeholder="https://www.example.com"
            />
          </div>
          
          <div className="mt-lg">
            <Textarea
              label="机构地址"
              value={system.organization.address}
              onChange={(e) => updateSettings('system.organization.address', e.target.value)}
              placeholder="请输入详细地址"
              rows={2}
            />
          </div>
          
          <div className="mt-lg">
            <Textarea
              label="机构简介"
              value={system.organization.description || ''}
              onChange={(e) => updateSettings('system.organization.description', e.target.value)}
              placeholder="机构简介和服务介绍"
              rows={3}
            />
          </div>
        </div>
      </Card>

      {/* 业务配置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">业务配置</h3>
          <p className="card-subtitle">业务相关参数设置</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Input
              label="默认收费标准"
              type="number"
              value={system.business.defaultPrice.toString()}
              onChange={(e) => updateSettings('system.business.defaultPrice', parseFloat(e.target.value) || 0)}
              placeholder="200"
              min="0"
              step="10"
            />
            
            <Select
              label="货币单位"
              value={system.business.currency}
              onChange={(e) => updateSettings('system.business.currency', e.target.value)}
              options={[
                { value: '¥', label: '人民币 (¥)' },
                { value: '$', label: '美元 ($)' },
                { value: '€', label: '欧元 (€)' },
                { value: '£', label: '英镑 (£)' }
              ]}
            />
            
            <Input
              label="税率"
              type="number"
              value={system.business.taxRate.toString()}
              onChange={(e) => updateSettings('system.business.taxRate', parseFloat(e.target.value) || 0)}
              placeholder="0"
              min="0"
              max="100"
              step="0.1"
            />
            
            <Select
              label="发票模板"
              value={system.business.invoiceTemplate}
              onChange={(e) => updateSettings('system.business.invoiceTemplate', e.target.value)}
              options={[
                { value: 'standard', label: '标准模板' },
                { value: 'simple', label: '简单模板' },
                { value: 'detailed', label: '详细模板' },
                { value: 'custom', label: '自定义模板' }
              ]}
            />
          </div>
          
          <div className="mt-lg">
            <label className="form-label">可选咨询时长（分钟）</label>
            <div className="settings-form-row">
              {system.business.sessionDurations.map((duration, index) => (
                <Input
                  key={index}
                  type="number"
                  value={duration.toString()}
                  onChange={(e) => {
                    const newDurations = [...system.business.sessionDurations];
                    newDurations[index] = parseInt(e.target.value) || 0;
                    updateSettings('system.business.sessionDurations', newDurations);
                  }}
                  min="15"
                  max="300"
                  step="5"
                />
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* 数据策略 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">数据策略</h3>
          <p className="card-subtitle">数据保留和管理策略</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Input
              label="自动删除天数"
              type="number"
              value={system.dataPolicy.autoDeleteDays.toString()}
              onChange={(e) => updateSettings('system.dataPolicy.autoDeleteDays', parseInt(e.target.value) || 0)}
              placeholder="365"
              min="0"
              help="设置为0表示永不自动删除"
            />
            
            <Input
              label="自动归档天数"
              type="number"
              value={system.dataPolicy.archiveAfterDays.toString()}
              onChange={(e) => updateSettings('system.dataPolicy.archiveAfterDays', parseInt(e.target.value) || 0)}
              placeholder="180"
              min="0"
              help="超过此天数的数据将被归档"
            />
            
            <Input
              label="最大备份数量"
              type="number"
              value={system.dataPolicy.maxBackups.toString()}
              onChange={(e) => updateSettings('system.dataPolicy.maxBackups', parseInt(e.target.value) || 1)}
              placeholder="10"
              min="1"
              max="50"
            />
          </div>
          
          <div className="mt-lg">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={system.dataPolicy.compressionEnabled}
                onChange={(e) => updateSettings('system.dataPolicy.compressionEnabled', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用数据压缩（减少存储空间）
            </label>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SystemConfig;
