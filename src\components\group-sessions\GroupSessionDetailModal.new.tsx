import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { Button } from '../ui/Button';
import { Badge } from '../ui/DataDisplay';
import { 
  Users, 
  Calendar, 
  Clock, 
  MapPin, 
  User, 
  FileText,
  Activity,
  Edit,
  Trash2
} from 'lucide-react';
import type { GroupSession } from '../../types/groupSession';
import './GroupSessionDetailModal.new.css';

interface GroupSessionDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  session: GroupSession | null;
  onEdit?: (session: GroupSession) => void;
  onDelete?: (session: GroupSession) => void;
}

export const GroupSessionDetailModal: React.FC<GroupSessionDetailModalProps> = ({
  isOpen,
  onClose,
  session,
  onEdit,
  onDelete
}) => {
  if (!session) return null;

  const handleEdit = () => {
    if (onEdit) {
      onEdit(session);
    }
    onClose();
  };

  const handleDelete = () => {
    if (onDelete && window.confirm(`确定要删除团体活动"${session.title}"吗？此操作不可恢复。`)) {
      onDelete(session);
      onClose();
    }
  };

  // 状态颜色映射
  const getStatusColor = (status: string): 'primary' | 'success' | 'warning' | 'danger' | 'gray' => {
    switch (status) {
      case '计划中': return 'warning';
      case '进行中': return 'success';
      case '已完成': return 'gray';
      case '已取消': return 'danger';
      case '暂停': return 'warning';
      default: return 'gray';
    }
  };

  // 获取进度信息
  const getProgressInfo = () => {
    if (session.totalSessions && session.currentSession !== undefined) {
      const progress = (session.currentSession / session.totalSessions) * 100;
      return {
        text: `${session.currentSession}/${session.totalSessions} 次`,
        percentage: Math.round(progress)
      };
    }
    return {
      text: '进行中',
      percentage: 0
    };
  };

  const progressInfo = getProgressInfo();

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={session.title}
      subtitle={`${session.sessionType} - ${session.targetAge}`}
      size="lg"
    >
      <div className="group-session-detail-content">
        {/* 主要信息区域 */}
        <div className="detail-main-info">
          <div className="info-grid">
            <div className="info-section">
              <div className="section-header">
                <FileText size={16} className="section-icon" />
                <h4 className="section-title">基本信息</h4>
              </div>
              <div className="info-items">
                <div className="info-item">
                  <span className="info-label">状态</span>
                  <Badge variant={getStatusColor(session.status)}>
                    {session.status}
                  </Badge>
                </div>
                <div className="info-item">
                  <span className="info-label">团体类型</span>
                  <span className="info-value">{session.sessionType}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">目标年龄</span>
                  <span className="info-value">{session.targetAge}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <User size={14} />
                    治疗师
                  </span>
                  <span className="info-value">{session.therapistName}</span>
                </div>
              </div>
            </div>

            <div className="info-section">
              <div className="section-header">
                <Calendar size={16} className="section-icon" />
                <h4 className="section-title">时间安排</h4>
              </div>
              <div className="info-items">
                <div className="info-item">
                  <span className="info-label">开始日期</span>
                  <span className="info-value">{session.startDate}</span>
                </div>
                {session.endDate && (
                  <div className="info-item">
                    <span className="info-label">结束日期</span>
                    <span className="info-value">{session.endDate}</span>
                  </div>
                )}
                <div className="info-item">
                  <span className="info-label">
                    <Clock size={14} />
                    会议时间
                  </span>
                  <span className="info-value">{session.meetingTime}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">活动时长</span>
                  <span className="info-value">{session.duration} 分钟</span>
                </div>
              </div>
            </div>

            <div className="info-section">
              <div className="section-header">
                <Users size={16} className="section-icon" />
                <h4 className="section-title">参与情况</h4>
              </div>
              <div className="info-items">
                <div className="info-item">
                  <span className="info-label">当前人数</span>
                  <span className="info-value">{session.currentParticipants} 人</span>
                </div>
                <div className="info-item">
                  <span className="info-label">最大容量</span>
                  <span className="info-value">{session.maxParticipants} 人</span>
                </div>
                <div className="info-item">
                  <span className="info-label">活动频率</span>
                  <span className="info-value">{session.frequency}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <MapPin size={14} />
                    活动地点
                  </span>
                  <span className="info-value">{session.location}</span>
                </div>
              </div>
            </div>

            {/* 进度信息 */}
            {session.totalSessions && (
              <div className="info-section">
                <div className="section-header">
                  <Activity size={16} className="section-icon" />
                  <h4 className="section-title">进度信息</h4>
                </div>
                <div className="info-items">
                  <div className="info-item">
                    <span className="info-label">总次数</span>
                    <span className="info-value">{session.totalSessions} 次</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">当前进度</span>
                    <span className="info-value">{progressInfo.text}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">完成百分比</span>
                    <span className="info-value">{progressInfo.percentage}%</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 次要信息区域 */}
        <div className="detail-secondary-info">
          {/* 描述 */}
          {session.description && (
            <div className="info-section full-width">
              <div className="section-header">
                <h4 className="section-title">团体描述</h4>
              </div>
              <div className="description-content">
                {session.description}
              </div>
            </div>
          )}

          {/* 参与要求 */}
          {session.requirements && (
            <div className="info-section">
              <div className="section-header">
                <h4 className="section-title">参与要求</h4>
              </div>
              <div className="requirements-content">
                {session.requirements}
              </div>
            </div>
          )}

          {/* 材料清单 */}
          {session.materials && session.materials.length > 0 && (
            <div className="info-section">
              <div className="section-header">
                <h4 className="section-title">所需材料</h4>
              </div>
              <div className="materials-list">
                {session.materials.map((material, index) => (
                  <Badge key={index} variant="gray">
                    {material}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* 备注 */}
          {session.notes && (
            <div className="info-section full-width">
              <div className="section-header">
                <h4 className="section-title">备注</h4>
              </div>
              <div className="notes-content">
                {session.notes}
              </div>
            </div>
          )}

          {/* 参与者列表 */}
          {session.participants && session.participants.length > 0 && (
            <div className="info-section full-width">
              <div className="section-header">
                <Users size={16} className="section-icon" />
                <h4 className="section-title">参与者列表</h4>
              </div>
              <div className="participants-list">
                {session.participants.map((participant) => (
                  <div key={participant.id} className="participant-item">
                    <div className="participant-info">
                      <span className="participant-name">{participant.visitorName}</span>
                      <span className="participant-details">
                        {participant.age}岁 · {participant.gender}
                      </span>
                    </div>
                    <Badge variant={participant.status === '已确认' ? 'success' : 'warning'}>
                      {participant.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="modal-actions">
        <div className="actions-left">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            关闭
          </Button>
        </div>
        <div className="actions-right">
          {onEdit && (
            <Button
              variant="primary"
              leftIcon={<Edit size={14} />}
              onClick={handleEdit}
            >
              编辑
            </Button>
          )}
          {onDelete && (
            <Button
              variant="danger"
              leftIcon={<Trash2 size={14} />}
              onClick={handleDelete}
            >
              删除
            </Button>
          )}
        </div>
      </div>
    </BaseModal>
  );
};
