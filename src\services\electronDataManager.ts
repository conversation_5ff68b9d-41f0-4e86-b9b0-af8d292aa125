// Electron 环境下的 SQLite 数据管理器
import type { SimpleCase } from '../types/case';
import type { Visitor } from '../types/visitor';
import type { GroupSession } from '../types/groupSession';
import type { SandTool, SandToolUsageRecord, SandToolMaintenanceRecord } from '../types/sandtool';
import type { Appointment, Room } from '../types/schedule';
import type { AppSettings } from '../types/settings';

declare global {
  interface Window {
    electronAPI?: {
      dbQuery: (sql: string, params?: any[]) => Promise<any[]>;
      dbRun: (sql: string, params?: any[]) => Promise<{ changes: number; lastInsertRowid: number }>;
      getAppVersion: () => Promise<string>;
      getAppPath: (name: string) => Promise<string>;
      // 浏览器功能
      openExternal: (url: string) => Promise<{ success: boolean; error?: string }>;
      showItemInFolder: (path: string) => Promise<{ success: boolean; error?: string }>;
      platform: string;
      isElectron: boolean;
    };
  }
}

export class ElectronDataManager {
  private isElectron: boolean;

  constructor() {
    this.isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
  }

  // 检查是否在 Electron 环境中
  isElectronEnvironment(): boolean {
    return this.isElectron;
  }

  // 执行 SQL 查询
  private async query(sql: string, params: any[] = []): Promise<any[]> {
    if (!this.isElectron) {
      throw new Error('此方法只能在 Electron 环境中使用');
    }
    if (!window.electronAPI) {
      throw new Error('Electron API不可用');
    }
    return await window.electronAPI.dbQuery(sql, params);
  }

  // 执行 SQL 命令
  private async run(sql: string, params: any[] = []): Promise<{ changes: number; lastInsertRowid: number }> {
    if (!this.isElectron) {
      throw new Error('此方法只能在 Electron 环境中使用');
    }
    if (!window.electronAPI) {
      throw new Error('Electron API不可用');
    }
    return await window.electronAPI.dbRun(sql, params);
  }

  // 来访者操作
  async getAllVisitors(): Promise<Visitor[]> {
    if (!this.isElectron) {
      // 浏览器环境返回模拟数据
      const { testVisitorGenerator } = await import('../utils/generateTestVisitors');
      const mockVisitors = testVisitorGenerator.generateTestVisitors(10);
      // 添加ID和时间戳
      return mockVisitors.map((visitor, index) => ({
        ...visitor,
        id: `visitor-${index + 1}`,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      }));
    }
    const rows = await this.query('SELECT * FROM visitors ORDER BY created_at DESC');
    return rows.map(this.mapVisitorFromDb);
  }

  async getVisitor(id: string): Promise<Visitor | null> {
    if (!this.isElectron) {
      // 浏览器环境返回模拟数据
      const visitors = await this.getAllVisitors();
      return visitors.find(v => v.id === id) || null;
    }
    const rows = await this.query('SELECT * FROM visitors WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapVisitorFromDb(rows[0]) : null;
  }

  async saveVisitor(visitor: Visitor): Promise<void> {
    if (!this.isElectron) {
      // 浏览器环境下只是模拟保存
      console.log('浏览器环境：模拟保存来访者数据', visitor);
      return;
    }
    const exists = await this.getVisitor(visitor.id);
    
    if (exists) {
      await this.run(`
        UPDATE visitors SET 
          name = ?, gender = ?, age = ?, phone = ?, email = ?,
          emergency_contact = ?, emergency_phone = ?, occupation = ?,
          education = ?, address = ?, notes = ?, status = ?, updated_at = ?
        WHERE id = ?
      `, [
        visitor.name, visitor.gender, visitor.age, visitor.phone, visitor.email,
        visitor.emergencyContact, visitor.emergencyPhone, visitor.occupation,
        visitor.education, visitor.address, visitor.notes, visitor.status,
        new Date().toISOString(), visitor.id
      ]);
    } else {
      await this.run(`
        INSERT INTO visitors (
          id, name, gender, age, phone, email, emergency_contact,
          emergency_phone, occupation, education, address, notes,
          status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        visitor.id, visitor.name, visitor.gender, visitor.age, visitor.phone,
        visitor.email, visitor.emergencyContact, visitor.emergencyPhone,
        visitor.occupation, visitor.education, visitor.address, visitor.notes,
        visitor.status, visitor.createdAt, visitor.updatedAt
      ]);
    }
  }

  async deleteVisitor(id: string): Promise<void> {
    await this.run('DELETE FROM visitors WHERE id = ?', [id]);
  }

  // 个案操作
  async getAllCases(): Promise<SimpleCase[]> {
    if (!this.isElectron) {
      // 浏览器环境返回模拟数据
      const mockCases = await import('../data/mockCases');
      return mockCases.mockCases;
    }
    const rows = await this.query('SELECT * FROM cases ORDER BY created_at DESC');
    return rows.map(this.mapCaseFromDb);
  }

  async getCase(id: string): Promise<SimpleCase | null> {
    if (!this.isElectron) {
      // 浏览器环境返回模拟数据
      const mockCases = await import('../data/mockCases');
      return mockCases.mockCases.find(c => c.id === id) || null;
    }
    const rows = await this.query('SELECT * FROM cases WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapCaseFromDb(rows[0]) : null;
  }

  async saveCase(caseData: SimpleCase): Promise<void> {
    if (!this.isElectron) {
      // 浏览器环境下只是模拟保存
      console.log('浏览器环境：模拟保存个案数据', caseData);
      return;
    }
    const exists = await this.getCase(caseData.id);
    
    if (exists) {
      await this.run(`
        UPDATE cases SET 
          visitor_id = ?, name = ?, summary = ?, therapy_method = ?, selected_sand_tools = ?, last_date = ?, next_date = ?,
          total = ?, star = ?, duration = ?, crisis = ?, homework = ?,
          progress = ?, keywords = ?, supervision = ?, updated_at = ?
        WHERE id = ?
      `, [
        caseData.visitorId, caseData.name, caseData.summary, caseData.therapyMethod, 
        JSON.stringify(caseData.selectedSandTools || []), caseData.lastDate, caseData.nextDate,
        caseData.total, caseData.star ? 1 : 0, caseData.duration, caseData.crisis,
        caseData.homework, caseData.progress, JSON.stringify(caseData.keywords || []),
        caseData.supervision, new Date().toISOString(), caseData.id
      ]);
    } else {
      await this.run(`
        INSERT INTO cases (
          id, visitor_id, name, summary, therapy_method, selected_sand_tools, last_date, next_date, total,
          star, duration, crisis, homework, progress, keywords,
          supervision, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        caseData.id, caseData.visitorId, caseData.name, caseData.summary, caseData.therapyMethod,
        JSON.stringify(caseData.selectedSandTools || []), caseData.lastDate,
        caseData.nextDate, caseData.total, caseData.star ? 1 : 0, caseData.duration,
        caseData.crisis, caseData.homework, caseData.progress,
        JSON.stringify(caseData.keywords || []), caseData.supervision,
        caseData.createdAt, caseData.updatedAt
      ]);
    }
  }

  async deleteCase(id: string): Promise<void> {
    await this.run('DELETE FROM cases WHERE id = ?', [id]);
  }

  // 团体会话操作
  async getAllGroupSessions(): Promise<GroupSession[]> {
    const rows = await this.query('SELECT * FROM group_sessions ORDER BY created_at DESC');
    return rows.map(this.mapGroupSessionFromDb);
  }

  async getGroupSession(id: string): Promise<GroupSession | null> {
    const rows = await this.query('SELECT * FROM group_sessions WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapGroupSessionFromDb(rows[0]) : null;
  }

  async saveGroupSession(session: GroupSession): Promise<void> {
    const exists = await this.getGroupSession(session.id);
    
    if (exists) {
      await this.run(`
        UPDATE group_sessions SET 
          title = ?, description = ?, therapist_id = ?, therapist_name = ?,
          max_participants = ?, current_participants = ?, session_type = ?,
          target_age = ?, duration = ?, frequency = ?, total_sessions = ?,
          current_session = ?, start_date = ?, end_date = ?, meeting_time = ?,
          location = ?, status = ?, requirements = ?, materials = ?,
          notes = ?, updated_at = ?
        WHERE id = ?
      `, [
        session.title, session.description, session.therapistId, session.therapistName,
        session.maxParticipants, session.currentParticipants, session.sessionType,
        session.targetAge, session.duration, session.frequency, session.totalSessions,
        session.currentSession, session.startDate, session.endDate, session.meetingTime,
        session.location, session.status, session.requirements,
        JSON.stringify(session.materials || []), session.notes,
        new Date().toISOString(), session.id
      ]);
    } else {
      await this.run(`
        INSERT INTO group_sessions (
          id, title, description, therapist_id, therapist_name,
          max_participants, current_participants, session_type, target_age,
          duration, frequency, total_sessions, current_session, start_date,
          end_date, meeting_time, location, status, requirements,
          materials, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        session.id, session.title, session.description, session.therapistId,
        session.therapistName, session.maxParticipants, session.currentParticipants,
        session.sessionType, session.targetAge, session.duration, session.frequency,
        session.totalSessions, session.currentSession, session.startDate,
        session.endDate, session.meetingTime, session.location, session.status,
        session.requirements, JSON.stringify(session.materials || []),
        session.notes, session.createdAt, session.updatedAt
      ]);
    }
  }

  async deleteGroupSession(id: string): Promise<void> {
    await this.run('DELETE FROM group_sessions WHERE id = ?', [id]);
  }

  // 数据映射函数
  private mapVisitorFromDb(row: any): Visitor {
    return {
      id: row.id,
      name: row.name,
      gender: row.gender,
      age: row.age,
      phone: row.phone,
      email: row.email,
      emergencyContact: row.emergency_contact,
      emergencyPhone: row.emergency_phone,
      occupation: row.occupation,
      education: row.education,
      address: row.address,
      notes: row.notes,
      status: row.status,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private mapCaseFromDb(row: any): SimpleCase {
    return {
      id: row.id,
      visitorId: row.visitor_id,
      name: row.name,
      summary: row.summary,
      therapyMethod: row.therapy_method || '箱庭疗法',
      selectedSandTools: row.selected_sand_tools ? JSON.parse(row.selected_sand_tools) : [],
      lastDate: row.last_date,
      nextDate: row.next_date,
      total: row.total,
      star: Boolean(row.star),
      duration: row.duration,
      crisis: row.crisis,
      homework: row.homework,
      progress: row.progress,
      keywords: row.keywords ? JSON.parse(row.keywords) : [],
      supervision: row.supervision,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private mapGroupSessionFromDb(row: any): GroupSession {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      therapistId: row.therapist_id,
      therapistName: row.therapist_name,
      maxParticipants: row.max_participants,
      currentParticipants: row.current_participants,
      participants: [], // 需要从关联表获取
      sessionType: row.session_type,
      targetAge: row.target_age,
      duration: row.duration,
      frequency: row.frequency,
      totalSessions: row.total_sessions,
      currentSession: row.current_session,
      startDate: row.start_date,
      endDate: row.end_date,
      meetingTime: row.meeting_time,
      location: row.location,
      status: row.status,
      requirements: row.requirements,
      materials: row.materials ? JSON.parse(row.materials) : [],
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  // 获取统计信息
  async getStatistics() {
    const [visitors, cases, sessions] = await Promise.all([
      this.query('SELECT COUNT(*) as count FROM visitors'),
      this.query('SELECT COUNT(*) as count FROM cases'),
      this.query('SELECT COUNT(*) as count FROM group_sessions')
    ]);

    return {
      visitors: visitors[0]?.count || 0,
      cases: cases[0]?.count || 0,
      groupSessions: sessions[0]?.count || 0,
      lastBackup: null // TODO: 实现备份时间记录
    };
  }

  // 沙具管理操作
  async getAllSandTools(): Promise<SandTool[]> {
    const rows = await this.query('SELECT * FROM sand_tools ORDER BY created_at DESC');
    return rows.map(this.mapSandToolFromDb);
  }

  async getSandTool(id: string): Promise<SandTool | null> {
    const rows = await this.query('SELECT * FROM sand_tools WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapSandToolFromDb(rows[0]) : null;
  }

  async saveSandTool(tool: SandTool): Promise<void> {
    const exists = await this.getSandTool(tool.id);
    
    if (exists) {
      await this.run(`
        UPDATE sand_tools SET 
          name = ?, category_id = ?, description = ?, material = ?, size = ?,
          color = ?, quantity = ?, available_quantity = ?, location = ?,
          purchase_date = ?, price = ?, supplier = ?, condition = ?,
          image_url = ?, notes = ?, updated_at = ?
        WHERE id = ?
      `, [
        tool.name, tool.category, tool.description, tool.material, tool.size,
        tool.color, tool.quantity, tool.available, tool.location,
        tool.lastUsed, 0, '', tool.condition, tool.imageUrl, tool.notes,
        new Date().toISOString(), tool.id
      ]);
    } else {
      await this.run(`
        INSERT INTO sand_tools (
          id, name, category_id, description, material, size, color,
          quantity, available_quantity, location, purchase_date, price,
          supplier, condition, image_url, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        tool.id, tool.name, tool.category, tool.description, tool.material,
        tool.size, tool.color, tool.quantity, tool.available, tool.location,
        tool.lastUsed, 0, '', tool.condition, tool.imageUrl, tool.notes,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  async deleteSandTool(id: string): Promise<void> {
    await this.run('DELETE FROM sand_tools WHERE id = ?', [id]);
  }

  async getAllSandToolUsageRecords(): Promise<SandToolUsageRecord[]> {
    const rows = await this.query('SELECT * FROM sand_tool_usage_records ORDER BY session_date DESC');
    return rows.map(this.mapSandToolUsageRecordFromDb);
  }

  async saveSandToolUsageRecord(record: SandToolUsageRecord): Promise<void> {
    await this.run(`
      INSERT INTO sand_tool_usage_records (
        id, sand_tool_id, visitor_id, case_id, session_date,
        quantity_used, usage_duration, usage_notes, therapist_notes, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      record.id, record.toolId, record.clientName, record.sessionId,
      record.usageDate, 1, record.duration, record.notes, '',
      new Date().toISOString()
    ]);
  }

  async getAllSandToolMaintenanceRecords(): Promise<SandToolMaintenanceRecord[]> {
    const rows = await this.query('SELECT * FROM sand_tool_maintenance_records ORDER BY maintenance_date DESC');
    return rows.map(this.mapSandToolMaintenanceRecordFromDb);
  }

  async saveSandToolMaintenanceRecord(record: SandToolMaintenanceRecord): Promise<void> {
    await this.run(`
      INSERT INTO sand_tool_maintenance_records (
        id, sand_tool_id, maintenance_type, maintenance_date,
        description, cost, performed_by, next_maintenance_date, notes, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      record.id, record.toolId, record.maintenanceType, record.date,
      record.description, record.cost, record.performedBy, record.nextScheduled,
      '', new Date().toISOString()
    ]);
  }

  // 日程管理操作
  async getAllAppointments(): Promise<Appointment[]> {
    const rows = await this.query('SELECT * FROM appointments ORDER BY appointment_date DESC, start_time DESC');
    return rows.map(this.mapAppointmentFromDb);
  }

  async getAppointment(id: string): Promise<Appointment | null> {
    const rows = await this.query('SELECT * FROM appointments WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapAppointmentFromDb(rows[0]) : null;
  }

  async saveAppointment(appointment: Appointment): Promise<void> {
    const exists = await this.getAppointment(appointment.id);
    
    if (exists) {
      await this.run(`
        UPDATE appointments SET 
          visitor_id = ?, case_id = ?, therapist_id = ?, therapist_name = ?,
          room_id = ?, title = ?, description = ?, appointment_date = ?,
          start_time = ?, end_time = ?, duration = ?, appointment_type = ?,
          status = ?, urgency_level = ?, recurring_type = ?, recurring_end_date = ?,
          reminder_time = ?, notes = ?, cancellation_reason = ?, updated_at = ?
        WHERE id = ?
      `, [
        appointment.visitorId, appointment.visitorId, appointment.therapistId, appointment.therapistName,
        appointment.room, appointment.subject, appointment.description, appointment.date,
        appointment.startTime, appointment.endTime, appointment.duration, appointment.type,
        appointment.status, appointment.urgency, null, null,
        appointment.reminderTime, appointment.notes, null, new Date().toISOString(), appointment.id
      ]);
    } else {
      await this.run(`
        INSERT INTO appointments (
          id, visitor_id, case_id, therapist_id, therapist_name, room_id,
          title, description, appointment_date, start_time, end_time, duration,
          appointment_type, status, urgency_level, recurring_type, recurring_end_date,
          reminder_time, notes, cancellation_reason, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        appointment.id, appointment.visitorId, appointment.visitorId, appointment.therapistId,
        appointment.therapistName, appointment.room, appointment.subject, appointment.description,
        appointment.date, appointment.startTime, appointment.endTime, appointment.duration,
        appointment.type, appointment.status, appointment.urgency, null, null,
        appointment.reminderTime, appointment.notes, null,
        appointment.createdAt, appointment.updatedAt
      ]);
    }
  }

  async deleteAppointment(id: string): Promise<void> {
    await this.run('DELETE FROM appointments WHERE id = ?', [id]);
  }

  async getAllRooms(): Promise<Room[]> {
    const rows = await this.query('SELECT * FROM rooms ORDER BY name');
    return rows.map(this.mapRoomFromDb);
  }

  async saveRoom(room: Room): Promise<void> {
    const exists = await this.query('SELECT id FROM rooms WHERE id = ?', [room.id]);
    
    if (exists.length > 0) {
      await this.run(`
        UPDATE rooms SET name = ?, capacity = ?, equipment = ?, location = ?,
        description = ?, is_available = ?, updated_at = ? WHERE id = ?
      `, [
        room.name, room.capacity, JSON.stringify(room.equipment || []), room.notes,
        room.notes, room.available ? 1 : 0, new Date().toISOString(), room.id
      ]);
    } else {
      await this.run(`
        INSERT INTO rooms (id, name, capacity, equipment, location, description, is_available, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        room.id, room.name, room.capacity, JSON.stringify(room.equipment || []),
        room.notes, room.notes, room.available ? 1 : 0,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  // 设置管理操作
  async getSettings(): Promise<AppSettings | null> {
    const rows = await this.query('SELECT * FROM settings');
    if (rows.length === 0) return null;
    
    const settings: any = {};
    rows.forEach(row => {
      if (!settings[row.category]) {
        settings[row.category] = {};
      }
      let value = row.value;
      if (row.data_type === 'object' || row.data_type === 'array') {
        try {
          value = JSON.parse(value);
        } catch (e) {
          value = null;
        }
      } else if (row.data_type === 'boolean') {
        value = value === 'true';
      } else if (row.data_type === 'number') {
        value = parseFloat(value);
      }
      settings[row.category][row.key] = value;
    });
    
    return settings as AppSettings;
  }

  async saveSettings(settings: AppSettings): Promise<void> {
    // 清空现有设置
    await this.run('DELETE FROM settings WHERE is_system = 0');
    
    // 保存新设置
    const flatSettings = this.flattenSettings(settings);
    for (const [key, value] of Object.entries(flatSettings)) {
      const [category, settingKey] = key.split('.');
      const dataType = typeof value;
      const stringValue = dataType === 'object' ? JSON.stringify(value) : String(value);
      
      await this.run(`
        INSERT INTO settings (id, category, key, value, data_type, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        `${category}_${settingKey}`, category, settingKey, stringValue, dataType,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  // 系统数据管理
  async saveSystemData(key: string, value: any): Promise<void> {
    const exists = await this.query('SELECT id FROM system_data WHERE key = ?', [key]);
    const stringValue = JSON.stringify(value);
    
    if (exists.length > 0) {
      await this.run(`
        UPDATE system_data SET value = ?, updated_at = ? WHERE key = ?
      `, [stringValue, new Date().toISOString(), key]);
    } else {
      await this.run(`
        INSERT INTO system_data (id, key, value, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `, [
        `system_${key}`, key, stringValue,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  async getSystemData(key: string): Promise<any | null> {
    const rows = await this.query('SELECT value FROM system_data WHERE key = ?', [key]);
    if (rows.length === 0) return null;
    
    try {
      return JSON.parse(rows[0].value);
    } catch (error) {
      return rows[0].value;
    }
  }

  // 用户偏好设置
  async saveUserPreference(category: string, key: string, value: any): Promise<void> {
    const preferenceId = `${category}_${key}`;
    const exists = await this.query('SELECT id FROM user_preferences WHERE id = ?', [preferenceId]);
    const stringValue = JSON.stringify(value);
    
    if (exists.length > 0) {
      await this.run(`
        UPDATE user_preferences SET value = ?, updated_at = ? WHERE id = ?
      `, [stringValue, new Date().toISOString(), preferenceId]);
    } else {
      await this.run(`
        INSERT INTO user_preferences (id, category, key, value, data_type, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        preferenceId, category, key, stringValue, typeof value,
        new Date().toISOString(), new Date().toISOString()
      ]);
    }
  }

  async getUserPreference(category: string, key?: string): Promise<any> {
    if (key) {
      const preferenceId = `${category}_${key}`;
      const rows = await this.query('SELECT value FROM user_preferences WHERE id = ?', [preferenceId]);
      if (rows.length === 0) return null;
      
      try {
        return JSON.parse(rows[0].value);
      } catch (error) {
        return rows[0].value;
      }
    } else {
      // 获取整个类别的偏好设置
      const rows = await this.query('SELECT key, value FROM user_preferences WHERE category = ?', [category]);
      
      const result: any = {};
      rows.forEach(row => {
        try {
          result[row.key] = JSON.parse(row.value);
        } catch (error) {
          result[row.key] = row.value;
        }
      });
      
      return result;
    }
  }

  // 新增数据映射函数
  private mapSandToolFromDb(row: any): SandTool {
    return {
      id: row.id,
      name: row.name,
      category: row.category_id,
      description: row.description,
      material: row.material,
      size: row.size,
      color: row.color,
      quantity: row.quantity,
      available: row.available_quantity,
      condition: row.condition,
      location: row.location,
      imageUrl: row.image_url,
      notes: row.notes,
      lastUsed: row.purchase_date
    };
  }

  private mapSandToolUsageRecordFromDb(row: any): SandToolUsageRecord {
    return {
      id: row.id,
      toolId: row.sand_tool_id,
      toolName: '',
      sessionId: row.case_id,
      sessionType: '个案',
      clientName: row.visitor_id,
      therapistName: '',
      usageDate: row.session_date,
      duration: row.usage_duration,
      notes: row.usage_notes,
      returnCondition: '良好'
    };
  }

  private mapSandToolMaintenanceRecordFromDb(row: any): SandToolMaintenanceRecord {
    return {
      id: row.id,
      toolId: row.sand_tool_id,
      maintenanceType: row.maintenance_type,
      date: row.maintenance_date,
      description: row.description,
      cost: row.cost,
      performedBy: row.performed_by,
      nextScheduled: row.next_maintenance_date
    };
  }

  private mapAppointmentFromDb(row: any): Appointment {
    return {
      id: row.id,
      visitorId: row.visitor_id,
      visitorName: '',
      visitorPhone: '',
      date: row.appointment_date,
      startTime: row.start_time,
      endTime: row.end_time,
      duration: row.duration,
      type: row.appointment_type,
      status: row.status,
      urgency: row.urgency_level,
      room: row.room_id,
      therapistId: row.therapist_id,
      therapistName: row.therapist_name,
      subject: row.title,
      description: row.description,
      notes: row.notes,
      reminderEnabled: true,
      reminderTime: row.reminder_time || 30,
      isFirstSession: false,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdBy: ''
    };
  }

  private mapRoomFromDb(row: any): Room {
    return {
      id: row.id,
      name: row.name,
      capacity: row.capacity,
      type: '个体咨询室',
      equipment: row.equipment ? JSON.parse(row.equipment) : [],
      available: Boolean(row.is_available),
      notes: row.description
    };
  }

  // 辅助方法：扁平化设置对象
  private flattenSettings(obj: any, prefix = ''): Record<string, any> {
    const flattened: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        Object.assign(flattened, this.flattenSettings(value, newKey));
      } else {
        flattened[newKey] = value;
      }
    }
    
    return flattened;
  }
}

// 单例实例
export const electronDataManager = new ElectronDataManager();
