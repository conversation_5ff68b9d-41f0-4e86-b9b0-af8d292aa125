/* 个案编辑模态框样式 */
@import '../../styles/variables.css';

/* 编辑区块 */
.edit-section {
  margin-bottom: var(--spacing-4xl);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  background: var(--white);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  flex: 1;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.reset-btn {
  color: var(--text-secondary) !important;
  transition: color var(--transition-normal);
}

.reset-btn:hover {
  color: var(--primary-blue) !important;
}

/* 只读信息网格 */
.readonly-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
}

.readonly-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.readonly-item label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.readonly-value {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  font-size: var(--text-base);
  color: var(--text-primary);
  min-height: 40px;
}

.star-icon {
  color: var(--accent-amber);
}

/* 表单网格布局 */
.form-grid {
  padding: var(--spacing-xl);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  align-items: start;
}

.form-row:last-child {
  margin-bottom: 0;
}

/* 单列表单行 */
.form-row.single-column {
  grid-template-columns: 1fr;
}

/* 复选框包装器 */
.checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg) 0;
}

.star-checkbox {
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.checkbox-icon {
  color: var(--accent-amber);
  margin-left: var(--spacing-sm);
  margin-right: var(--spacing-sm);
}

/* 评估网格 */
.assessment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  padding: var(--spacing-xl);
}

.assessment-item {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

/* 关键词管理 */
.keywords-management {
  padding: var(--spacing-xl);
}

.keyword-input-section {
  margin-bottom: var(--spacing-xl);
}

.keyword-input-wrapper {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-md);
  align-items: end;
}

.add-keyword-btn {
  height: 40px;
  padding: 0 var(--spacing-lg);
  white-space: nowrap;
}

/* 关键词建议 */
.keyword-suggestions {
  margin-top: var(--spacing-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--spacing-sm);
}

.suggestion-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--white);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
}

.suggestion-btn:hover:not(.disabled) {
  border-color: var(--primary-blue);
  background: rgba(59, 130, 246, 0.05);
  color: var(--primary-blue);
}

.suggestion-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-tertiary);
}

/* 已选择的关键词 */
.selected-keywords {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.selected-header {
  margin-bottom: var(--spacing-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.selected-keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.selected-keyword {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  color: var(--primary-blue-darker);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.remove-keyword-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: 10px;
  line-height: 1;
}

.remove-keyword-btn:hover {
  background: var(--error);
  color: var(--white);
}

/* 督导记录区域 */
.supervision-section {
  padding: var(--spacing-xl);
}

/* 新增咨询记录 */
.new-record-section {
  padding: var(--spacing-xl);
}

.record-toggle {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.record-checkbox {
  color: var(--text-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-base);
}

/* 记录表单 */
.record-form {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

.record-basic-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.record-content {
  margin-bottom: var(--spacing-xl);
}

.record-optional {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .readonly-info-grid {
    grid-template-columns: 1fr;
  }
  
  .assessment-grid {
    grid-template-columns: 1fr;
  }
  
  .keyword-input-wrapper {
    grid-template-columns: 1fr;
  }
  
  .suggestions-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  
  .record-basic-info,
  .record-optional {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .edit-section {
    margin-bottom: var(--spacing-3xl);
  }
  
  .section-header,
  .readonly-info-grid,
  .form-grid,
  .assessment-grid,
  .keywords-management,
  .supervision-section,
  .new-record-section,
  .record-form {
    padding: var(--spacing-lg);
  }
  
  .suggestions-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* 特殊表单元素样式 */
.therapy-select,
.duration-select,
.crisis-select,
.homework-select,
.progress-select,
.mood-select,
.topic-select,
.therapist-mood-select {
  font-size: var(--text-sm);
}

.summary-textarea,
.supervision-textarea,
.record-note-textarea {
  resize: vertical;
  min-height: 80px;
}

.date-input,
.keyword-input,
.key-quote-input {
  font-size: var(--text-sm);
}
