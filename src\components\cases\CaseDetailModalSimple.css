/* 简化版个案详情模态框样式 */
.case-detail-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 加载状态 */
.detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态卡片行 */
.status-cards-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 8px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.status-card:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  color: white;
}

.status-icon.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.status-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.status-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.status-icon.info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.status-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.status-value {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
}

/* 详细内容区域 */
.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 详细信息段落 */
.detail-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.section-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.section-title svg {
  color: #6b7280;
}

/* 信息网格 */
.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #111827;
  font-weight: 400;
}

/* 治疗信息样式 */
.therapy-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.therapy-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.problem-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.problem-content {
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

/* 咨询安排网格 */
.schedule-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.schedule-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.days-info {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}

/* 评估网格 */
.assessment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.assessment-item {
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.assessment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.assessment-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.assessment-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
}

/* 督导记录 */
.supervision-content {
  padding: 16px;
  background: #fefbf3;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  color: #92400e;
}

/* 底部操作区域 */
.detail-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .case-detail-wrapper {
    max-height: 60vh;
  }
  
  .status-cards-row {
    grid-template-columns: 1fr;
  }
  
  .info-row,
  .therapy-row,
  .schedule-grid {
    grid-template-columns: 1fr;
  }
  
  .assessment-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-footer {
    flex-direction: column-reverse;
  }
}

/* Badge组件内图标对齐 */
.detail-section .badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.detail-section .badge svg {
  flex-shrink: 0;
}
