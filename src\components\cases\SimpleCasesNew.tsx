import React, { useState, useEffect } from 'react';
import { User, Search, Plus, Eye, Edit, Trash2 } from 'lucide-react';
import { caseService } from '../../services';
import type { SimpleCase } from '../../types/case';
import SimpleCreateCaseModal from './SimpleCreateCaseModal';
import './SimpleCases.css';

const SimpleCases: React.FC = () => {
  const [cases, setCases] = useState<SimpleCase[]>([]);
  const [filteredCases, setFilteredCases] = useState<SimpleCase[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [loading, setLoading] = useState(true);
  
  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedCase, setSelectedCase] = useState<SimpleCase | null>(null);

  useEffect(() => {
    loadCases();
  }, []);

  useEffect(() => {
    filterCases();
  }, [cases, searchTerm, statusFilter]);

  const loadCases = async () => {
    try {
      setLoading(true);
      const casesData = await caseService.getAllCases();
      setCases(casesData);
    } catch (error) {
      console.error('加载个案失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterCases = () => {
    let filtered = cases;

    if (searchTerm) {
      filtered = filtered.filter(c => 
        c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.summary.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      // 根据状态筛选 - 暂时使用star字段作为状态标识
      if (statusFilter === 'active') {
        filtered = filtered.filter(c => c.star);
      } else if (statusFilter === 'completed') {
        filtered = filtered.filter(c => !c.star);
      }
    }

    setFilteredCases(filtered);
  };

  const handleCreateCase = () => {
    setShowCreateModal(true);
  };

  const handleViewCase = (caseItem: SimpleCase) => {
    setSelectedCase(caseItem);
    setShowDetailModal(true);
  };

  const handleEditCase = (caseItem: SimpleCase) => {
    setSelectedCase(caseItem);
    setShowEditModal(true);
  };

  const handleDeleteCase = async (caseId: string) => {
    if (confirm('确定要删除这个个案吗？')) {
      try {
        await caseService.deleteCase(caseId);
        await loadCases();
      } catch (error) {
        console.error('删除个案失败:', error);
        alert('删除失败，请重试');
      }
    }
  };

  const handleCaseCreated = () => {
    setShowCreateModal(false);
    loadCases();
  };

  const handleCaseUpdated = () => {
    setShowEditModal(false);
    loadCases();
  };

  const getStatusBadge = (caseItem: SimpleCase) => {
    // 简单的状态判断
    const status = caseItem.star ? 'active' : 'completed';
    const statusMap = {
      'active': { text: '进行中', class: 'status-attention' },
      'completed': { text: '已结束', class: 'status-safe' },
    };
    
    const statusInfo = statusMap[status];
    
    return (
      <span className={`status-badge ${statusInfo.class}`}>
        {statusInfo.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="simple-case-list">
        <div style={{ textAlign: 'center', padding: '40px', color: '#6b7280' }}>
          加载中...
        </div>
      </div>
    );
  }

  return (
    <div className="simple-case-list">
      <div className="simple-case-header">
        <h1>个案管理</h1>
        <button 
          className="simple-btn primary"
          onClick={handleCreateCase}
        >
          <Plus size={16} />
          新建个案
        </button>
      </div>

      <div className="simple-search-bar">
        <div style={{ position: 'relative', flex: 1 }}>
          <Search size={16} style={{ 
            position: 'absolute', 
            left: '12px', 
            top: '50%', 
            transform: 'translateY(-50%)', 
            color: '#9ca3af' 
          }} />
          <input
            type="text"
            placeholder="搜索来访者姓名或问题描述..."
            className="simple-search-input"
            style={{ paddingLeft: '36px' }}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <select
          className="simple-filter-select"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="">全部状态</option>
          <option value="active">进行中</option>
          <option value="completed">已结束</option>
        </select>
      </div>

      {filteredCases.length === 0 ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '60px 20px', 
          color: '#6b7280',
          background: '#f9fafb',
          borderRadius: '8px',
          border: '1px dashed #d1d5db'
        }}>
          <User size={48} style={{ margin: '0 auto 16px', color: '#d1d5db' }} />
          <p>暂无个案数据</p>
          <button 
            className="simple-btn primary"
            onClick={handleCreateCase}
            style={{ marginTop: '12px' }}
          >
            创建第一个个案
          </button>
        </div>
      ) : (
        <div className="simple-case-grid">
          {filteredCases.map((caseItem) => (
            <div key={caseItem.id} className="simple-case-card">
              <div className="case-name">
                <User size={16} />
                {caseItem.name}
              </div>
              
              <div className="case-summary">
                {caseItem.summary}
              </div>
              
              <div className="case-meta">
                <div className="case-status">
                  {getStatusBadge(caseItem)}
                </div>
                <div>
                  {new Date(caseItem.createdAt).toLocaleDateString()}
                </div>
              </div>
              
              <div className="case-actions">
                <button 
                  className="simple-btn"
                  onClick={() => handleViewCase(caseItem)}
                >
                  <Eye size={14} />
                  查看
                </button>
                <button 
                  className="simple-btn"
                  onClick={() => handleEditCase(caseItem)}
                >
                  <Edit size={14} />
                  编辑
                </button>
                <button 
                  className="simple-btn"
                  onClick={() => handleDeleteCase(caseItem.id)}
                  style={{ color: '#dc2626' }}
                >
                  <Trash2 size={14} />
                  删除
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 模态框 */}
      {showCreateModal && (
        <SimpleCreateCaseModal
          onClose={() => setShowCreateModal(false)}
          onCaseCreated={handleCaseCreated}
        />
      )}

      {showEditModal && selectedCase && (
        <div className="simple-modal">
          <div className="simple-modal-content">
            <div className="simple-modal-header">
              <h2 className="simple-modal-title">编辑个案</h2>
              <button 
                className="simple-btn"
                onClick={() => setShowEditModal(false)}
              >
                关闭
              </button>
            </div>
            <p>编辑个案功能正在开发中...</p>
          </div>
        </div>
      )}

      {showDetailModal && selectedCase && (
        <div className="simple-modal">
          <div className="simple-modal-content">
            <div className="simple-modal-header">
              <h2 className="simple-modal-title">个案详情</h2>
              <button 
                className="simple-btn"
                onClick={() => setShowDetailModal(false)}
              >
                关闭
              </button>
            </div>
            <div>
              <p><strong>来访者：</strong>{selectedCase.name}</p>
              <p><strong>问题描述：</strong>{selectedCase.summary}</p>
              <p><strong>治疗方法：</strong>{selectedCase.therapyMethod}</p>
              <p><strong>咨询时长：</strong>{selectedCase.duration}分钟</p>
              <p><strong>创建时间：</strong>{new Date(selectedCase.createdAt).toLocaleString()}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleCases;
