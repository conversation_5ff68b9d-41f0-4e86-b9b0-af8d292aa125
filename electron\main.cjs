const { app, BrowserWindow, Menu, ipcMain, dialog } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');

// 数据库路径
const userDataPath = app.getPath('userData');
const dbPath = path.join(userDataPath, 'xlsp.db');

let mainWindow;
let db;

// 初始化数据库
function initDatabase() {
  try {
    console.log('开始初始化数据库...');
    console.log('用户数据路径:', userDataPath);
    console.log('数据库路径:', dbPath);
    
    // 确保用户数据目录存在
    if (!fs.existsSync(userDataPath)) {
      console.log('创建用户数据目录:', userDataPath);
      fs.mkdirSync(userDataPath, { recursive: true });
    }

    // 连接数据库
    console.log('连接数据库...');
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('数据库连接失败:', err.message);
        db = null;
      } else {
        console.log('数据库连接成功');
        // 启用外键约束
        db.run('PRAGMA foreign_keys = ON;');
        
        // 创建表结构
        console.log('创建数据库表...');
        createTables();
        
        console.log('数据库初始化成功:', dbPath);
      }
    });
    
  } catch (error) {
    console.error('数据库初始化失败:', error.message);
    console.warn('应用将在非数据库模式下运行');
    db = null; // 确保数据库实例为null
  }
}

// 创建数据库表
function createTables() {
  const tables = [
    // 来访者表
    `CREATE TABLE IF NOT EXISTS visitors (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      gender TEXT CHECK(gender IN ('男', '女', '其他')),
      age INTEGER,
      phone TEXT UNIQUE,
      email TEXT,
      emergency_contact TEXT,
      emergency_phone TEXT,
      occupation TEXT,
      education TEXT,
      address TEXT,
      notes TEXT,
      status TEXT DEFAULT '活跃' CHECK(status IN ('活跃', '暂停', '完成')),
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 个案表
    `CREATE TABLE IF NOT EXISTS cases (
      id TEXT PRIMARY KEY,
      visitor_id TEXT,
      name TEXT NOT NULL,
      summary TEXT,
      therapy_method TEXT DEFAULT '箱庭疗法',
      selected_sand_tools TEXT,
      last_date TEXT,
      next_date TEXT,
      total INTEGER DEFAULT 0,
      star BOOLEAN DEFAULT FALSE,
      duration INTEGER DEFAULT 50,
      crisis TEXT CHECK(crisis IN ('⚠️', '⚡', '✅')),
      homework TEXT CHECK(homework IN ('📋', '✅', '❌')),
      progress TEXT CHECK(progress IN ('⬆️', '➡️', '⬇️')),
      keywords TEXT,
      supervision TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (visitor_id) REFERENCES visitors (id)
    )`,
    
    // 团体会话表
    `CREATE TABLE IF NOT EXISTS group_sessions (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      therapist_id TEXT,
      therapist_name TEXT,
      max_participants INTEGER DEFAULT 8,
      current_participants INTEGER DEFAULT 0,
      participants TEXT,
      session_type TEXT,
      target_age TEXT,
      duration INTEGER DEFAULT 90,
      frequency TEXT,
      total_sessions INTEGER,
      current_session INTEGER DEFAULT 0,
      start_date TEXT,
      end_date TEXT,
      meeting_time TEXT,
      location TEXT,
      status TEXT DEFAULT '计划中' CHECK(status IN ('计划中', '进行中', '已完成', '已取消')),
      requirements TEXT,
      materials TEXT,
      selected_sand_tools TEXT,
      notes TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 沙具表
    `CREATE TABLE IF NOT EXISTS sand_tools (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      category TEXT,
      subcategory TEXT,
      description TEXT,
      material TEXT,
      size TEXT,
      color TEXT,
      quantity INTEGER DEFAULT 1,
      available INTEGER DEFAULT 1,
      condition TEXT DEFAULT '良好',
      location TEXT,
      image_url TEXT,
      notes TEXT,
      tags TEXT,
      usage_count INTEGER DEFAULT 0,
      last_used TEXT,
      is_fragile BOOLEAN DEFAULT FALSE,
      needs_care BOOLEAN DEFAULT FALSE,
      replacement_needed BOOLEAN DEFAULT FALSE
    )`,
    
    // 设置表
    `CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY,
      data TEXT,
      updated_at TEXT
    )`
  ];

  // 使用serialize确保表按顺序创建
  db.serialize(() => {
    tables.forEach(sql => {
      db.run(sql, (err) => {
        if (err) {
          console.error('创建表失败:', err.message);
        }
      });
    });

    // 数据库升级和迁移
    db.all("PRAGMA table_info(cases)", (err, columns) => {
      if (err) {
        console.warn('检查表结构失败:', err.message);
        return;
      }
      
      const hasTherapyMethod = columns.some(col => col.name === 'therapy_method');
      if (!hasTherapyMethod) {
        db.run('ALTER TABLE cases ADD COLUMN therapy_method TEXT DEFAULT "箱庭疗法"', (err) => {
          if (err) {
            console.warn('添加therapy_method字段失败:', err.message);
          } else {
            console.log('已添加therapy_method字段到cases表');
          }
        });
      }

      const hasSelectedSandTools = columns.some(col => col.name === 'selected_sand_tools');
      if (!hasSelectedSandTools) {
        db.run('ALTER TABLE cases ADD COLUMN selected_sand_tools TEXT', (err) => {
          if (err) {
            console.warn('添加selected_sand_tools字段失败:', err.message);
          } else {
            console.log('已添加selected_sand_tools字段到cases表');
          }
        });
      }
    });
  });
}

// 创建主窗口
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/logo.png'),
    titleBarStyle: 'default',
    show: false
  });

  // 加载应用
  if (isDev) {
    // 检测开发服务器端口
    const devPort = process.env.DEV_PORT || '5173';
    mainWindow.loadURL(`http://localhost:${devPort}`);
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 设置菜单
  setApplicationMenu();
}

// 设置应用菜单
function setApplicationMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '导入数据',
          accelerator: 'CmdOrCtrl+I',
          click: () => importData()
        },
        {
          label: '导出数据',
          accelerator: 'CmdOrCtrl+E',
          click: () => exportData()
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => app.quit()
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '全屏', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于沙盘管理系统',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: '沙盘管理系统',
              detail: '专业的心理健康沙盘疗法管理系统\\n版本: 1.0.0\\n\\n© 2024 版权所有'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 导入数据
async function importData() {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: '选择数据文件',
      filters: [
        { name: 'JSON 文件', extensions: ['json'] }
      ]
    });

    if (!result.canceled && result.filePaths.length > 0) {
      const filePath = result.filePaths[0];
      const data = fs.readFileSync(filePath, 'utf8');
      
      // 发送数据到渲染进程进行处理
      mainWindow.webContents.send('import-data', data);
      
      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '导入成功',
        message: '数据文件已加载，请在应用中确认导入'
      });
    }
  } catch (error) {
    dialog.showErrorBox('导入失败', error.message);
  }
}

// 导出数据
async function exportData() {
  try {
    if (!db) {
      dialog.showMessageBox(mainWindow, {
        type: 'warning',
        title: '导出失败',
        message: '数据库未初始化，无法导出数据'
      });
      return;
    }
    
    const result = await dialog.showSaveDialog(mainWindow, {
      title: '保存数据文件',
      defaultPath: `xlsp-backup-${new Date().toISOString().split('T')[0]}.json`,
      filters: [
        { name: 'JSON 文件', extensions: ['json'] }
      ]
    });

    if (!result.canceled) {
      // 使用Promise.all并行获取所有表的数据
      const [visitors, cases, groupSessions, sandTools] = await Promise.all([
        new Promise((resolve) => {
          db.all('SELECT * FROM visitors', (err, rows) => {
            resolve(err ? [] : rows);
          });
        }),
        new Promise((resolve) => {
          db.all('SELECT * FROM cases', (err, rows) => {
            resolve(err ? [] : rows);
          });
        }),
        new Promise((resolve) => {
          db.all('SELECT * FROM group_sessions', (err, rows) => {
            resolve(err ? [] : rows);
          });
        }),
        new Promise((resolve) => {
          db.all('SELECT * FROM sand_tools', (err, rows) => {
            resolve(err ? [] : rows);
          });
        })
      ]);

      const data = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        visitors,
        cases,
        groupSessions,
        sandTools
      };

      fs.writeFileSync(result.filePath, JSON.stringify(data, null, 2));
      
      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '导出成功',
        message: '数据导出完成'
      });
    }
  } catch (error) {
    dialog.showErrorBox('导出失败', error.message);
  }
}

// IPC 处理程序
ipcMain.handle('db-query', async (event, sql, params = []) => {
  return new Promise((resolve, reject) => {
    try {
      if (!db) {
        console.warn('数据库未初始化，返回空结果');
        resolve([]);
        return;
      }
      
      db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('Database query error:', err);
          resolve([]); // 返回空数组而不是抛出错误
        } else {
          resolve(rows || []);
        }
      });
    } catch (error) {
      console.error('Database query error:', error);
      resolve([]); // 返回空数组而不是抛出错误
    }
  });
});

ipcMain.handle('db-run', async (event, sql, params = []) => {
  return new Promise((resolve, reject) => {
    try {
      if (!db) {
        console.warn('数据库未初始化，返回默认结果');
        resolve({ changes: 0, lastInsertRowid: null });
        return;
      }
      
      db.run(sql, params, function(err) {
        if (err) {
          console.error('Database run error:', err);
          resolve({ changes: 0, lastInsertRowid: null }); // 返回默认结果
        } else {
          resolve({ changes: this.changes, lastInsertRowid: this.lastID });
        }
      });
    } catch (error) {
      console.error('Database run error:', error);
      resolve({ changes: 0, lastInsertRowid: null }); // 返回默认结果
    }
  });
});

ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-path', (event, name) => {
  return app.getPath(name);
});

// 处理来自渲染进程的导出数据
ipcMain.handle('save-export-data', (event, filePath, data) => {
  try {
    fs.writeFileSync(filePath, data);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '导出成功',
      message: '数据导出完成'
    });
    return { success: true };
  } catch (error) {
    dialog.showErrorBox('导出失败', error.message);
    return { success: false, error: error.message };
  }
});

// 应用事件
app.whenReady().then(() => {
  initDatabase();
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    if (db) {
      db.close((err) => {
        if (err) {
          console.error('关闭数据库时出错:', err.message);
        } else {
          console.log('数据库已关闭');
        }
      });
    }
    app.quit();
  }
});

app.on('before-quit', () => {
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库时出错:', err.message);
      } else {
        console.log('数据库已关闭');
      }
    });
  }
});

// 单实例应用
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
