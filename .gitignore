# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Electron builds and distributables
dist-electron/
*.exe
*.dmg
*.AppImage
*.blockmap
*.deb
*.rpm
*.tar.gz
*.zip
*.7z
builder-debug.yml
builder-effective-config.yaml
win-unpacked/
mac/
linux-unpacked/

# SQLite database files
*.db
*.sqlite
*.sqlite3

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
.DS_Store
.Spotlight-V100
.Trashes

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*~

# Development docs (already organized in docs/)
*.md.backup
HELP_CENTER*.md
MODAL_REDESIGN*.md
STATISTICS*.md
SANDTOOLS*.md
SETTINGS*.md
VISUAL_OPTIMIZATION*.md
QWEN*.md

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
