import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON>h, <PERSON>own, Sun, Cloud, CloudRain } from 'lucide-react';

interface MoodEntry {
  date: string;
  mood: 'great' | 'good' | 'okay' | 'down' | 'stressed';
  note?: string;
  timestamp: string;
}

export const MoodTrackerCard: React.FC = () => {
  const [currentMood, setCurrentMood] = useState<MoodEntry['mood'] | null>(null);
  const [moodNote, setMoodNote] = useState('');
  const [todayMood, setTodayMood] = useState<MoodEntry | null>(null);
  const [showNoteInput, setShowNoteInput] = useState(false);

  const moodOptions = [
    { value: 'great', icon: Sun, label: '很棒', color: '#10b981', bgColor: '#ecfdf5' },
    { value: 'good', icon: Smile, label: '不错', color: '#3b82f6', bgColor: '#eff6ff' },
    { value: 'okay', icon: Meh, label: '一般', color: '#f59e0b', bgColor: '#fffbeb' },
    { value: 'down', icon: Frown, label: '低落', color: '#ef4444', bgColor: '#fef2f2' },
    { value: 'stressed', icon: CloudRain, label: '焦虑', color: '#8b5cf6', bgColor: '#f3f4f6' }
  ] as const;

  // 加载今日心情
  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    const savedMood = localStorage.getItem(`mood_${today}`);
    if (savedMood) {
      setTodayMood(JSON.parse(savedMood));
    }
  }, []);

  // 保存心情
  const handleSaveMood = () => {
    if (!currentMood) return;

    const today = new Date().toISOString().split('T')[0];
    const moodEntry: MoodEntry = {
      date: today,
      mood: currentMood,
      note: moodNote.trim() || undefined,
      timestamp: new Date().toISOString()
    };

    localStorage.setItem(`mood_${today}`, JSON.stringify(moodEntry));
    setTodayMood(moodEntry);
    setCurrentMood(null);
    setMoodNote('');
    setShowNoteInput(false);
  };

  const getCurrentMoodOption = () => {
    if (todayMood) {
      return moodOptions.find(option => option.value === todayMood.mood);
    }
    return null;
  };

  const currentMoodOption = getCurrentMoodOption();

  return (
    <div className="workspace-card mood-tracker-card">
      <div className="workspace-card-header">
        <div className="workspace-card-icon mood-icon">
          <Heart size={20} />
        </div>
        <h3 className="workspace-card-title">今日心情</h3>
      </div>
      
      <div className="workspace-card-content">
        {todayMood ? (
          <div className="mood-display">
            <div 
              className="current-mood"
              style={{ 
                backgroundColor: currentMoodOption?.bgColor,
                color: currentMoodOption?.color 
              }}
            >
              {currentMoodOption && (
                <>
                  <currentMoodOption.icon size={24} />
                  <span className="mood-label">{currentMoodOption.label}</span>
                </>
              )}
            </div>
            {todayMood.note && (
              <div className="mood-note">
                <p>"{todayMood.note}"</p>
              </div>
            )}
            <button 
              className="update-mood-btn"
              onClick={() => setTodayMood(null)}
            >
              更新心情
            </button>
          </div>
        ) : (
          <div className="mood-selector">
            <p className="mood-prompt">今天感觉怎么样？</p>
            <div className="mood-options">
              {moodOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <button
                    key={option.value}
                    className={`mood-option ${currentMood === option.value ? 'selected' : ''}`}
                    onClick={() => {
                      setCurrentMood(option.value);
                      setShowNoteInput(true);
                    }}
                    style={{
                      backgroundColor: currentMood === option.value ? option.bgColor : '#f9fafb',
                      color: currentMood === option.value ? option.color : '#6b7280'
                    }}
                  >
                    <IconComponent size={18} />
                    <span>{option.label}</span>
                  </button>
                );
              })}
            </div>
            
            {showNoteInput && currentMood && (
              <div className="mood-note-input">
                <textarea
                  placeholder="想说点什么吗？（可选）"
                  value={moodNote}
                  onChange={(e) => setMoodNote(e.target.value)}
                  maxLength={100}
                />
                <div className="mood-actions">
                  <button className="save-mood-btn" onClick={handleSaveMood}>
                    记录心情
                  </button>
                  <button 
                    className="cancel-mood-btn" 
                    onClick={() => {
                      setCurrentMood(null);
                      setMoodNote('');
                      setShowNoteInput(false);
                    }}
                  >
                    取消
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};