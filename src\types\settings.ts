// 系统设置类型定义

export interface UserProfile {
  name: string;
  title: string;
}

export interface WorkPreferences {
  defaultSessionDuration: number; // 默认咨询时长（分钟）
  workingHours: {
    start: string; // 工作开始时间 HH:mm
    end: string;   // 工作结束时间 HH:mm
  };
  autoSave: boolean; // 自动保存
}

export interface UserSettings {
  profile: UserProfile;
  preferences: WorkPreferences;
}

export interface AppointmentNotifications {
  enabled: boolean;
  advanceTime: number; // 提前提醒时间（分钟）
}

export interface ThemeSettings {
  mode: 'light' | 'dark' | 'auto'; // 主题模式
  primaryColor: string; // 主题色
  fontSize: 'small' | 'medium' | 'large'; // 字体大小
}

export interface LayoutSettings {
  sidebarCollapsed: boolean; // 侧边栏默认折叠状态
  density: 'comfortable' | 'compact'; // 界面密度
  animation: boolean; // 启用动画
}

export interface InterfaceSettings {
  theme: ThemeSettings;
  layout: LayoutSettings;
  language: 'zh-CN' | 'en-US'; // 系统语言
}

export interface SystemNotifications {
  updates: boolean; // 系统更新通知
  errors: boolean; // 错误警告
  maintenance: boolean; // 维护通知
}

export interface NotificationSettings {
  appointments: AppointmentNotifications;
  system: SystemNotifications;
}

export interface BackupConfig {
  autoBackup: boolean; // 自动备份
  backupTime: string; // 备份时间 HH:mm
  frequency: 'daily' | 'weekly' | 'monthly'; // 备份频率
  location: 'local' | 'cloud'; // 存储位置
  encryption: boolean; // 加密备份
}

export interface ExportConfig {
  format: 'xlsx' | 'csv' | 'pdf'; // 导出格式
  dateRange: 'all' | 'year' | 'month' | 'custom'; // 数据范围
  includeImages: boolean; // 包含图片
  compression: boolean; // 压缩文件
}

export interface CleanupConfig {
  tempFiles: boolean; // 清理临时文件
  logs: boolean; // 清理日志
  cache: boolean; // 清理缓存
  oldBackups: boolean; // 清理旧备份
}

export interface DataManagement {
  backup: BackupConfig;
  export: ExportConfig;
  cleanup: CleanupConfig;
}

export interface AppSettings {
  user: UserSettings;
  interface: InterfaceSettings;
  notifications: NotificationSettings;
  dataManagement: DataManagement;
  version: string; // 设置版本，用于迁移
  lastUpdated: string; // 最后更新时间
}

// 设置操作结果
export interface SettingsResult {
  success: boolean;
  message: string;
  data?: any;
}

// 设置标签页类型
export type SettingsTab = 'user' | 'interface' | 'notifications' | 'data';

export interface SettingsTabConfig {
  id: SettingsTab;
  title: string;
  icon: string;
  description: string;
}
