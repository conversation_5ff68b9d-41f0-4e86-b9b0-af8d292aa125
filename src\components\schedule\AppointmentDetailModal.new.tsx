import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { <PERSON>ton, Badge } from '../ui';
import { Calendar, Clock, User, Phone, MapPin, Bell, AlertTriangle, Star } from 'lucide-react';
import type { Appointment } from '../../types/schedule';
import { formatTimeRange } from '../../data/mockSchedule';

interface AppointmentDetailModalProps {
  isOpen: boolean;
  appointment: Appointment;
  onClose: () => void;
  onEdit: (appointment: Appointment) => void;
  onUpdate?: (appointment: Appointment) => void;
}

const AppointmentDetailModal: React.FC<AppointmentDetailModalProps> = ({ 
  isOpen, 
  appointment, 
  onClose, 
  onEdit,
  onUpdate 
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  const getStatusVariant = (status: string): "primary" | "danger" | "warning" | "success" | "gray" => {
    switch (status) {
      case '已完成': return 'success';
      case '已确认': 
      case '进行中': return 'primary';
      case '已取消': return 'danger';
      case '待确认': return 'warning';
      default: return 'gray';
    }
  };

  const getUrgencyVariant = (urgency: string): "primary" | "danger" | "warning" | "success" | "gray" => {
    switch (urgency) {
      case '危机干预': return 'danger';
      case '紧急': return 'warning';
      case '普通': return 'gray';
      default: return 'gray';
    }
  };

  const handleStatusChange = (newStatus: Appointment['status']) => {
    if (onUpdate) {
      const updatedAppointment = { 
        ...appointment, 
        status: newStatus, 
        updatedAt: new Date().toISOString() 
      };
      onUpdate(updatedAppointment);
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="预约详情"
      subtitle={`${appointment.visitorName} - ${appointment.subject}`}
      size="xl"
    >
      <div className="form-modal-body">
        {/* 状态信息 */}
        <div className="form-section">
          <h4 className="form-section-title">状态信息</h4>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">预约状态</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                <Badge variant={getStatusVariant(appointment.status)}>
                  {appointment.status}
                </Badge>
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">紧急程度</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                <Badge variant={getUrgencyVariant(appointment.urgency)}>
                  {appointment.urgency !== '普通' && <AlertTriangle size={14} style={{ marginRight: '4px' }} />}
                  {appointment.urgency}
                </Badge>
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">预约类型</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {appointment.isFirstSession && <Star size={16} style={{ marginRight: '4px', color: '#fbbf24' }} />}
                {appointment.type}
                {appointment.isFirstSession && ' (首次)'}
              </div>
            </div>
          </div>
        </div>

        {/* 来访者信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <User size={16} />
            来访者信息
          </h4>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">姓名</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {appointment.visitorName}
              </div>
            </div>
            
            {appointment.visitorPhone && (
              <div className="form-group">
                <label className="form-label">手机号</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Phone size={16} />
                  {appointment.visitorPhone}
                </div>
              </div>
            )}
            
            {appointment.visitorAge && (
              <div className="form-group">
                <label className="form-label">年龄</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                  {appointment.visitorAge}岁
                </div>
              </div>
            )}
            
            {appointment.visitorGender && (
              <div className="form-group">
                <label className="form-label">性别</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                  {appointment.visitorGender}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 时间安排 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <Calendar size={16} />
            时间安排
          </h4>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">日期</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Calendar size={16} />
                {formatDate(appointment.date)}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">时间</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Clock size={16} />
                {formatTimeRange(appointment.startTime, appointment.endTime)}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">时长</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {appointment.duration}分钟
              </div>
            </div>
          </div>
        </div>

        {/* 服务信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <MapPin size={16} />
            服务信息
          </h4>
          
          <div className="form-grid form-grid-2">
            <div className="form-group">
              <label className="form-label">治疗师</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {appointment.therapistName}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">咨询室</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {appointment.room}
              </div>
            </div>
          </div>
          
          {appointment.sessionNumber && (
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <label className="form-label">咨询次数</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                  第{appointment.sessionNumber}次
                  {appointment.totalPlannedSessions && 
                    ` / 共${appointment.totalPlannedSessions}次`
                  }
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 咨询内容 */}
        <div className="form-section">
          <h4 className="form-section-title">咨询内容</h4>
          
          <div className="form-grid form-grid-1">
            <div className="form-group">
              <label className="form-label">主题</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {appointment.subject}
              </div>
            </div>
          </div>
          
          {appointment.description && (
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <label className="form-label">详细描述</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', minHeight: '60px', alignItems: 'flex-start', padding: '12px' }}>
                  {appointment.description}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 提醒设置 */}
        {appointment.reminderEnabled && (
          <div className="form-section">
            <h4 className="form-section-title">
              <Bell size={16} />
              提醒设置
            </h4>
            
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <label className="form-label">提醒时间</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Bell size={16} />
                  提前{appointment.reminderTime}分钟
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 费用信息 */}
        {appointment.fee && (
          <div className="form-section">
            <h4 className="form-section-title">费用信息</h4>
            
            <div className="form-grid form-grid-2">
              <div className="form-group">
                <label className="form-label">费用</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                  ￥{appointment.fee}
                </div>
              </div>
              
              {appointment.paymentStatus && (
                <div className="form-group">
                  <label className="form-label">支付状态</label>
                  <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                    <Badge variant={appointment.paymentStatus === '已支付' ? 'success' : 'warning'}>
                      {appointment.paymentStatus}
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 备注 */}
        {appointment.notes && (
          <div className="form-section">
            <h4 className="form-section-title">备注</h4>
            
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', minHeight: '60px', alignItems: 'flex-start', padding: '12px' }}>
                  {appointment.notes}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 操作历史 */}
        <div className="form-section">
          <h4 className="form-section-title">操作历史</h4>
          
          <div className="form-grid form-grid-2">
            <div className="form-group">
              <label className="form-label">创建时间</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {formatDateTime(appointment.createdAt)}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">创建者</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {appointment.createdBy}
              </div>
            </div>
          </div>
          
          {appointment.updatedAt !== appointment.createdAt && (
            <div className="form-grid form-grid-1">
              <div className="form-group">
                <label className="form-label">最后更新</label>
                <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                  {formatDateTime(appointment.updatedAt)}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 状态操作按钮 */}
        {onUpdate && appointment.status !== '已完成' && appointment.status !== '已取消' && (
          <div className="form-section">
            <h4 className="form-section-title">预约操作</h4>
            
            <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              {appointment.status === '待确认' && (
                <>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => handleStatusChange('已确认')}
                  >
                    确认预约
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleStatusChange('已取消')}
                  >
                    取消预约
                  </Button>
                </>
              )}
              
              {appointment.status === '已确认' && (
                <>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => handleStatusChange('进行中')}
                  >
                    开始咨询
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleStatusChange('已取消')}
                  >
                    取消预约
                  </Button>
                </>
              )}
              
              {appointment.status === '进行中' && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => handleStatusChange('已完成')}
                >
                  完成咨询
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="form-modal-footer">
        <div className="form-modal-actions">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            关闭
          </Button>
          <Button
            onClick={() => onEdit(appointment)}
          >
            编辑预约
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export { AppointmentDetailModal };
export default AppointmentDetailModal;
