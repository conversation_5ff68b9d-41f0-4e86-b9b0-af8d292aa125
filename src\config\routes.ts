// 页面路由配置
export type PageType = 'dashboard' | 'visitors' | 'schedule' | 'tools' | 'group-sessions' | 'analytics' | 'settings';

export interface PageConfig {
  id: PageType;
  title: string;
  path: string;
  icon?: string;
  description?: string;
}

export const pageConfigs: Record<PageType, PageConfig> = {
  dashboard: {
    id: 'dashboard',
    title: '首页',
    path: '/',
    description: '系统概览和快捷操作'
  },
  visitors: {
    id: 'visitors',
    title: '来访者管理',
    path: '/visitors',
    description: '管理和查看所有来访者信息'
  },
  schedule: {
    id: 'schedule',
    title: '日程安排',
    path: '/schedule',
    description: '管理咨询日程和预约'
  },
  tools: {
    id: 'tools',
    title: '沙具管理',
    path: '/tools',
    description: '管理沙盘治疗工具'
  },
  'group-sessions': {
    id: 'group-sessions',
    title: '团沙管理',
    path: '/group-sessions',
    description: '管理团体沙盘治疗活动'
  },
  analytics: {
    id: 'analytics',
    title: '数据统计',
    path: '/analytics',
    description: '查看系统使用统计'
  },
  settings: {
    id: 'settings',
    title: '系统设置',
    path: '/settings',
    description: '系统配置和用户设置'
  }
};
