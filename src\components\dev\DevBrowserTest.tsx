import React, { useState } from 'react';
import { browserService, openExternal, openBaidu, openGitHub, search } from '../../services/browserService';
import './DevBrowserTest.css';

/**
 * 开发测试组件 - 浏览器功能测试
 * 注意：这是临时开发组件，封版时需要删除
 */
const DevBrowserTest: React.FC = () => {
  const [url, setUrl] = useState('https://www.baidu.com');
  const [searchQuery, setSearchQuery] = useState('');
  const [filePath, setFilePath] = useState('');
  const [message, setMessage] = useState('');

  const showMessage = (msg: string, isError: boolean = false) => {
    setMessage(msg);
    console.log(isError ? `错误: ${msg}` : `信息: ${msg}`);
    setTimeout(() => setMessage(''), 3000);
  };

  const handleOpenExternal = async () => {
    if (!url.trim()) {
      showMessage('请输入有效的URL', true);
      return;
    }

    const success = await openExternal(url);
    if (success) {
      showMessage(`成功打开: ${url}`);
    } else {
      showMessage(`打开失败: ${url}`, true);
    }
  };

  const handleQuickOpen = async (type: 'baidu' | 'github') => {
    const success = type === 'baidu' ? await openBaidu() : await openGitHub();
    if (success) {
      showMessage(`成功打开${type === 'baidu' ? '百度' : 'GitHub'}`);
    } else {
      showMessage(`打开${type === 'baidu' ? '百度' : 'GitHub'}失败`, true);
    }
  };

  const handleSearch = async (engine: 'baidu' | 'google') => {
    if (!searchQuery.trim()) {
      showMessage('请输入搜索内容', true);
      return;
    }

    const success = await search(searchQuery, engine);
    if (success) {
      showMessage(`成功在${engine === 'baidu' ? '百度' : 'Google'}中搜索: ${searchQuery}`);
    } else {
      showMessage(`搜索失败`, true);
    }
  };

  const handleShowInFolder = async () => {
    if (!filePath.trim()) {
      showMessage('请输入文件路径', true);
      return;
    }

    const success = await browserService.showItemInFolder(filePath);
    if (success) {
      showMessage(`成功在文件管理器中显示: ${filePath}`);
    } else {
      showMessage(`显示文件失败: ${filePath}`, true);
    }
  };

  const handleTestUrls = async () => {
    const testUrls = [
      'https://www.baidu.com',
      'https://github.com',
      'https://www.google.com',
      'https://stackoverflow.com',
      'https://developer.mozilla.org'
    ];

    showMessage('开始批量测试URL...');
    for (const testUrl of testUrls) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
      const success = await openExternal(testUrl);
      console.log(`测试 ${testUrl}: ${success ? '成功' : '失败'}`);
    }
    showMessage('批量测试完成，请查看控制台日志');
  };

  return (
    <div className="dev-browser-test">
      <div className="test-header">
        <h2>🌐 浏览器功能测试</h2>
        <p className="warning">⚠️ 这是开发期间的临时功能，封版时会移除</p>
        <p className="env-info">
          环境: {browserService.isElectronEnvironment() ? 'Electron' : 'Web浏览器'}
        </p>
      </div>

      {message && (
        <div className={`message ${message.includes('失败') || message.includes('错误') ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      {/* URL 测试区域 */}
      <div className="test-section">
        <h3>URL 打开测试</h3>
        <div className="input-group">
          <input
            type="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="输入要打开的URL..."
            className="url-input"
          />
          <button onClick={handleOpenExternal} className="btn-primary">
            打开链接
          </button>
        </div>
      </div>

      {/* 快捷按钮 */}
      <div className="test-section">
        <h3>快捷打开</h3>
        <div className="button-group">
          <button onClick={() => handleQuickOpen('baidu')} className="btn-secondary">
            打开百度
          </button>
          <button onClick={() => handleQuickOpen('github')} className="btn-secondary">
            打开GitHub
          </button>
        </div>
      </div>

      {/* 搜索测试 */}
      <div className="test-section">
        <h3>搜索测试</h3>
        <div className="input-group">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="输入搜索内容..."
            className="search-input"
          />
          <button onClick={() => handleSearch('baidu')} className="btn-baidu">
            百度搜索
          </button>
          <button onClick={() => handleSearch('google')} className="btn-google">
            Google搜索
          </button>
        </div>
      </div>

      {/* 文件管理器测试 (仅Electron) */}
      {browserService.isElectronEnvironment() && (
        <div className="test-section">
          <h3>文件管理器测试</h3>
          <div className="input-group">
            <input
              type="text"
              value={filePath}
              onChange={(e) => setFilePath(e.target.value)}
              placeholder="输入文件路径..."
              className="path-input"
            />
            <button onClick={handleShowInFolder} className="btn-primary">
              在文件夹中显示
            </button>
          </div>
          <p className="hint">
            示例路径: C:\Users\<USER>\Desktop 或 C:\Windows\System32\notepad.exe
          </p>
        </div>
      )}

      {/* 批量测试 */}
      <div className="test-section">
        <h3>批量测试</h3>
        <button onClick={handleTestUrls} className="btn-warning">
          批量打开测试URL (5个)
        </button>
        <p className="hint">
          将打开多个常用网站，请注意浏览器弹窗
        </p>
      </div>

      {/* 功能说明 */}
      <div className="test-section info-section">
        <h3>功能说明</h3>
        <ul>
          <li>✅ 在系统默认浏览器中打开URL</li>
          <li>✅ 快捷访问常用网站</li>
          <li>✅ 搜索引擎集成</li>
          <li>✅ 文件管理器集成 (仅Electron)</li>
          <li>✅ URL格式验证</li>
          <li>✅ 错误处理和用户反馈</li>
        </ul>
      </div>

      {/* 封版提醒 */}
      <div className="test-section warning-section">
        <h3>⚠️ 封版时需要移除的文件</h3>
        <ul>
          <li>src/components/dev/DevBrowserTest.tsx (本文件)</li>
          <li>src/components/dev/DevBrowserTest.css</li>
          <li>src/services/browserService.ts</li>
          <li>electron/main.js 中的浏览器相关代码</li>
          <li>electron/preload.js 中的浏览器API</li>
        </ul>
      </div>
    </div>
  );
};

export default DevBrowserTest;
