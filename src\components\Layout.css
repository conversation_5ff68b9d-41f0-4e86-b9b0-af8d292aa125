/* Layout组件样式 */
.layout {
  min-height: 100vh;
  background-color: #f8fafc;
}

.main-content {
  margin-left: 240px;
  padding-top: 60px; /* Header高度 */
  min-height: 100vh;
  transition: margin-left 0.3s ease;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.main-content::-webkit-scrollbar {
  display: none;
}

.main-content.sidebar-collapsed {
  margin-left: 60px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }
  
  .main-content.sidebar-collapsed {
    margin-left: 0;
  }
}
