/* 日程安排页面样式 */
.schedule-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  height: 100vh;
  gap: 0;
  background: #f8fafc;
}

/* 左侧边栏 */
.schedule-sidebar {
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  padding: 24px;
  overflow-y: auto;
}

.sidebar-header {
  margin-bottom: 32px;
}

.schedule-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.create-btn {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.create-btn:hover {
  background: #2563eb;
}

/* 今日概览 */
.today-summary {
  margin-bottom: 32px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.summary-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #334155;
  margin: 0 0 16px 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
}

.stat-card.urgent {
  background: #fef2f2;
  border-color: #fecaca;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.next-appointment {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.next-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8px;
}

.next-time {
  font-size: 16px;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 4px;
}

.next-visitor {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.next-subject {
  font-size: 12px;
  color: #64748b;
}

/* 日期导航 */
.date-navigator {
  margin-bottom: 32px;
}

.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.nav-btn {
  background: none;
  border: 1px solid #e2e8f0;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.current-date {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
  text-align: center;
  flex: 1;
  margin: 0 12px;
}

.today-btn {
  background: none;
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.today-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

/* 快速筛选 */
.quick-filters {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.filter-title {
  font-size: 14px;
  font-weight: 600;
  color: #334155;
  margin: 0 0 16px 0;
}

.filter-group {
  margin-bottom: 16px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-group label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 6px;
}

.filter-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  color: #334155;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 主内容区 */
.schedule-main {
  padding: 24px;
  overflow-y: auto;
}

/* 工具栏 */
.schedule-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.toolbar-left .view-switcher {
  display: flex;
  background: #f8fafc;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-btn:hover:not(.active) {
  color: #334155;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box svg {
  position: absolute;
  left: 12px;
  color: #94a3b8;
  pointer-events: none;
}

.search-input {
  padding: 8px 12px 8px 40px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #334155;
  width: 240px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: #94a3b8;
}

/* 预约列表区域 */
.appointments-area {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.area-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.area-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.area-title .count {
  font-weight: 400;
  color: #64748b;
  margin-left: 8px;
}

/* 空状态 */
.empty-state {
  padding: 60px 24px;
  text-align: center;
  color: #64748b;
}

.empty-state svg {
  margin-bottom: 16px;
  color: #cbd5e1;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: #475569;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 24px 0;
}

/* 预约网格 */
.appointments-grid {
  padding: 24px;
}

/* 日视图预约卡片 */
.appointment-card.day-view {
  display: flex;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 16px;
  transition: all 0.2s;
  position: relative;
}

.appointment-card.day-view:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-time {
  flex-shrink: 0;
  width: 120px;
  padding: 20px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  border-radius: 12px 0 0 12px;
  text-align: center;
}

.time-main {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.time-duration {
  font-size: 12px;
  color: #64748b;
}

.card-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.visitor-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.card-body .subject {
  font-size: 14px;
  color: #475569;
  margin-bottom: 8px;
}

.details {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.details span {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
}

.details .type {
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.urgency-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #f59e0b;
  background: #fef3c7;
  padding: 4px;
  border-radius: 50%;
}

/* 日期分组（周视图/月视图） */
.day-group {
  margin-bottom: 24px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.group-date {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.group-count {
  font-size: 14px;
  color: #64748b;
}

.group-appointments {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.appointment-item.compact {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s;
}

.appointment-item.compact:hover {
  background: white;
  border-color: #cbd5e1;
}

.item-time {
  flex-shrink: 0;
  width: 80px;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
}

.item-content {
  flex: 1;
  margin-left: 16px;
}

.item-visitor {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.item-subject {
  font-size: 12px;
  color: #64748b;
}

.item-status {
  margin-left: 16px;
  margin-right: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .schedule-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .schedule-sidebar {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .summary-stats {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .quick-filters {
    display: flex;
    gap: 16px;
    align-items: center;
  }
  
  .filter-group {
    margin-bottom: 0;
    flex: 1;
  }
}

@media (max-width: 768px) {
  .schedule-layout {
    grid-template-columns: 1fr;
  }
  
  .schedule-sidebar {
    padding: 16px;
  }
  
  .schedule-main {
    padding: 16px;
  }
  
  .schedule-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .card-time {
    width: 100px;
    padding: 16px;
  }
  
  .appointment-card.day-view {
    flex-direction: column;
  }
  
  .card-time {
    width: auto;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 12px 12px 0 0;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
