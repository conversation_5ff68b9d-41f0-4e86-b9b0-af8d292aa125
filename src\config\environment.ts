// 环境配置文件
export const config = {
  // 应用信息
  app: {
    name: '沙盘管理系统',
    version: '1.0.0',
    description: '专业的心理健康沙盘疗法管理系统'
  },

  // 数据库配置 - 仅支持桌面端SQLite
  database: {
    name: 'xlsp',
    version: 1,
    type: 'sqlite' // 仅支持桌面端SQLite数据库
  },

  // 开发环境配置
  development: {
    showDebugInfo: true,
    enableDevTools: true,
    autoOpenDevTools: false
  },

  // 生产环境配置
  production: {
    showDebugInfo: false,
    enableDevTools: false,
    autoBackup: true,
    backupInterval: 24 * 60 * 60 * 1000 // 24小时
  },

  // UI 配置
  ui: {
    theme: 'default',
    language: 'zh-CN',
    animations: true,
    compactMode: false
  },

  // 安全配置
  security: {
    encryptSensitiveData: true,
    sessionTimeout: 30 * 60 * 1000, // 30分钟
    maxLoginAttempts: 5
  }
};

// 环境检测 - 仅支持桌面端
export const environment = {
  // 必须为 Electron 桌面环境
  isElectron: typeof window !== 'undefined' && window.electronAPI?.isElectron,
  
  // 是否为开发环境
  isDevelopment: import.meta.env.DEV,
  
  // 是否为生产环境
  isProduction: import.meta.env.PROD,
  
  // 平台信息 - 仅支持桌面平台
  platform: typeof window !== 'undefined' ? 
    (window.electronAPI?.platform || 'desktop') : 'desktop',
  
  // 检查环境有效性
  validateEnvironment() {
    if (!this.isElectron) {
      throw new Error('此应用仅支持桌面环境运行，请使用桌面版本');
    }
    return true;
  },
  
  // 获取当前配置
  getConfig() {
    this.validateEnvironment();
    return this.isProduction ? config.production : config.development;
  },

  // 获取存储类型 - 仅SQLite
  getStorageType() {
    this.validateEnvironment();
    return 'SQLite';
  },

  // 获取应用信息
  getAppInfo() {
    this.validateEnvironment();
    return {
      ...config.app,
      environment: this.isProduction ? 'production' : 'development',
      platform: this.platform,
      storageType: 'SQLite',
      isElectron: true,
      supportedPlatforms: ['Windows', 'macOS', 'Linux']
    };
  }
};

// 日志工具
export const logger = {
  debug: (...args: any[]) => {
    if (environment.getConfig().showDebugInfo) {
      console.log('[DEBUG]', ...args);
    }
  },
  
  info: (...args: any[]) => {
    console.info('[INFO]', ...args);
  },
  
  warn: (...args: any[]) => {
    console.warn('[WARN]', ...args);
  },
  
  error: (...args: any[]) => {
    console.error('[ERROR]', ...args);
  }
};
