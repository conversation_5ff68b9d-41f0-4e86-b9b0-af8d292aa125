/* ActionButtons 组件样式 - 简洁实用设计 */
.action-buttons-container {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
}

.action-buttons-container.compact {
  gap: 6px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #ffffff;
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 32px;
  white-space: nowrap;
  text-decoration: none;
}

.action-buttons-container.compact .action-btn {
  padding: 4px 8px;
  min-height: 28px;
  gap: 4px;
  font-size: 11px;
  border-radius: 4px;
}

.action-btn:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.action-btn:active {
  background: #f1f5f9;
}

/* 查看按钮 - 蓝色主题 */
.action-btn.view-btn {
  color: #3b82f6;
  border-color: #dbeafe;
}

.action-btn.view-btn:hover {
  color: #2563eb;
  border-color: #3b82f6;
  background: #eff6ff;
}

/* 编辑按钮 - 绿色主题 */
.action-btn.edit-btn {
  color: #10b981;
  border-color: #d1fae5;
}

.action-btn.edit-btn:hover {
  color: #059669;
  border-color: #10b981;
  background: #ecfdf5;
}

/* 删除按钮 - 红色主题 */
.action-btn.delete-btn {
  color: #ef4444;
  border-color: #fecaca;
}

.action-btn.delete-btn:hover {
  color: #dc2626;
  border-color: #ef4444;
  background: #fef2f2;
}

/* 按钮文字样式 */
.action-btn span {
  font-weight: 500;
}

/* 表格内的特殊处理 */
.table .action-buttons-container,
.action-buttons-table {
  margin: -2px 0;
}

.table .action-buttons-container .action-btn,
.action-buttons-table .action-btn {
  padding: 4px 8px;
  min-height: 28px;
  gap: 4px;
  font-size: 11px;
  border-radius: 4px;
}

.table .action-buttons-container.compact .action-btn,
.action-buttons-table.compact .action-btn {
  padding: 3px 6px;
  min-height: 24px;
  gap: 3px;
  font-size: 10px;
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons-container:not(.compact) {
    flex-direction: row;
    gap: 8px;
    align-items: center;
  }
  
  .action-btn {
    width: auto;
    justify-content: center;
  }
}