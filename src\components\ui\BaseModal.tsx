import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import './BaseModal.css';

interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
  showCloseButton?: boolean;
  className?: string;
}

export const BaseModal: React.FC<BaseModalProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  size = 'md',
  children,
  showCloseButton = true,
  className = ''
}) => {
  // 处理ESC键关闭
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // 阻止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="base-modal-overlay" onClick={onClose}>
      <div 
        className={`base-modal-container base-modal-${size} ${className}`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        {(title || showCloseButton) && (
          <div className="base-modal-header">
            <div className="base-modal-header-content">
              {title && (
                <div className="base-modal-title-section">
                  <h3 className="base-modal-title">{title}</h3>
                  {subtitle && <p className="base-modal-subtitle">{subtitle}</p>}
                </div>
              )}
            </div>
            {showCloseButton && (
              <button
                className="base-modal-close"
                onClick={onClose}
                aria-label="关闭弹窗"
              >
                <X size={20} />
              </button>
            )}
          </div>
        )}

        {/* 内容区域 */}
        <div className="base-modal-content">
          {children}
        </div>
      </div>
    </div>
  );
};
