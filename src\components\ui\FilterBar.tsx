import React from 'react';
import { Input, Select } from './Form';
import './FilterBar.css';

interface FilterBarProps {
  searchProps: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder?: string;
    leftIcon?: React.ReactNode;
  };
  filters: Array<{
    value: string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    options: Array<{ value: string; label: string; disabled?: boolean }>;
  }>;
  className?: string;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  searchProps,
  filters,
  className = ''
}) => {
  return (
    <div className={`filter-bar ${className}`}>
      <div className="filter-bar-search">
        <Input
          {...searchProps}
          className="filter-bar-search-input"
        />
      </div>
      <div className="filter-bar-controls">
        {filters.map((filter, index) => (
          <Select
            key={index}
            {...filter}
            className="filter-bar-select"
          />
        ))}
      </div>
    </div>
  );
};
