import React, { useState } from 'react';
import { FormModal } from '../ui/FormModal';
import { Input, Select, Textarea } from '../ui';
import { User, Phone, Briefcase } from 'lucide-react';
import { visitorService } from '../../services/visitorService';
import type { Visitor, CreateVisitorForm } from '../../types/visitor';

interface EditVisitorModalProps {
  isOpen: boolean;
  visitor: Visitor;
  onClose: () => void;
  onSubmit: (updatedVisitor: Visitor) => void;
}

const EditVisitorModal: React.FC<EditVisitorModalProps> = ({ isOpen, visitor, onClose, onSubmit }) => {
  const [formData, setFormData] = useState<CreateVisitorForm>({
    name: visitor.name,
    gender: visitor.gender,
    age: visitor.age,
    phone: visitor.phone,
    email: visitor.email || '',
    emergencyContact: visitor.emergencyContact,
    emergencyPhone: visitor.emergencyPhone,
    occupation: visitor.occupation || '',
    education: visitor.education || '',
    address: visitor.address || '',
    notes: visitor.notes || '',
    status: visitor.status
  });

  const [errors, setErrors] = useState<Partial<Record<keyof CreateVisitorForm, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof CreateVisitorForm, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof CreateVisitorForm, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入姓名';
    }

    const ageValue = Number(formData.age);
    if (!formData.age || ageValue < 1 || ageValue > 120) {
      newErrors.age = '请输入有效年龄（1-120）';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号码';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入有效的手机号码';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    if (!formData.emergencyContact.trim()) {
      newErrors.emergencyContact = '请输入紧急联系人';
    }

    if (!formData.emergencyPhone.trim()) {
      newErrors.emergencyPhone = '请输入紧急联系电话';
    } else if (!/^1[3-9]\d{9}$/.test(formData.emergencyPhone)) {
      newErrors.emergencyPhone = '请输入有效的紧急联系电话';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const updateData = {
        ...formData,
        age: Number(formData.age)
      };

      const updatedVisitor = await visitorService.updateVisitor(visitor.id, updateData);
      
      if (updatedVisitor) {
        onSubmit(updatedVisitor);
        onClose();
      } else {
        alert('更新失败，来访者不存在');
      }
    } catch (error) {
      console.error('更新来访者失败:', error);
      alert('更新来访者失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="编辑来访者"
      subtitle="请修改来访者的信息"
      size="lg"
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      submitText="保存修改"
      cancelText="取消"
    >
      {/* 基本信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <User size={16} />
          基本信息
        </h4>
        
        <div className="form-grid form-grid-3">
          <Input
            label="姓名"
            required
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={errors.name}
            placeholder="请输入姓名"
          />
          
          <Select
            label="性别"
            required
            value={formData.gender}
            onChange={(e) => handleInputChange('gender', e.target.value as '男' | '女')}
            options={[
              { value: '男', label: '男' },
              { value: '女', label: '女' }
            ]}
          />

          <Input
            label="年龄"
            type="number"
            required
            value={formData.age}
            onChange={(e) => handleInputChange('age', e.target.value)}
            error={errors.age}
            placeholder="请输入年龄"
            min="1"
            max="120"
          />
        </div>
        
        <div className="form-grid form-grid-3">
          <Select
            label="状态"
            required
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value as '活跃' | '暂停' | '完成')}
            options={[
              { value: '活跃', label: '活跃' },
              { value: '暂停', label: '暂停' },
              { value: '完成', label: '完成' }
            ]}
          />
        </div>
      </div>

      {/* 联系信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Phone size={16} />
          联系信息
        </h4>
        
        <div className="form-grid form-grid-2">
          <Input
            label="手机号码"
            required
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            error={errors.phone}
            placeholder="请输入手机号码"
          />
          
          <Input
            label="邮箱"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={errors.email}
            placeholder="请输入邮箱（可选）"
          />
        </div>
        
        <div className="form-grid form-grid-2">
          <Input
            label="紧急联系人"
            required
            value={formData.emergencyContact}
            onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
            error={errors.emergencyContact}
            placeholder="请输入紧急联系人姓名"
          />
          
          <Input
            label="紧急联系电话"
            required
            value={formData.emergencyPhone}
            onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
            error={errors.emergencyPhone}
            placeholder="请输入紧急联系电话"
          />
        </div>
      </div>

      {/* 其他信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Briefcase size={16} />
          其他信息
        </h4>
        
        <div className="form-grid form-grid-2">
          <Input
            label="职业"
            value={formData.occupation}
            onChange={(e) => handleInputChange('occupation', e.target.value)}
            placeholder="请输入职业（可选）"
          />
          
          <Select
            label="学历"
            value={formData.education}
            onChange={(e) => handleInputChange('education', e.target.value)}
            options={[
              { value: '', label: '请选择学历' },
              { value: '小学', label: '小学' },
              { value: '初中', label: '初中' },
              { value: '高中', label: '高中' },
              { value: '中专', label: '中专' },
              { value: '大专', label: '大专' },
              { value: '本科', label: '本科' },
              { value: '硕士', label: '硕士' },
              { value: '博士', label: '博士' }
            ]}
          />
        </div>
        
        <div className="form-grid form-grid-1">
          <Textarea
            label="地址"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            placeholder="请输入详细地址（可选）"
            rows={2}
          />
        </div>
        
        <div className="form-grid form-grid-1">
          <Textarea
            label="备注"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="请输入备注信息（可选）"
            rows={3}
          />
        </div>
      </div>
    </FormModal>
  );
};

export { EditVisitorModal };
export default EditVisitorModal;