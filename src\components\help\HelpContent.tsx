import React from 'react';
import { 
  ChevronRight, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  Mail,
  Phone,
  Globe,
  MessageCircle,
  ExternalLink
} from 'lucide-react';
import type { 
  HelpItem, 
  HelpNavigation,
  ContactInfo 
} from '../../types/help';

interface HelpContentProps {
  content: any;
  navigation: HelpNavigation;
  onNavigate: (categoryId: string, sectionId: string, itemId: string) => void;
  contactInfo: ContactInfo[];
}

interface StepGuideProps {
  item: HelpItem;
}

interface FAQSectionProps {
  item: HelpItem;
}

interface ContactSectionProps {
  contactInfo: ContactInfo[];
}

// 步骤指南组件
const StepGuide: React.FC<StepGuideProps> = ({ item }) => {
  if (!item.steps || item.steps.length === 0) {
    return (
      <div className="help-empty-content">
        <Info size={48} className="empty-icon" />
        <p>暂无详细步骤说明</p>
      </div>
    );
  }

  return (
    <div className="step-guide">
      {item.steps.map((step, index) => (
        <div key={step.id} className="step-item">
          <div className="step-header">
            <div className="step-number">{index + 1}</div>
            <h4 className="step-title">{step.title}</h4>
          </div>
          
          <div className="step-content">
            <p className="step-description">{step.description}</p>
            
            {step.tips && step.tips.length > 0 && (
              <div className="step-tips">
                <div className="tips-header">
                  <CheckCircle size={16} className="tips-icon" />
                  <span>操作提示</span>
                </div>
                <ul className="tips-list">
                  {step.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="tip-item">{tip}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {step.warning && (
              <div className="step-warning">
                <AlertTriangle size={16} className="warning-icon" />
                <span className="warning-text">{step.warning}</span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

// FAQ组件
const FAQSection: React.FC<FAQSectionProps> = ({ item }) => {
  if (!item.steps || item.steps.length === 0) {
    return (
      <div className="help-empty-content">
        <Info size={48} className="empty-icon" />
        <p>暂无常见问题</p>
      </div>
    );
  }

  return (
    <div className="faq-section">
      {item.steps.map((step) => (
        <div key={step.id} className="faq-item">
          <div className="faq-question">
            <h4>{step.title}</h4>
          </div>
          <div className="faq-answer">
            <p>{step.description}</p>
            {step.tips && step.tips.length > 0 && (
              <div className="faq-tips">
                <strong>具体步骤：</strong>
                <ol className="faq-steps">
                  {step.tips.map((tip, tipIndex) => (
                    <li key={tipIndex}>{tip}</li>
                  ))}
                </ol>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

// 联系我们组件
const ContactSection: React.FC<ContactSectionProps> = ({ contactInfo }) => {
  const getContactIcon = (type: string) => {
    switch (type) {
      case 'wechat':
        return <MessageCircle size={24} />;
      case 'email':
        return <Mail size={24} />;
      case 'phone':
        return <Phone size={24} />;
      case 'website':
        return <Globe size={24} />;
      default:
        return <Info size={24} />;
    }
  };

  const getContactColor = (type: string) => {
    switch (type) {
      case 'wechat':
        return 'contact-wechat';
      case 'email':
        return 'contact-email';
      case 'phone':
        return 'contact-phone';
      case 'website':
        return 'contact-website';
      default:
        return 'contact-default';
    }
  };

  return (
    <div className="contact-section">
      <div className="contact-intro">
        <h3>多种方式联系我们</h3>
        <p>如果您在使用过程中遇到任何问题，可以通过以下方式联系我们获取帮助：</p>
      </div>

      <div className="contact-grid">
        {contactInfo.map((contact, index) => (
          <div key={index} className={`contact-card ${getContactColor(contact.type)}`}>
            <div className="contact-header">
              <div className="contact-icon">
                {getContactIcon(contact.type)}
              </div>
              <h4 className="contact-label">{contact.label}</h4>
            </div>
            
            <div className="contact-body">
              {contact.qrCode ? (
                <div className="contact-qr-section">
                    <img 
                      src="/gzh.jpg" 
                      alt="公众号二维码" 
                      className="qr-code" 
                    />
                    <p className="qr-text">公众号二维码</p>
                    <p className="qr-subtitle">扫码关注</p>
                    <p className="contact-value">{contact.value}</p>
                  </div>
              ) : (
                <div className="contact-value-section">
                  <p className="contact-value">{contact.value}</p>
                  {contact.type === 'website' && (
                    <a 
                      href={contact.value} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="contact-link"
                    >
                      访问网站 <ExternalLink size={14} />
                    </a>
                  )}
                </div>
              )}
              
              {contact.description && (
                <p className="contact-description">{contact.description}</p>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="contact-note">
        <Info size={16} className="note-icon" />
        <p>
          我们承诺在收到您的咨询后会尽快回复。如果是紧急问题，建议优先使用电话联系方式。
        </p>
      </div>
    </div>
  );
};

const HelpContent: React.FC<HelpContentProps> = ({
  content,
  onNavigate,
  contactInfo
}) => {
  // 如果是搜索结果
  if (Array.isArray(content)) {
    return null; // 搜索结果在Help.tsx中处理

  }

  const { category, section, item } = content;

  if (!category || !section || !item) {
    // 显示分类或章节概览页面
    if (category && section) {
      // 显示章节概览
      return (
        <div className="help-content-container">
          <div className="help-content-header">
            <h1 className="content-title">{section.title}</h1>
            <p className="content-description">{section.description || `本章节包含 ${section.children.length} 个帮助主题`}</p>
          </div>
          <div className="help-content-body">
            <div className="section-overview">
              <h3>本章节内容</h3>
              <div className="overview-items">
                {section.children.map((childItem: any) => (
                  <button
                    key={childItem.id}
                    className="overview-item"
                    onClick={() => onNavigate(category.id, section.id, childItem.id)}
                  >
                    <span className="overview-title">{childItem.title}</span>
                    <ChevronRight size={16} className="overview-arrow" />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      );
    } else if (category) {
      // 显示分类概览
      return (
        <div className="help-content-container">
          <div className="help-content-header">
            <h1 className="content-title">{category.title}</h1>
            <p className="content-description">{category.description || `本分类包含 ${category.children.length} 个章节`}</p>
          </div>
          <div className="help-content-body">
            <div className="category-overview">
              <h3>本分类内容</h3>
              <div className="overview-sections">
                {category.children.map((childSection: any) => (
                  <button
                    key={childSection.id}
                    className="overview-section"
                    onClick={() => onNavigate(category.id, childSection.id, '')}
                  >
                    <div className="section-info">
                      <h4 className="section-title">{childSection.title}</h4>
                      <p className="section-description">{childSection.description || `包含 ${childSection.children.length} 个主题`}</p>
                    </div>
                    <ChevronRight size={16} className="section-arrow" />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    // 默认空状态
    return (
      <div className="help-empty-content">
        <Info size={48} className="empty-icon" />
        <h3>请选择要查看的帮助内容</h3>
        <p>从左侧导航中选择您需要了解的功能模块</p>
      </div>
    );
  }

  // 面包屑导航
  const breadcrumbs = [
    { title: category.title, id: category.id },
    { title: section.title, id: section.id },
    { title: item.title, id: item.id }
  ];

  return (
    <div className="help-content-container">
      {/* 面包屑导航 */}
      <nav className="help-breadcrumb">
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={crumb.id}>
            {index < breadcrumbs.length - 1 ? (
              <button 
                className="breadcrumb-item breadcrumb-link"
                onClick={() => {
                  if (index === 0) {
                    // 点击分类，跳转到该分类的概览页面
                    onNavigate(category.id, '', '');
                  } else if (index === 1) {
                    // 点击章节，跳转到该章节的概览页面
                    onNavigate(category.id, section.id, '');
                  }
                }}
              >
                {crumb.title}
              </button>
            ) : (
              <span className="breadcrumb-item breadcrumb-current">{crumb.title}</span>
            )}
            {index < breadcrumbs.length - 1 && (
              <ChevronRight size={14} className="breadcrumb-separator" />
            )}
          </React.Fragment>
        ))}
      </nav>

      {/* 内容头部 */}
      <div className="help-content-header">
        <h1 className="content-title">{item.title}</h1>
        {item.tags && item.tags.length > 0 && (
          <div className="content-tags">
            {item.tags.map((tag: any, index: any) => (
              <span key={index} className="content-tag">{tag}</span>
            ))}
          </div>
        )}
        
        {/* 相关内容推荐移到头部 - 提高可见性 */}
        {section.children.length > 1 && (
          <div className="help-related-content-header">
            <h4>本章节其他内容</h4>
            <div className="related-items-horizontal">
              {section.children
                .filter((relatedItem: any) => relatedItem.id !== item.id)
                .slice(0, 3) // 在头部只显示前3个相关项目
                .map((relatedItem: any) => (
                  <button
                    key={relatedItem.id}
                    className="related-item-compact"
                    onClick={() => onNavigate(category.id, section.id, relatedItem.id)}
                  >
                    <span className="related-title-compact">{relatedItem.title}</span>
                  </button>
                ))}
              {section.children.filter((relatedItem: any) => relatedItem.id !== item.id).length > 3 && (
                <span className="more-content-hint">+{section.children.length - 4} 更多</span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 内容主体 */}
      <div className="help-content-body">
        {item.type === 'contact' ? (
          <ContactSection contactInfo={contactInfo} />
        ) : item.type === 'faq' ? (
          <FAQSection item={item} />
        ) : (
          <>
            {typeof item.content === 'string' && item.content && (
              <div className="content-description">
                <p>{item.content}</p>
              </div>
            )}
            <StepGuide item={item} />
          </>
        )}
      </div>

      {/* 相关链接保留在底部，作为完整列表 */}
      {section.children.length > 1 && (
        <div className="help-related-content">
          <h3>完整章节内容</h3>
          <div className="related-items">
            {section.children
              .filter((relatedItem: any) => relatedItem.id !== item.id)
              .map((relatedItem: any) => (
                <button
                  key={relatedItem.id}
                  className="related-item"
                  onClick={() => onNavigate(category.id, section.id, relatedItem.id)}
                >
                  <span className="related-title">{relatedItem.title}</span>
                  <ChevronRight size={16} className="related-arrow" />
                </button>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default HelpContent;
