import React, { useState } from 'react';
import { Download, Upload, RotateCcw } from 'lucide-react';
import { Card } from '../ui/Card';
import { Input, Select } from '../ui/Form';
import { Button } from '../ui/Button';
import { SettingsService } from '../../services/settingsService';
import type { AppSettings } from '../../types/settings';

interface DataManagementProps {
  settings: AppSettings;
  updateSettings: (path: string, value: any) => void;
  onSettingsChange: (settings: AppSettings) => void;
  saving: boolean;
}

const DataManagement: React.FC<DataManagementProps> = ({ 
  settings, 
  updateSettings, 
  onSettingsChange 
}) => {
  const { dataManagement } = settings;
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);

  // 导出设置
  const handleExportSettings = async () => {
    setExporting(true);
    try {
      const result = SettingsService.exportSettings();
      if (result.success) {
        alert('设置导出成功！');
      } else {
        alert('导出失败：' + result.message);
      }
    } catch (error) {
      alert('导出过程中发生错误');
    } finally {
      setExporting(false);
    }
  };

  // 导入设置
  const handleImportSettings = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setImporting(true);
    try {
      const result = await SettingsService.importSettings(file);
      if (result.success) {
        const newSettings = SettingsService.loadSettings();
        onSettingsChange(newSettings);
        alert('设置导入成功！');
      } else {
        alert('导入失败：' + result.message);
      }
    } catch (error) {
      alert('导入过程中发生错误');
    } finally {
      setImporting(false);
      // 清空文件输入
      event.target.value = '';
    }
  };

  // 重置设置
  const handleResetSettings = () => {
    if (confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
      const result = SettingsService.resetSettings();
      if (result.success) {
        const defaultSettings = SettingsService.getDefaultSettings();
        onSettingsChange(defaultSettings);
        alert('设置已重置为默认值');
      } else {
        alert('重置失败：' + result.message);
      }
    }
  };

  return (
    <div className="settings-form">
      {/* 备份设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">备份设置</h3>
          <p className="card-subtitle">数据自动备份配置</p>
        </div>
        <div className="card-content">
          <div className="mb-lg">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.backup.autoBackup}
                onChange={(e) => updateSettings('dataManagement.backup.autoBackup', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用自动备份
            </label>
          </div>
          
          {dataManagement.backup.autoBackup && (
            <div className="settings-form-grid">
              <Input
                label="备份时间"
                type="time"
                value={dataManagement.backup.backupTime}
                onChange={(e) => updateSettings('dataManagement.backup.backupTime', e.target.value)}
              />
              
              <Select
                label="备份频率"
                value={dataManagement.backup.frequency}
                onChange={(e) => updateSettings('dataManagement.backup.frequency', e.target.value)}
                options={[
                  { value: 'daily', label: '每日备份' },
                  { value: 'weekly', label: '每周备份' },
                  { value: 'monthly', label: '每月备份' }
                ]}
              />
              
              <Select
                label="存储位置"
                value={dataManagement.backup.location}
                onChange={(e) => updateSettings('dataManagement.backup.location', e.target.value)}
                options={[
                  { value: 'local', label: '本地存储' },
                  { value: 'cloud', label: '云端存储' }
                ]}
              />
            </div>
          )}
          
          {dataManagement.backup.autoBackup && (
            <div className="mt-lg">
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={dataManagement.backup.encryption}
                  onChange={(e) => updateSettings('dataManagement.backup.encryption', e.target.checked)}
                />
                <span className="checkmark"></span>
                启用备份加密
              </label>
            </div>
          )}
        </div>
      </Card>

      {/* 导出设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">数据导出</h3>
          <p className="card-subtitle">数据导出格式和范围设置</p>
        </div>
        <div className="card-content">
          <div className="settings-form-grid">
            <Select
              label="导出格式"
              value={dataManagement.export.format}
              onChange={(e) => updateSettings('dataManagement.export.format', e.target.value)}
              options={[
                { value: 'xlsx', label: 'Excel (.xlsx)' },
                { value: 'csv', label: 'CSV (.csv)' },
                { value: 'pdf', label: 'PDF (.pdf)' }
              ]}
            />
            
            <Select
              label="数据范围"
              value={dataManagement.export.dateRange}
              onChange={(e) => updateSettings('dataManagement.export.dateRange', e.target.value)}
              options={[
                { value: 'all', label: '全部数据' },
                { value: 'year', label: '最近一年' },
                { value: 'month', label: '最近一月' },
                { value: 'custom', label: '自定义范围' }
              ]}
            />
          </div>
          
          <div className="mt-lg">
            <div className="checkbox-group">
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={dataManagement.export.includeImages}
                  onChange={(e) => updateSettings('dataManagement.export.includeImages', e.target.checked)}
                />
                <span className="checkmark"></span>
                包含图片文件
              </label>
              
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={dataManagement.export.compression}
                  onChange={(e) => updateSettings('dataManagement.export.compression', e.target.checked)}
                />
                <span className="checkmark"></span>
                压缩导出文件
              </label>
            </div>
          </div>
        </div>
      </Card>

      {/* 清理设置 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">数据清理</h3>
          <p className="card-subtitle">系统清理和维护工具</p>
        </div>
        <div className="card-content">
          <div className="checkbox-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.tempFiles}
                onChange={(e) => updateSettings('dataManagement.cleanup.tempFiles', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理临时文件
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.logs}
                onChange={(e) => updateSettings('dataManagement.cleanup.logs', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理系统日志
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.cache}
                onChange={(e) => updateSettings('dataManagement.cleanup.cache', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理缓存文件
            </label>
            
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={dataManagement.cleanup.oldBackups}
                onChange={(e) => updateSettings('dataManagement.cleanup.oldBackups', e.target.checked)}
              />
              <span className="checkmark"></span>
              清理旧备份文件
            </label>
          </div>
        </div>
      </Card>

      {/* 设置管理 */}
      <Card>
        <div className="card-header">
          <h3 className="card-title">设置管理</h3>
          <p className="card-subtitle">导入、导出和重置系统设置</p>
        </div>
        <div className="card-content">
          <div className="settings-actions">
            <Button
              variant="primary"
              leftIcon={<Download size={16} />}
              onClick={handleExportSettings}
              disabled={exporting}
            >
              {exporting ? '导出中...' : '导出设置'}
            </Button>
            
            <div className="file-input-wrapper">
              <Button
                variant="secondary"
                leftIcon={<Upload size={16} />}
                disabled={importing}
                onClick={() => document.getElementById('import-file')?.click()}
              >
                {importing ? '导入中...' : '导入设置'}
              </Button>
              <input
                id="import-file"
                type="file"
                accept=".json"
                style={{ display: 'none' }}
                onChange={handleImportSettings}
              />
            </div>
            
            <Button
              variant="warning"
              leftIcon={<RotateCcw size={16} />}
              onClick={handleResetSettings}
            >
              重置设置
            </Button>
          </div>
          
          <div className="mt-lg">
            <p className="text-sm text-secondary">
              <strong>注意：</strong>
              导入设置将覆盖当前所有配置，重置设置将恢复到系统默认值，这些操作无法撤销。
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DataManagement;
