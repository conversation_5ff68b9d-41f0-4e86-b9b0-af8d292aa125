/* 创建沙具模态框样式 */

.create-sand-tool-modal {
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  padding: 0;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 24px 16px;
  border-bottom: 1px solid var(--border-light);
}

.modal-title-section {
  flex: 1;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.modal-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-blue);
}

.modal-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--primary-blue);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.required {
  color: var(--error);
}

.form-input,
.form-select,
.form-textarea {
  padding: 10px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 14px;
  background-color: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--error);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-hint {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-top: 4px;
}

.error-message {
  font-size: 12px;
  color: var(--error);
  margin-top: 4px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 24px;
  border-top: 1px solid var(--border-light);
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .modal-title {
    font-size: 20px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .modal-content {
    padding: 16px;
  }
  
  .modal-footer {
    padding: 12px 16px 16px;
    flex-direction: column;
  }
  
  .form-section {
    margin-bottom: 24px;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 16px;
  }
  
  .form-field {
    gap: 4px;
  }
  
  .checkbox-group {
    gap: 8px;
  }
}

/* 图片上传区域 */
.image-upload-area {
  margin-bottom: 20px;
}

.hidden-input {
  display: none;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-light);
  border-radius: 12px;
  padding: 40px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--bg-secondary);
}

.upload-placeholder:hover {
  border-color: var(--primary-blue);
  background-color: rgba(59, 130, 246, 0.05);
}

.upload-icon {
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.upload-placeholder h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.upload-placeholder p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 4px 0;
}

.upload-tips {
  font-size: 12px !important;
  color: var(--text-tertiary) !important;
}

.image-preview {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-light);
}

.preview-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.image-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
}

.change-image-btn,
.remove-image-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-weight: 500;
}

.change-image-btn {
  background-color: var(--primary-blue);
  color: white;
}

.change-image-btn:hover {
  background-color: #2563eb;
}

.remove-image-btn {
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
}

.remove-image-btn:hover {
  background-color: #dc2626;
}

/* 子类别建议标签 */
.subcategory-suggestions {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.subcategory-suggestions span {
  font-size: 12px;
  color: var(--text-secondary);
}

.suggestion-tag {
  padding: 4px 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: 4px;
  font-size: 12px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-tag:hover {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

/* 标签输入控件样式 */
.tag-input-container {
  margin-bottom: 8px;
}

.tag-input-row {
  display: flex;
  gap: 8px;
  align-items: flex-end;
  margin-bottom: 12px;
}

.tag-input-field {
  flex: 1;
  min-width: 0;
}

.tag-add-button {
  flex-shrink: 0;
  height: 40px; /* 与输入框高度一致 */
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background-color: #e0e7ff;
  color: #3730a3;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  border: 1px solid #c7d2fe;
}

.tag-remove-btn {
  background: transparent;
  border: none;
  color: #3730a3;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  font-weight: bold;
  margin-left: 2px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.tag-remove-btn:hover {
  background-color: rgba(55, 48, 163, 0.2);
  color: #1e1b4b;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .tag-input-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .tag-add-button {
    width: 100%;
    height: 36px;
  }
}
