import React, { useState, useMemo, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Package, 
  Eye, 
  Edit,
  Trash2,
  CheckSquare,
  Square,
  Grid3X3,
  List
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  PageHeader, 
  Card, 
  CardContent, 
  Button, 
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
  Badge,
  Input,
  Select,
  EmptyState,
  Pagination
} from '../ui/index';
import { SandToolDetailModal } from './SandToolDetailModal';
import CreateSandToolModal from './CreateSandToolModal';
import EditSandToolModal from './EditSandToolModal';
import { MarketingModal } from './MarketingModal';
import { 
  mockSandTools, 
  sandToolCategories, 
  getStatsData, 
  filterTools
} from '../../data/mockSandTools';
import type { SandTool, SandToolFilters } from '../../types/sandtool';
import './SandTools.css';

const SandTools: React.FC = () => {
  const [tools, setTools] = useState<SandTool[]>(mockSandTools);
  const [selectedTool, setSelectedTool] = useState<SandTool | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMarketingModal, setShowMarketingModal] = useState(false);
  const [selectedToolIds, setSelectedToolIds] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<SandToolFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table');

  // 页面加载时显示营销弹窗
  useEffect(() => {
    const hasSeenMarketing = sessionStorage.getItem('sandtools-marketing-seen');
    if (!hasSeenMarketing) {
      const timer = setTimeout(() => {
        setShowMarketingModal(true);
      }, 2000); // 延迟2秒显示
      return () => clearTimeout(timer);
    }
  }, []);

  const handleMarketingClose = () => {
    setShowMarketingModal(false);
    sessionStorage.setItem('sandtools-marketing-seen', 'true');
  };

  const stats = useMemo(() => getStatsData(), [tools]);
  
  const filteredTools = useMemo(() => {
    return filterTools(tools, { ...filters, search: searchTerm });
  }, [tools, filters, searchTerm]);

  // 分页计算
  const totalItems = filteredTools.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentPageTools = filteredTools.slice(startIndex, endIndex);

  React.useEffect(() => {
    const handleSandToolUsed = (event: CustomEvent<{ toolIds: string[]; date: string }>) => {
      const { toolIds, date } = event.detail;
      
      setTools(prevTools => 
        prevTools.map(tool => {
          if (toolIds.includes(tool.id)) {
            return {
              ...tool,
              lastUsed: date,
              usageCount: (tool.usageCount || 0) + 1
            };
          }
          return tool;
        })
      );
    };

    window.addEventListener('sandToolUsed', handleSandToolUsed as EventListener);
    
    return () => {
      window.removeEventListener('sandToolUsed', handleSandToolUsed as EventListener);
    };
  }, []);

  const handleViewDetail = (tool: SandTool) => {
    setSelectedTool(tool);
    setShowDetailModal(true);
  };

  const handleEdit = (tool: SandTool) => {
    setSelectedTool(tool);
    setShowEditModal(true);
  };

  // 删除沙具
  const handleDelete = (tool: SandTool) => {
    if (confirm(`确定要删除沙具"${tool.name}"吗？此操作不可恢复。`)) {
      setTools(prev => prev.filter(t => t.id !== tool.id));
      console.log('删除沙具:', tool);
    }
  };

  const handleAddTool = () => {
    setShowCreateModal(true);
  };

  // 选择相关处理函数
  const handleSelectTool = (toolId: string) => {
    setSelectedToolIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(toolId)) {
        newSet.delete(toolId);
      } else {
        newSet.add(toolId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    const currentPageIds = currentPageTools.map(t => t.id);
    const allCurrentPageSelected = currentPageIds.every(id => selectedToolIds.has(id));
    
    if (allCurrentPageSelected) {
      setSelectedToolIds(prev => {
        const newSet = new Set(prev);
        currentPageIds.forEach(id => newSet.delete(id));
        return newSet;
      });
    } else {
      setSelectedToolIds(prev => {
        const newSet = new Set(prev);
        currentPageIds.forEach(id => newSet.add(id));
        return newSet;
      });
    }
  };

  const getSelectedTools = () => {
    return tools.filter(t => selectedToolIds.has(t.id));
  };

  // 批量操作函数
  const handleBatchView = () => {
    const selected = getSelectedTools();
    if (selected.length === 1) {
      handleViewDetail(selected[0]);
    } else {
      alert(`已选择 ${selected.length} 个沙具，请选择单个沙具进行查看`);
    }
  };

  const handleBatchEdit = () => {
    const selected = getSelectedTools();
    if (selected.length === 1) {
      handleEdit(selected[0]);
    } else {
      alert(`已选择 ${selected.length} 个沙具，请选择单个沙具进行编辑`);
    }
  };

  const handleBatchDelete = () => {
    const selected = getSelectedTools();
    if (selected.length === 0) return;
    
    const names = selected.map(t => t.name).join('、');
    if (confirm(`确定要删除以下 ${selected.length} 个沙具吗？\n${names}\n\n此操作不可恢复。`)) {
      const deletedIds = new Set(selected.map(t => t.id));
      setTools(tools.filter(t => !deletedIds.has(t.id)));
      setSelectedToolIds(new Set());
      alert(`成功删除 ${selected.length} 个沙具`);
    }
  };

  // 获取库存状态标签
  const getStockBadge = (tool: SandTool) => {
    if (tool.available === 0) {
      return <Badge variant="danger">无库存</Badge>;
    } else if (tool.available <= tool.quantity * 0.2) {
      return <Badge variant="warning">库存不足</Badge>;
    } else if (tool.available >= tool.quantity * 0.8) {
      return <Badge variant="success">库存充足</Badge>;
    } else {
      return <Badge variant="gray">库存正常</Badge>;
    }
  };

  // 获取类别名称
  const getCategoryName = (category: string) => {
    const categoryInfo = sandToolCategories.find(cat => cat.id === category);
    return categoryInfo?.name || category;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  return (
    <PageContainer>
      <PageHeader
        title="沙具管理"
        subtitle="管理沙盘治疗工具库存和使用记录"
        actions={
          <div className="flex items-center gap-3">
            {/* 视图切换控件 */}
            <div className="view-toggle-control">
              <Button
                variant={viewMode === 'table' ? 'primary' : 'ghost'}
                size="sm"
                leftIcon={<List size={16} />}
                onClick={() => setViewMode('table')}
                className={`toggle-btn ${viewMode === 'table' ? 'active' : ''}`}
              >
                表格
              </Button>
              <Button
                variant={viewMode === 'card' ? 'primary' : 'ghost'}
                size="sm"
                leftIcon={<Grid3X3 size={16} />}
                onClick={() => setViewMode('card')}
                className={`toggle-btn ${viewMode === 'card' ? 'active' : ''}`}
              >
                卡片
              </Button>
            </div>
            <Button
              leftIcon={<Plus size={16} />}
              onClick={handleAddTool}
            >
              添加沙具
            </Button>
          </div>
        }
      />

      {/* 统计概览 */}
      <div className="stats-row mb-xl">
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.totalTools}</div>
              <div className="stat-label">沙具总数</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.availableTools}</div>
              <div className="stat-label">可用库存</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.lowStockTools}</div>
              <div className="stat-label">库存不足</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="stat-item">
              <div className="stat-number">{stats.needsMaintenanceTools}</div>
              <div className="stat-label">需要维护</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-xl">
        <CardContent>
          <div className="filter-section">
            <div className="filter-row">
              {/* 搜索框 */}
              <div className="search-field">
                <Input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="搜索沙具名称、类别或材质..."
                  leftIcon={<Search size={16} />}
                />
              </div>
              
              {/* 类别筛选 */}
              <div className="filter-field">
                <Select
                  value={filters.category || 'all'}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value as any }))}
                  options={[
                    { value: 'all', label: '全部类别' },
                    ...sandToolCategories.map(category => ({
                      value: category.id,
                      label: category.name
                    }))
                  ]}
                />
              </div>
              
              {/* 状况筛选 */}
              <div className="filter-field">
                <Select
                  value={filters.condition || 'all'}
                  onChange={(e) => setFilters(prev => ({ ...prev, condition: e.target.value as any }))}
                  options={[
                    { value: 'all', label: '全部状况' },
                    { value: '全新', label: '全新' },
                    { value: '良好', label: '良好' },
                    { value: '一般', label: '一般' },
                    { value: '损坏', label: '损坏' },
                    { value: '报废', label: '报废' }
                  ]}
                />
              </div>
              
              {/* 库存筛选 */}
              <div className="filter-field">
                <Select
                  value={filters.availability || 'all'}
                  onChange={(e) => setFilters(prev => ({ ...prev, availability: e.target.value as any }))}
                  options={[
                    { value: 'all', label: '全部库存' },
                    { value: 'available', label: '有库存' },
                    { value: 'unavailable', label: '无库存' },
                    { value: 'low-stock', label: '库存不足' }
                  ]}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作工具栏 */}
      {selectedToolIds.size > 0 && (
        <Card className="mb-lg">
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-md">
                <span className="text-sm text-gray-600">
                  已选择 {selectedToolIds.size} 个沙具
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedToolIds(new Set())}
                >
                  取消选择
                </Button>
              </div>
              <div className="flex gap-sm">
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<Eye size={14} />}
                  onClick={handleBatchView}
                  disabled={selectedToolIds.size !== 1}
                >
                  查看
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<Edit size={14} />}
                  onClick={handleBatchEdit}
                  disabled={selectedToolIds.size !== 1}
                >
                  编辑
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  leftIcon={<Trash2 size={14} />}
                  onClick={handleBatchDelete}
                  disabled={selectedToolIds.size === 0}
                >
                  删除 ({selectedToolIds.size})
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 沙具列表 */}
      <Card>
        <CardContent className="p-0">
          {currentPageTools.length === 0 ? (
            <EmptyState
              icon={<Package size={48} />}
              title="暂无沙具"
              description="没有找到符合条件的沙具，请调整筛选条件或添加新的沙具。"
              action={
                <Button
                  leftIcon={<Plus size={16} />}
                  onClick={handleAddTool}
                >
                  添加第一个沙具
                </Button>
              }
            />
          ) : (
            <>
              {/* 表格视图 */}
              {viewMode === 'table' && (
                <Table className="sandtools-table">
                  <TableHeader>
                    <TableRow>
                      <TableCell isHeader style={{ width: '50px' }}>
                        <button
                          onClick={handleSelectAll}
                          className="flex items-center justify-center w-5 h-5 text-gray-500 hover:text-gray-700"
                          title={selectedToolIds.size === currentPageTools.length ? '取消全选' : '全选'}
                        >
                          {selectedToolIds.size === currentPageTools.length && currentPageTools.length > 0 ? (
                            <CheckSquare size={16} />
                          ) : (
                            <Square size={16} />
                          )}
                        </button>
                      </TableCell>
                      <TableCell isHeader style={{ width: '80px' }}>缩略图</TableCell>
                      <TableCell isHeader>沙具名称</TableCell>
                      <TableCell isHeader>类别</TableCell>
                      <TableCell isHeader>规格</TableCell>
                      <TableCell isHeader>库存状态</TableCell>
                      <TableCell isHeader>最后使用</TableCell>
                      <TableCell isHeader>操作</TableCell>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentPageTools.map((tool) => (
                      <TableRow 
                        key={tool.id}
                        className={selectedToolIds.has(tool.id) ? 'selected' : ''}
                      >
                        <TableCell>
                          <button
                            onClick={() => handleSelectTool(tool.id)}
                            className="flex items-center justify-center w-5 h-5 text-gray-500 hover:text-gray-700"
                            title={selectedToolIds.has(tool.id) ? '取消选择' : '选择'}
                          >
                            {selectedToolIds.has(tool.id) ? (
                              <CheckSquare size={16} className="text-blue-600" />
                            ) : (
                              <Square size={16} />
                            )}
                          </button>
                        </TableCell>
                        <TableCell>
                          <div className="tool-thumbnail">
                            {tool.imageData ? (
                              <img 
                                src={tool.imageData} 
                                alt={tool.name}
                                className="thumbnail-image"
                              />
                            ) : (
                              <div className="thumbnail-placeholder">
                                <Package size={20} />
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="tool-name">
                            <div className="name">{tool.name}</div>
                            {tool.description && (
                              <div className="description text-sm text-gray-500">
                                {tool.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="category-info">
                            <div className="category">{getCategoryName(tool.category)}</div>
                            {tool.subcategory && (
                              <div className="subcategory text-sm text-gray-500">{tool.subcategory}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="specification">
                            <div>{tool.size}</div>
                            <div className="text-sm text-gray-500">{tool.material}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="stock-info">
                            <div className="stock-numbers">{tool.available}/{tool.quantity}</div>
                            <div className="mt-1">{getStockBadge(tool)}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="usage-info">
                            {tool.lastUsed ? (
                              <>
                                <div className="last-used">{tool.lastUsed}</div>
                                <div className="usage-count text-sm text-gray-500">使用 {tool.usageCount || 0} 次</div>
                              </>
                            ) : (
                              <div className="never-used text-sm text-gray-500">从未使用</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewDetail(tool)}
                              title="查看详情"
                            >
                              <Eye size={14} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(tool)}
                              title="编辑"
                            >
                              <Edit size={14} />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              {/* 卡片视图 */}
              {viewMode === 'card' && (
                <div className="sandtools-grid">
                  {currentPageTools.map((tool) => (
                    <div 
                      key={tool.id}
                      className={`tool-card ${selectedToolIds.has(tool.id) ? 'selected' : ''}`}
                    >
                      {/* 选择框 */}
                      <div className="card-select">
                        <button
                          onClick={() => handleSelectTool(tool.id)}
                          className="select-btn"
                          title={selectedToolIds.has(tool.id) ? '取消选择' : '选择'}
                        >
                          {selectedToolIds.has(tool.id) ? (
                            <CheckSquare size={16} className="text-blue-600" />
                          ) : (
                            <Square size={16} />
                          )}
                        </button>
                      </div>

                      {/* 图片区域 */}
                      <div className="card-image">
                        {tool.imageData ? (
                          <img 
                            src={tool.imageData} 
                            alt={tool.name}
                            className="tool-image"
                          />
                        ) : (
                          <div className="image-placeholder">
                            <Package size={48} />
                            <span>暂无图片</span>
                          </div>
                        )}
                      </div>

                      {/* 内容区域 */}
                      <div className="card-content">
                        <h3 className="tool-title">{tool.name}</h3>
                        
                        <div className="tool-info">
                          <div className="info-row">
                            <span className="info-label">类别:</span>
                            <span className="info-value">{getCategoryName(tool.category)}</span>
                          </div>
                          
                          <div className="info-row">
                            <span className="info-label">规格:</span>
                            <span className="info-value">{tool.size} · {tool.material}</span>
                          </div>
                          
                          <div className="info-row">
                            <span className="info-label">库存:</span>
                            <span className="info-value">
                              {tool.available}/{tool.quantity}
                              <span className="ml-2">{getStockBadge(tool)}</span>
                            </span>
                          </div>
                          
                          {tool.lastUsed && (
                            <div className="info-row">
                              <span className="info-label">最后使用:</span>
                              <span className="info-value">{tool.lastUsed}</span>
                            </div>
                          )}
                        </div>

                        {tool.description && (
                          <p className="tool-description">{tool.description}</p>
                        )}
                      </div>

                      {/* 操作按钮 */}
                      <div className="card-actions">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetail(tool)}
                          title="查看详情"
                        >
                          <Eye size={14} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(tool)}
                          title="编辑"
                        >
                          <Edit size={14} />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
          
          {/* 分页组件 */}
          {filteredTools.length > 0 && (
            <div className="mt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                total={filteredTools.length}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                showTotal={true}
                showSizeChanger={true}
                showQuickJumper={true}
                pageSizeOptions={[12, 24, 48, 96]}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* 详情模态框 */}
      {showDetailModal && selectedTool && (
        <SandToolDetailModal
          isOpen={showDetailModal}
          tool={selectedTool}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedTool(null);
          }}
          onEdit={(tool: SandTool) => {
            setShowDetailModal(false);
            setSelectedTool(null);
            handleEdit(tool);
          }}
          onDelete={(tool: SandTool) => {
            setShowDetailModal(false);
            setSelectedTool(null);
            handleDelete(tool);
          }}
        />
      )}

      {/* 创建模态框 */}
      {showCreateModal && (
        <CreateSandToolModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={(newTool: Omit<SandTool, 'id'>) => {
            const toolWithId: SandTool = {
              ...newTool,
              id: `tool-${Date.now()}`
            };
            setTools(prev => [...prev, toolWithId]);
            setShowCreateModal(false);
          }}
        />
      )}

      {/* 编辑模态框 */}
      {showEditModal && selectedTool && (
        <EditSandToolModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedTool(null);
          }}
          tool={selectedTool}
          onSubmit={(updatedTool: SandTool) => {
            setTools(prev => prev.map(t => t.id === updatedTool.id ? updatedTool : t));
            setShowEditModal(false);
            setSelectedTool(null);
          }}
        />
      )}

      {/* 营销弹窗 */}
      <MarketingModal
        isOpen={showMarketingModal}
        onClose={handleMarketingClose}
      />
    </PageContainer>
  );
};

export default SandTools;
