import React from 'react';
import { 
  BaseModal,
  Button,
  Badge
} from '../ui/index';
import { Phone, Mail, User, MapPin, GraduationCap, Briefcase } from 'lucide-react';
import type { Visitor } from '../../types/visitor';

interface ViewVisitorModalProps {
  isOpen: boolean;
  visitor: Visitor;
  onClose: () => void;
  onEdit: (visitor: Visitor) => void;
}

const ViewVisitorModal: React.FC<ViewVisitorModalProps> = ({ isOpen, visitor, onClose, onEdit }) => {
  const statusVariants = {
    '活跃': 'success',
    '暂停': 'warning',
    '完成': 'gray'
  } as const;

  const formatDate = (dateStr: string): string => {
    return new Date(dateStr).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="查看来访者详情"
      subtitle={`来访者ID: ${visitor.id}`}
      size="lg"
    >
      <div className="form-modal-body">
        {/* 基本信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <User size={16} />
            基本信息
          </h4>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">姓名</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {visitor.name}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">性别</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {visitor.gender}
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">年龄</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {visitor.age}岁
              </div>
            </div>
          </div>
          
          <div className="form-grid form-grid-3">
            <div className="form-group">
              <label className="form-label">状态</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                <Badge variant={statusVariants[visitor.status]}>
                  {visitor.status}
                </Badge>
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">创建时间</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {formatDate(visitor.createdAt)}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">更新时间</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb' }}>
                {formatDate(visitor.updatedAt)}
              </div>
            </div>
          </div>
        </div>

        {/* 联系信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <Phone size={16} />
            联系信息
          </h4>
          
          <div className="form-grid form-grid-2">
            <div className="form-group">
              <label className="form-label">手机号码</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Phone size={16} />
                {visitor.phone}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">邮箱</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                {visitor.email ? (
                  <>
                    <Mail size={16} />
                    {visitor.email}
                  </>
                ) : '未提供'}
              </div>
            </div>
          </div>
          
          <div className="form-grid form-grid-2">
            <div className="form-group">
              <label className="form-label">紧急联系人</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <User size={16} />
                {visitor.emergencyContact}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">紧急联系电话</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Phone size={16} />
                {visitor.emergencyPhone}
              </div>
            </div>
          </div>
        </div>

        {/* 其他信息 */}
        <div className="form-section">
          <h4 className="form-section-title">
            <Briefcase size={16} />
            其他信息
          </h4>
          
          <div className="form-grid form-grid-2">
            <div className="form-group">
              <label className="form-label">职业</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                {visitor.occupation ? (
                  <>
                    <Briefcase size={16} />
                    {visitor.occupation}
                  </>
                ) : '未提供'}
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">学历</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                {visitor.education ? (
                  <>
                    <GraduationCap size={16} />
                    {visitor.education}
                  </>
                ) : '未提供'}
              </div>
            </div>
          </div>
          
          <div className="form-grid form-grid-1">
            <div className="form-group">
              <label className="form-label">地址</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', gap: '8px' }}>
                {visitor.address ? (
                  <>
                    <MapPin size={16} />
                    {visitor.address}
                  </>
                ) : '未提供'}
              </div>
            </div>
          </div>
          
          <div className="form-grid form-grid-1">
            <div className="form-group">
              <label className="form-label">备注</label>
              <div className="form-input" style={{ backgroundColor: '#f9fafb', border: '1px solid #e5e7eb', minHeight: '80px', alignItems: 'flex-start', padding: '12px' }}>
                {visitor.notes || '无备注'}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="form-modal-footer">
        <div className="form-modal-actions">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            关闭
          </Button>
          <Button
            onClick={() => onEdit(visitor)}
          >
            编辑信息
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export { ViewVisitorModal };
export default ViewVisitorModal;