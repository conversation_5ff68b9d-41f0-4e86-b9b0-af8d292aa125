import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { Button } from '../ui/Button';
import { Badge } from '../ui/DataDisplay';
import { 
  Users, 
  Calendar, 
  Activity,
  Edit,
  Trash2
} from 'lucide-react';
import type { GroupSession } from '../../types/groupSession';
import './GroupSessionDetailModal.css';

interface GroupSessionDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  session: GroupSession | null;
  onEdit?: (session: GroupSession) => void;
  onDelete?: (session: GroupSession) => void;
}

export const GroupSessionDetailModal: React.FC<GroupSessionDetailModalProps> = ({
  isOpen,
  onClose,
  session,
  onEdit,
  onDelete
}) => {
  if (!session) return null;

  const handleEdit = () => {
    if (onEdit) {
      onEdit(session);
    }
    onClose();
  };

  const handleDelete = () => {
    if (onDelete && window.confirm(`确定要删除团体活动"${session.title}"吗？此操作不可恢复。`)) {
      onDelete(session);
      onClose();
    }
  };

  // 状态颜色映射
  const getStatusColor = (status: string): 'primary' | 'success' | 'warning' | 'danger' | 'gray' => {
    switch (status) {
      case '计划中': return 'warning';
      case '进行中': return 'success';
      case '已完成': return 'gray';
      case '已取消': return 'danger';
      case '暂停': return 'warning';
      default: return 'gray';
    }
  };

  // 获取进度信息
  const getProgressInfo = () => {
    if (session.totalSessions && session.currentSession !== undefined) {
      const progress = (session.currentSession / session.totalSessions) * 100;
      return {
        text: `${session.currentSession}/${session.totalSessions} 次`,
        percentage: Math.round(progress)
      };
    }
    return {
      text: '进行中',
      percentage: 0
    };
  };

  const progressInfo = getProgressInfo();

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={session.title}
      subtitle={`${session.sessionType} - ${session.targetAge}`}
      size="lg"
    >
      <div className="session-detail-wrapper">
        {/* 状态栏 */}
        <div className="status-bar">
          <Badge variant={getStatusColor(session.status)}>
            {session.status}
          </Badge>
          <span className="therapist-name">治疗师：{session.therapistName}</span>
        </div>

        {/* 基本信息卡片组 */}
        <div className="info-cards-grid">
          {/* 时间信息 */}
          <div className="info-card">
            <div className="card-header">
              <Calendar size={16} />
              <span>时间安排</span>
            </div>
            <div className="card-content">
              <div className="info-row">
                <span className="label">开始日期</span>
                <span className="value">{session.startDate}</span>
              </div>
              {session.endDate && (
                <div className="info-row">
                  <span className="label">结束日期</span>
                  <span className="value">{session.endDate}</span>
                </div>
              )}
              <div className="info-row">
                <span className="label">活动时间</span>
                <span className="value">{session.meetingTime}</span>
              </div>
              <div className="info-row">
                <span className="label">时长</span>
                <span className="value">{session.duration} 分钟</span>
              </div>
            </div>
          </div>

          {/* 参与信息 */}
          <div className="info-card">
            <div className="card-header">
              <Users size={16} />
              <span>参与情况</span>
            </div>
            <div className="card-content">
              <div className="info-row">
                <span className="label">当前人数</span>
                <span className="value">{session.currentParticipants} 人</span>
              </div>
              <div className="info-row">
                <span className="label">最大容量</span>
                <span className="value">{session.maxParticipants} 人</span>
              </div>
              <div className="info-row">
                <span className="label">活动频率</span>
                <span className="value">{session.frequency}</span>
              </div>
              <div className="info-row">
                <span className="label">活动地点</span>
                <span className="value">{session.location}</span>
              </div>
            </div>
          </div>

          {/* 进度信息 */}
          {session.totalSessions && (
            <div className="info-card">
              <div className="card-header">
                <Activity size={16} />
                <span>进度信息</span>
              </div>
              <div className="card-content">
                <div className="info-row">
                  <span className="label">总次数</span>
                  <span className="value">{session.totalSessions} 次</span>
                </div>
                <div className="info-row">
                  <span className="label">当前进度</span>
                  <span className="value">{progressInfo.text}</span>
                </div>
                <div className="info-row">
                  <span className="label">完成百分比</span>
                  <span className="value">{progressInfo.percentage}%</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 详细信息区域 */}
        <div className="detail-sections">
          {/* 描述 */}
          {session.description && (
            <div className="detail-section">
              <div className="section-title">团体描述</div>
              <div className="section-content">
                {session.description}
              </div>
            </div>
          )}

          {/* 参与要求和材料 */}
          <div className="detail-row">
            {session.requirements && (
              <div className="detail-section half-width">
                <div className="section-title">参与要求</div>
                <div className="section-content">
                  {session.requirements}
                </div>
              </div>
            )}

            {session.materials && session.materials.length > 0 && (
              <div className="detail-section half-width">
                <div className="section-title">所需材料</div>
                <div className="materials-tags">
                  {session.materials.map((material, index) => (
                    <span key={index} className="material-tag">
                      {material}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 参与者列表 */}
          {session.participants && session.participants.length > 0 && (
            <div className="detail-section">
              <div className="section-title">
                <Users size={16} />
                参与者列表 ({session.participants.length}人)
              </div>
              <div className="participants-grid">
                {session.participants.map((participant) => (
                  <div key={participant.id} className="participant-card">
                    <div className="participant-info">
                      <div className="participant-name">{participant.visitorName}</div>
                      <div className="participant-meta">
                        {participant.age}岁 · {participant.gender}
                      </div>
                    </div>
                    <Badge variant={participant.status === '已确认' ? 'success' : 'warning'}>
                      {participant.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 备注 */}
          {session.notes && (
            <div className="detail-section">
              <div className="section-title">备注</div>
              <div className="section-content notes">
                {session.notes}
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="action-buttons">
          <Button variant="secondary" onClick={onClose}>
            关闭
          </Button>
          <div className="button-group">
            {onEdit && (
              <Button
                variant="primary"
                leftIcon={<Edit size={14} />}
                onClick={handleEdit}
              >
                编辑
              </Button>
            )}
            {onDelete && (
              <Button
                variant="danger"
                leftIcon={<Trash2 size={14} />}
                onClick={handleDelete}
              >
                删除
              </Button>
            )}
          </div>
        </div>
      </div>
    </BaseModal>
  );
};
