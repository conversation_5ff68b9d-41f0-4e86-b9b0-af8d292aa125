import React from 'react';
import {
  Card,
  CardContent,
  PageContainer,
  PageHeader
} from '../ui';
import { FileX } from 'lucide-react';
import './Cases.css';

const CasesUnified: React.FC = () => {

  return (
    <PageContainer>
      <PageHeader
        title="个案管理"
        subtitle="个案管理功能暂时不可用"
      />
      
      <Card>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <FileX size={64} className="text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              功能暂时不可用
            </h3>
            <p className="text-gray-600 mb-4">
              个案管理功能正在维护中，请稍后再试。
            </p>
            <p className="text-sm text-gray-500">
              如有紧急需求，请联系系统管理员。
            </p>
          </div>
        </CardContent>
      </Card>

    </PageContainer>
  );
};

export default CasesUnified;
