/* 创建团体模态框样式 */
.modal-container.create-modal {
  max-width: 600px;
  max-height: 90vh;
}

.modal-container.large {
  max-width: 900px;
}

.create-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xl);
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-5xl);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--primary-blue);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
}

.form-row.two-columns {
  grid-template-columns: 1fr 1fr;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.required-mark {
  color: var(--error-red);
  margin-left: var(--spacing-xs);
}

.form-control {
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:hover {
  border-color: var(--border-medium);
}

.form-control::placeholder {
  color: var(--text-tertiary);
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.form-textarea.large {
  min-height: 120px;
}

.error-message {
  font-size: var(--text-xs);
  color: var(--error-red);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.help-text {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin: 0;
}

/* 时间设置特殊样式 */
.time-settings {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  align-items: end;
}

.date-time-group {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--spacing-sm);
  align-items: end;
}

.time-input {
  width: 120px;
}

/* 数字输入特殊样式 */
.number-input {
  width: 100px;
}

/* 人数设置网格 */
.capacity-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.submit-button {
  background: var(--primary-blue);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.submit-button:hover {
  background: var(--primary-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.submit-button:active {
  transform: translateY(0);
}

.submit-button:disabled {
  background: var(--border-light);
  color: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancel-button {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.cancel-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

/* 表单验证状态 */
.form-control.error {
  border-color: var(--error-red);
  background: rgba(239, 68, 68, 0.05);
}

.form-control.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.success {
  border-color: var(--success-green);
}

.form-control.success:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 加载状态 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-container.create-modal,
  .modal-container.large {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .form-row.two-columns {
    grid-template-columns: 1fr;
  }
  
  .time-settings {
    grid-template-columns: 1fr;
  }
  
  .date-time-group {
    grid-template-columns: 1fr;
  }
  
  .capacity-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }
  
  .modal-actions button {
    width: 100%;
    justify-content: center;
  }
  
  .time-input,
  .number-input {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 0;
  }
  
  .tab-content {
    padding: var(--spacing-lg);
  }
  
  .form-section {
    gap: var(--spacing-lg);
  }
  
  .create-form {
    gap: var(--spacing-xl);
  }
}
