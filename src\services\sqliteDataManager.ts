// 纯SQLite数据管理器 - 专业版只使用SQLite
import type { SimpleCase } from '../types/case';
import type { Visitor } from '../types/visitor';
import type { GroupSession } from '../types/groupSession';
import type { SandTool } from '../types/sandtool';
import type { Appointment } from '../types/schedule';
import type { AppSettings } from '../types/settings';

class SQLiteDataManager {
  
  // 环境检查 - 只支持桌面环境
  isElectronEnvironment(): boolean {
    const isElectron = typeof window !== 'undefined' && window.electronAPI?.isElectron;
    if (!isElectron) {
      throw new Error('此应用只支持桌面环境运行，请使用桌面版本');
    }
    return true;
  }

  // 数据库查询封装
  private async query(sql: string, params: any[] = []): Promise<any[]> {
    this.isElectronEnvironment();
    try {
      return await window.electronAPI.dbQuery(sql, params);
    } catch (error) {
      console.error('数据库查询失败:', error);
      return []; // 返回空数组而不是抛出错误
    }
  }

  private async run(sql: string, params: any[] = []): Promise<any> {
    this.isElectronEnvironment();
    try {
      return await window.electronAPI.dbRun(sql, params);
    } catch (error) {
      console.error('数据库执行失败:', error);
      return { changes: 0, lastInsertRowid: null }; // 返回默认结果
    }
  }

  // ==================== 访客管理 ====================
  async getAllVisitors(): Promise<Visitor[]> {
    const rows = await this.query('SELECT * FROM visitors ORDER BY created_at DESC');
    return rows.map(this.mapRowToVisitor);
  }

  async getVisitor(id: string): Promise<Visitor | null> {
    const rows = await this.query('SELECT * FROM visitors WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapRowToVisitor(rows[0]) : null;
  }

  async saveVisitor(visitor: Visitor): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO visitors (
        id, name, age, gender, phone, email, emergency_contact, 
        emergency_phone, occupation, education, address, notes, 
        status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      visitor.id,
      visitor.name,
      visitor.age,
      visitor.gender,
      visitor.phone || '',
      visitor.email || '',
      visitor.emergencyContact || '',
      visitor.emergencyPhone || '',
      visitor.occupation || '',
      visitor.education || '',
      visitor.address || '',
      visitor.notes || '',
      visitor.status,
      visitor.createdAt,
      visitor.updatedAt
    ];

    await this.run(sql, params);
  }

  async deleteVisitor(id: string): Promise<void> {
    await this.run('DELETE FROM visitors WHERE id = ?', [id]);
  }

  // ==================== 个案管理 ====================
  async getAllCases(): Promise<SimpleCase[]> {
    const sql = `
      SELECT 
        c.*,
        v.name as visitor_name
      FROM cases c
      LEFT JOIN visitors v ON c.visitor_id = v.id
      ORDER BY c.created_at DESC
    `;
    const rows = await this.query(sql);
    return rows.map(this.mapRowToCase);
  }

  async getCase(id: string): Promise<SimpleCase | null> {
    const sql = `
      SELECT 
        c.*,
        v.name as visitor_name
      FROM cases c
      LEFT JOIN visitors v ON c.visitor_id = v.id
      WHERE c.id = ?
    `;
    const rows = await this.query(sql, [id]);
    return rows.length > 0 ? this.mapRowToCase(rows[0]) : null;
  }

  async saveCase(caseData: SimpleCase): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO cases (
        id, visitor_id, name, summary, therapy_method, selected_sand_tools,
        last_date, next_date, total, star, duration, crisis, homework,
        progress, keywords, supervision, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      caseData.id,
      caseData.visitorId,
      caseData.name,
      caseData.summary,
      caseData.therapyMethod,
      JSON.stringify(caseData.selectedSandTools || []),
      caseData.lastDate || null,
      caseData.nextDate || null,
      caseData.total,
      caseData.star ? 1 : 0,
      caseData.duration,
      caseData.crisis || null,
      caseData.homework || null,
      caseData.progress || null,
      JSON.stringify(caseData.keywords || []),
      caseData.supervision || null,
      caseData.createdAt,
      caseData.updatedAt
    ];

    await this.run(sql, params);
  }

  async deleteCase(id: string): Promise<void> {
    await this.run('DELETE FROM cases WHERE id = ?', [id]);
  }

  // ==================== 预约管理 ====================
  async getAllAppointments(): Promise<Appointment[]> {
    const sql = `
      SELECT 
        a.*,
        v.name as visitor_name,
        c.summary as case_summary
      FROM appointments a
      LEFT JOIN visitors v ON a.visitor_id = v.id
      LEFT JOIN cases c ON a.case_id = c.id
      ORDER BY a.date DESC, a.start_time DESC
    `;
    const rows = await this.query(sql);
    return rows.map(this.mapRowToAppointment);
  }

  async getAppointment(id: string): Promise<Appointment | null> {
    const sql = `
      SELECT 
        a.*,
        v.name as visitor_name,
        c.summary as case_summary
      FROM appointments a
      LEFT JOIN visitors v ON a.visitor_id = v.id
      LEFT JOIN cases c ON a.case_id = c.id
      WHERE a.id = ?
    `;
    const rows = await this.query(sql, [id]);
    return rows.length > 0 ? this.mapRowToAppointment(rows[0]) : null;
  }

  async saveAppointment(appointment: Appointment): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO appointments (
        id, visitor_id, case_id, visitor_name, visitor_phone, visitor_age, visitor_gender,
        date, start_time, end_time, duration, type, status, urgency, room, therapist_id,
        therapist_name, subject, description, notes, reminder_enabled, reminder_time,
        is_first_session, session_number, total_planned_sessions, fee, payment_status,
        created_at, updated_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      appointment.id,
      appointment.visitorId || null,
      appointment.caseId || null,
      appointment.visitorName,
      appointment.visitorPhone || null,
      appointment.visitorAge || null,
      appointment.visitorGender || null,
      appointment.date,
      appointment.startTime,
      appointment.endTime,
      appointment.duration,
      appointment.type,
      appointment.status,
      appointment.urgency,
      appointment.room,
      appointment.therapistId,
      appointment.therapistName,
      appointment.subject,
      appointment.description || null,
      appointment.notes || null,
      appointment.reminderEnabled ? 1 : 0,
      appointment.reminderTime,
      appointment.isFirstSession ? 1 : 0,
      appointment.sessionNumber || null,
      appointment.totalPlannedSessions || null,
      appointment.fee || null,
      appointment.paymentStatus || null,
      appointment.createdAt,
      appointment.updatedAt,
      appointment.createdBy
    ];

    await this.run(sql, params);
  }

  async deleteAppointment(id: string): Promise<void> {
    await this.run('DELETE FROM appointments WHERE id = ?', [id]);
  }

  // ==================== 团体会话管理 ====================
  async getAllGroupSessions(): Promise<GroupSession[]> {
    const rows = await this.query('SELECT * FROM group_sessions ORDER BY created_at DESC');
    return rows.map(this.mapRowToGroupSession);
  }

  async getGroupSession(id: string): Promise<GroupSession | null> {
    const rows = await this.query('SELECT * FROM group_sessions WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapRowToGroupSession(rows[0]) : null;
  }

  async saveGroupSession(session: GroupSession): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO group_sessions (
        id, title, description, therapist_id, therapist_name, max_participants,
        current_participants, participants, session_type, target_age, duration,
        frequency, total_sessions, current_session, start_date, end_date,
        meeting_time, location, status, requirements, materials,
        selected_sand_tools, notes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      session.id,
      session.title,
      session.description || null,
      session.therapistId,
      session.therapistName,
      session.maxParticipants,
      session.currentParticipants,
      JSON.stringify(session.participants || []),
      session.sessionType,
      session.targetAge,
      session.duration,
      session.frequency,
      session.totalSessions || null,
      session.currentSession || null,
      session.startDate,
      session.endDate || null,
      session.meetingTime,
      session.location,
      session.status,
      session.requirements || null,
      JSON.stringify(session.materials || []),
      JSON.stringify(session.selectedSandTools || []),
      session.notes || null,
      session.createdAt,
      session.updatedAt
    ];

    await this.run(sql, params);
  }

  async deleteGroupSession(id: string): Promise<void> {
    await this.run('DELETE FROM group_sessions WHERE id = ?', [id]);
  }

  // ==================== 设置管理 ====================
  async getSettings(): Promise<AppSettings | null> {
    const rows = await this.query('SELECT * FROM settings ORDER BY updated_at DESC LIMIT 1');
    if (rows.length === 0) return null;
    
    try {
      return JSON.parse(rows[0].data);
    } catch {
      return null;
    }
  }

  async saveSettings(settings: AppSettings): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO settings (id, data, updated_at)
      VALUES (1, ?, ?)
    `;
    
    const params = [
      JSON.stringify(settings),
      new Date().toISOString()
    ];

    await this.run(sql, params);
  }

  // ==================== 沙具管理 ====================
  async getAllSandTools(): Promise<SandTool[]> {
    const rows = await this.query('SELECT * FROM sand_tools ORDER BY name');
    return rows.map(this.mapRowToSandTool);
  }

  async getSandTool(id: string): Promise<SandTool | null> {
    const rows = await this.query('SELECT * FROM sand_tools WHERE id = ?', [id]);
    return rows.length > 0 ? this.mapRowToSandTool(rows[0]) : null;
  }

  async saveSandTool(tool: SandTool): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO sand_tools (
        id, name, category, subcategory, description, material, size, color,
        quantity, available, condition, location, image_url, notes, tags,
        usage_count, last_used, is_fragile, needs_care, replacement_needed
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      tool.id, tool.name, tool.category, tool.subcategory || null, tool.description || null,
      tool.material, tool.size, tool.color || null, tool.quantity, tool.available,
      tool.condition, tool.location, tool.imageUrl || null, tool.notes || null,
      JSON.stringify(tool.tags || []), tool.usageCount || 0, tool.lastUsed || null,
      tool.isFragile ? 1 : 0, tool.needsCare ? 1 : 0, tool.replacementNeeded ? 1 : 0
    ];

    await this.run(sql, params);
  }

  async deleteSandTool(id: string): Promise<void> {
    await this.run('DELETE FROM sand_tools WHERE id = ?', [id]);
  }

  // ==================== 数据映射方法 ====================
  private mapRowToVisitor(row: any): Visitor {
    return {
      id: row.id,
      name: row.name,
      age: row.age,
      gender: row.gender,
      phone: row.phone || '',
      email: row.email || '',
      emergencyContact: row.emergency_contact || '',
      emergencyPhone: row.emergency_phone || '',
      occupation: row.occupation || '',
      education: row.education || '',
      address: row.address || '',
      notes: row.notes || '',
      status: row.status,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private mapRowToCase(row: any): SimpleCase {
    return {
      id: row.id,
      visitorId: row.visitor_id,
      name: row.name,
      summary: row.summary,
      therapyMethod: row.therapy_method,
      selectedSandTools: row.selected_sand_tools ? JSON.parse(row.selected_sand_tools) : [],
      lastDate: row.last_date,
      nextDate: row.next_date,
      total: row.total,
      star: Boolean(row.star),
      duration: row.duration,
      crisis: row.crisis,
      homework: row.homework,
      progress: row.progress,
      keywords: row.keywords ? JSON.parse(row.keywords) : [],
      supervision: row.supervision,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private mapRowToGroupSession(row: any): GroupSession {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      therapistId: row.therapist_id,
      therapistName: row.therapist_name,
      maxParticipants: row.max_participants,
      currentParticipants: row.current_participants,
      participants: row.participants ? JSON.parse(row.participants) : [],
      sessionType: row.session_type,
      targetAge: row.target_age,
      duration: row.duration,
      frequency: row.frequency,
      totalSessions: row.total_sessions,
      currentSession: row.current_session,
      startDate: row.start_date,
      endDate: row.end_date,
      meetingTime: row.meeting_time,
      location: row.location,
      status: row.status,
      requirements: row.requirements,
      materials: row.materials ? JSON.parse(row.materials) : [],
      selectedSandTools: row.selected_sand_tools ? JSON.parse(row.selected_sand_tools) : [],
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  private mapRowToSandTool(row: any): SandTool {
    return {
      id: row.id,
      name: row.name,
      category: row.category,
      subcategory: row.subcategory,
      description: row.description,
      material: row.material,
      size: row.size,
      color: row.color,
      quantity: row.quantity || 1,
      available: row.available || 1,
      condition: row.condition,
      location: row.location,
      imageUrl: row.image_url,
      notes: row.notes,
      tags: row.tags ? JSON.parse(row.tags) : [],
      usageCount: row.usage_count || 0,
      lastUsed: row.last_used,
      isFragile: Boolean(row.is_fragile),
      needsCare: Boolean(row.needs_care),
      replacementNeeded: Boolean(row.replacement_needed)
    };
  }

  private mapRowToAppointment(row: any): Appointment {
    return {
      id: row.id,
      visitorId: row.visitor_id,
      caseId: row.case_id,
      visitorName: row.visitor_name,
      visitorPhone: row.visitor_phone,
      visitorAge: row.visitor_age,
      visitorGender: row.visitor_gender,
      date: row.date,
      startTime: row.start_time,
      endTime: row.end_time,
      duration: row.duration,
      type: row.type,
      status: row.status,
      urgency: row.urgency,
      room: row.room,
      therapistId: row.therapist_id,
      therapistName: row.therapist_name,
      subject: row.subject,
      description: row.description,
      notes: row.notes,
      reminderEnabled: Boolean(row.reminder_enabled),
      reminderTime: row.reminder_time,
      isFirstSession: Boolean(row.is_first_session),
      sessionNumber: row.session_number,
      totalPlannedSessions: row.total_planned_sessions,
      fee: row.fee,
      paymentStatus: row.payment_status,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdBy: row.created_by
    };
  }

  // ==================== 系统信息 ====================
  async getSystemInfo() {
    try {
      const version = await window.electronAPI.getAppVersion();
      const userDataPath = await window.electronAPI.getAppPath('userData');
      return {
        platform: 'Desktop',
        storageType: 'SQLite',
        databasePath: userDataPath + '/xlsp.db',
        isElectron: true,
        version: version
      };
    } catch (error) {
      console.error('获取系统信息失败:', error);
      return {
        platform: 'Desktop',
        storageType: 'SQLite',
        databasePath: 'xlsp.db',
        isElectron: true,
        version: '1.0.0'
      };
    }
  }

  // ==================== 数据导出 ====================
  async exportAllData() {
    const [visitors, cases, sessions, tools] = await Promise.all([
      this.getAllVisitors(),
      this.getAllCases(),
      this.getAllGroupSessions(),
      this.getAllSandTools()
    ]);

    return {
      exportDate: new Date().toISOString(),
      version: '1.0.0',
      data: {
        visitors,
        cases,
        sessions,
        tools
      }
    };
  }
}

// 导出单例
export const sqliteDataManager = new SQLiteDataManager();
