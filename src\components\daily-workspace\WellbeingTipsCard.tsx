import React, { useState, useEffect } from 'react';
import { Lightbulb, RefreshCw, BookOpen, Coffee, Sunrise, Moon } from 'lucide-react';

interface WellbeingTip {
  id: string;
  category: 'morning' | 'work' | 'break' | 'evening' | 'general';
  title: string;
  content: string;
  icon: string;
  actionable?: boolean;
  action?: string;
}

export const WellbeingTipsCard: React.FC = () => {
  const [currentTip, setCurrentTip] = useState<WellbeingTip | null>(null);
  const [tipHistory, setTipHistory] = useState<string[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());

  const wellbeingTips: WellbeingTip[] = [
    // 晨间建议
    {
      id: 'morning_1',
      category: 'morning',
      title: '晨间阳光',
      content: '早晨的阳光能帮助调节生物钟，提升一天的精神状态。试着在窗边站立几分钟，感受温暖的阳光。',
      icon: '☀️',
      actionable: true,
      action: '现在就去窗边站一会儿'
    },
    {
      id: 'morning_2',
      category: 'morning',
      title: '感恩开始',
      content: '每天早晨想三件值得感恩的事情，可以是昨天的美好回忆，也可以是今天的期待。',
      icon: '🙏',
      actionable: true,
      action: '写下三件感恩的事'
    },
    {
      id: 'morning_3',
      category: 'morning',
      title: '深呼吸启动',
      content: '用5分钟的深呼吸开始新的一天，让身心都准备好迎接挑战。',
      icon: '🌬️',
      actionable: true,
      action: '开始深呼吸练习'
    },
    
    // 工作建议
    {
      id: 'work_1',
      category: 'work',
      title: '番茄工作法',
      content: '专注工作25分钟，然后休息5分钟。这样的节奏能帮助保持高效和专注。',
      icon: '🍅',
      actionable: false
    },
    {
      id: 'work_2',
      category: 'work',
      title: '姿势调整',
      content: '每小时起身活动一下，调整坐姿，缓解肩颈压力。你的身体会感谢你的。',
      icon: '🧘‍♀️',
      actionable: true,
      action: '现在起身活动'
    },
    {
      id: 'work_3',
      category: 'work',
      title: '优先级管理',
      content: '列出今天最重要的3件事，专注完成它们。完成感会带来满足和动力。',
      icon: '📝',
      actionable: false
    },
    
    // 休息建议
    {
      id: 'break_1',
      category: 'break',
      title: '眼部放松',
      content: '看向远方20秒，让眼睛得到休息。或者闭眼深呼吸，缓解视觉疲劳。',
      icon: '👀',
      actionable: true,
      action: '开始眼部放松'
    },
    {
      id: 'break_2',
      category: 'break',
      title: '喝水提醒',
      content: '身体需要充足的水分来维持最佳状态。喝一杯温水，感受身体的感谢。',
      icon: '💧',
      actionable: true,
      action: '去喝一杯水'
    },
    {
      id: 'break_3',
      category: 'break',
      title: '微笑练习',
      content: '即使是假笑也能触发大脑释放快乐激素。给自己一个微笑吧！',
      icon: '😊',
      actionable: true,
      action: '对着镜子微笑'
    },
    
    // 晚间建议
    {
      id: 'evening_1',
      category: 'evening',
      title: '今日回顾',
      content: '回顾今天的收获和成长，哪怕是很小的进步也值得庆祝。',
      icon: '📖',
      actionable: false
    },
    {
      id: 'evening_2',
      category: 'evening',
      title: '放松准备',
      content: '睡前一小时远离屏幕，做些轻松的活动，帮助大脑准备休息。',
      icon: '🌙',
      actionable: false
    },
    {
      id: 'evening_3',
      category: 'evening',
      title: '明日期待',
      content: '想想明天有什么值得期待的事情，带着积极的心情入睡。',
      icon: '⭐',
      actionable: false
    },
    
    // 通用建议
    {
      id: 'general_1',
      category: 'general',
      title: '接纳情绪',
      content: '所有的情绪都是正常的。不要抗拒负面情绪，而是观察和接纳它们。',
      icon: '🤗',
      actionable: false
    },
    {
      id: 'general_2',
      category: 'general',
      title: '小步前进',
      content: '进步不需要很大，每天一小步就足够。持续的小改变会带来巨大的变化。',
      icon: '👣',
      actionable: false
    },
    {
      id: 'general_3',
      category: 'general',
      title: '自我关怀',
      content: '对自己温柔一些，就像对待好朋友一样。你值得被善待，包括被自己善待。',
      icon: '💝',
      actionable: false
    }
  ];

  // 更新当前时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // 每分钟更新一次

    return () => clearInterval(timer);
  }, []);

  // 根据时间选择合适的建议
  const getTimeBasedCategory = (): WellbeingTip['category'] => {
    const hour = currentTime.getHours();
    
    if (hour >= 6 && hour < 10) return 'morning';
    if (hour >= 10 && hour < 14) return 'work';
    if (hour >= 14 && hour < 18) return 'break';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'general';
  };

  // 获取随机建议
  const getRandomTip = (preferredCategory?: WellbeingTip['category']) => {
    let availableTips = wellbeingTips;
    
    // 优先显示时间相关的建议
    if (preferredCategory) {
      const categoryTips = wellbeingTips.filter(tip => tip.category === preferredCategory);
      if (categoryTips.length > 0) {
        availableTips = categoryTips;
      }
    }
    
    // 过滤掉最近显示过的建议
    const unshownTips = availableTips.filter(tip => !tipHistory.includes(tip.id));
    const tipsToChooseFrom = unshownTips.length > 0 ? unshownTips : availableTips;
    
    const randomIndex = Math.floor(Math.random() * tipsToChooseFrom.length);
    return tipsToChooseFrom[randomIndex];
  };

  // 初始化建议
  useEffect(() => {
    if (!currentTip) {
      const timeCategory = getTimeBasedCategory();
      const tip = getRandomTip(timeCategory);
      setCurrentTip(tip);
    }
  }, [currentTip]);

  // 刷新建议
  const refreshTip = () => {
    if (currentTip) {
      const newHistory = [...tipHistory, currentTip.id].slice(-5); // 只保留最近5个
      setTipHistory(newHistory);
    }
    
    const timeCategory = getTimeBasedCategory();
    const newTip = getRandomTip(timeCategory);
    setCurrentTip(newTip);
  };

  const getCategoryIcon = (category: WellbeingTip['category']) => {
    switch (category) {
      case 'morning': return <Sunrise size={14} />;
      case 'work': return <Coffee size={14} />;
      case 'break': return <RefreshCw size={14} />;
      case 'evening': return <Moon size={14} />;
      default: return <BookOpen size={14} />;
    }
  };

  const getCategoryLabel = (category: WellbeingTip['category']) => {
    switch (category) {
      case 'morning': return '晨间建议';
      case 'work': return '工作建议';
      case 'break': return '休息建议';
      case 'evening': return '晚间建议';
      default: return '日常建议';
    }
  };

  if (!currentTip) return null;

  return (
    <div className="workspace-card wellbeing-card">
      <div className="workspace-card-header">
        <div className="workspace-card-icon wellbeing-icon">
          <Lightbulb size={20} />
        </div>
        <h3 className="workspace-card-title">健康小贴士</h3>
        <button className="refresh-tip-btn" onClick={refreshTip}>
          <RefreshCw size={16} />
        </button>
      </div>
      
      <div className="workspace-card-content">
        <div className="tip-content">
          <div className="tip-header">
            <div className="tip-category">
              {getCategoryIcon(currentTip.category)}
              <span>{getCategoryLabel(currentTip.category)}</span>
            </div>
            <div className="tip-emoji">{currentTip.icon}</div>
          </div>
          
          <h4 className="tip-title">{currentTip.title}</h4>
          <p className="tip-text">{currentTip.content}</p>
          
          {currentTip.actionable && currentTip.action && (
            <button className="tip-action-btn">
              {currentTip.action}
            </button>
          )}
        </div>
        
        <div className="tip-footer">
          <span className="tip-hint">💡 点击刷新获取更多建议</span>
        </div>
      </div>
    </div>
  );
};