/* 新建个案模态框样式 */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--spacing-lg);
}

.modal-container {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.form-section {
  margin-bottom: 0;
}

.section-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-light);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 来访者选择组件样式 */
.visitor-selection {
  width: 100%;
}

.visitor-tabs {
  display: flex;
  margin-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: none;
  cursor: pointer;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: var(--primary);
  background-color: var(--bg-secondary);
}

.tab-btn.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
  background-color: var(--bg-secondary);
}

.visitor-selector {
  position: relative;
}

.selected-visitor {
  padding: var(--spacing-md);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  background: white;
  cursor: pointer;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.selected-visitor:hover {
  border-color: var(--primary);
}

.visitor-info {
  flex: 1;
}

.visitor-name {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 4px;
}

.visitor-details {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.no-selection {
  color: var(--text-muted);
  font-style: italic;
}

.visitor-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
}

.search-box {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.search-box input {
  flex: 1;
  border: none;
  outline: none;
  font-size: var(--text-sm);
}

.visitor-list {
  max-height: 200px;
  overflow-y: auto;
}

.visitor-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.2s;
}

.visitor-item:hover {
  background-color: var(--bg-secondary);
}

.visitor-item:last-child {
  border-bottom: none;
}

.visitor-item .visitor-name {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 4px;
}

.visitor-meta {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.no-visitors {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
}

.checkbox-row {
  margin-top: var(--spacing-md);
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-weight: var(--font-normal);
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: auto;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .modal-overlay {
    padding: var(--spacing-md);
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
  }
  
  .visitor-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    justify-content: center;
  }
}
