// 测试桌面应用数据持久化
console.log('=== 桌面应用数据持久化测试 ===');

// 检查是否为桌面环境
if (window.electronAPI) {
  console.log('✅ 检测到桌面环境');
  
  // 测试数据库基本操作
  async function testDatabaseOperations() {
    try {
      console.log('开始测试数据库操作...');
      
      // 测试访客数据
      const testVisitor = {
        name: '测试访客',
        age: 25,
        gender: '女',
        phone: '13800138000',
        email: '<EMAIL>',
        emergencyContact: '测试联系人',
        emergencyPhone: '13900139000',
        occupation: '软件工程师',
        education: '本科',
        address: '测试地址',
        notes: '这是一个测试访客',
        status: '活跃'
      };

      console.log('保存测试访客数据...');
      const visitorId = await window.electronAPI.saveVisitor(testVisitor);
      console.log('✅ 访客保存成功，ID:', visitorId);

      console.log('获取所有访客数据...');
      const visitors = await window.electronAPI.getAllVisitors();
      console.log('✅ 获取访客数据成功，数量:', visitors.length);
      console.log('访客数据:', visitors);

      // 测试个案数据
      const testCase = {
        visitorId: visitorId,
        name: testVisitor.name,
        summary: '测试个案摘要',
        therapyMethod: '箱庭疗法',
        selectedSandTools: ['tool1', 'tool2'],
        total: 1,
        star: false,
        duration: 50,
        crisis: '✅',
        homework: '📋',
        progress: '⬆️',
        keywords: ['测试', '个案'],
        supervision: '测试督导记录'
      };

      console.log('保存测试个案数据...');
      const caseId = await window.electronAPI.saveCase(testCase);
      console.log('✅ 个案保存成功，ID:', caseId);

      console.log('获取所有个案数据...');
      const cases = await window.electronAPI.getAllCases();
      console.log('✅ 获取个案数据成功，数量:', cases.length);
      console.log('个案数据:', cases);

      // 测试团体会话数据
      const testSession = {
        title: '测试团体会话',
        description: '这是一个测试团体会话',
        therapistId: 'therapist1',
        therapistName: '测试治疗师',
        maxParticipants: 10,
        currentParticipants: 5,
        participants: [],
        sessionType: '开放式团体',
        targetAge: '成人',
        duration: 90,
        frequency: '每周',
        totalSessions: 8,
        currentSession: 1,
        startDate: '2024-01-01',
        endDate: '2024-03-01',
        meetingTime: '14:00',
        location: '团体室A',
        status: '进行中',
        requirements: '无特殊要求',
        materials: ['沙盘', '沙具'],
        selectedSandTools: ['tool1', 'tool2'],
        notes: '测试备注'
      };

      console.log('保存测试团体会话数据...');
      const sessionId = await window.electronAPI.saveGroupSession(testSession);
      console.log('✅ 团体会话保存成功，ID:', sessionId);

      console.log('获取所有团体会话数据...');
      const sessions = await window.electronAPI.getAllGroupSessions();
      console.log('✅ 获取团体会话数据成功，数量:', sessions.length);
      console.log('团体会话数据:', sessions);

      console.log('=== 数据持久化测试完成 ===');
      console.log('✅ 所有测试通过！桌面应用数据持久化功能正常工作。');

    } catch (error) {
      console.error('❌ 数据库操作测试失败:', error);
    }
  }

  // 运行测试
  testDatabaseOperations();

} else {
  console.log('❌ 未检测到桌面环境，当前为浏览器环境');
  console.log('📝 在浏览器环境中，数据将保存到 localStorage');
  
  // 简单测试浏览器环境
  localStorage.setItem('test_data', JSON.stringify({ test: '浏览器测试数据' }));
  const browserData = localStorage.getItem('test_data');
  console.log('浏览器数据测试:', JSON.parse(browserData));
}
