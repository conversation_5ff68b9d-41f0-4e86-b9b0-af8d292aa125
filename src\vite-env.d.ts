/// <reference types="vite/client" />

// Electron API 类型定义
interface ElectronAPI {
  // 数据库操作
  dbQuery: (sql: string, params?: any[]) => Promise<any[]>;
  dbRun: (sql: string, params?: any[]) => Promise<{ changes: number; lastInsertRowid: number }>;
  
  // 应用信息
  getAppVersion: () => Promise<string>;
  getAppPath: (name: string) => Promise<string>;
  
  // 浏览器功能
  openExternal: (url: string) => Promise<void>;
  showItemInFolder: (path: string) => Promise<{ success: boolean; error?: string }>;
  
  // 系统通知
  showNotification: (title: string, options: {
    body?: string;
    icon?: string;
    tag?: string;
    requireInteraction?: boolean;
    actions?: Array<{ action: string; title: string }>;
  }) => Promise<{ success: boolean; error?: string }>;
  
  // 平台信息
  platform: string;
  isElectron: boolean;
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}