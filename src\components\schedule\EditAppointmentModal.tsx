import React, { useState } from 'react';
import { FormModal } from '../ui/FormModal';
import { Input, Select, Textarea } from '../ui';
import { Calendar, Clock, User, MapPin } from 'lucide-react';
import { mockRooms } from '../../data/mockSchedule';
import type { Appointment, AppointmentType, UrgencyLevel } from '../../types/schedule';

interface EditAppointmentModalProps {
  isOpen: boolean;
  appointment: Appointment;
  onClose: () => void;
  onUpdate: (updatedAppointment: Appointment) => void;
}

interface EditFormData {
  // 来访者信息
  visitorName: string;
  visitorPhone: string;
  visitorAge: string;
  visitorGender: '' | '男' | '女';
  
  // 时间信息
  date: string;
  startTime: string;
  duration: string;
  
  // 预约详情
  type: AppointmentType;
  urgency: UrgencyLevel;
  room: string;
  therapistName: string;
  subject: string;
  description: string;
  notes: string;
  
  // 提醒设置
  reminderEnabled: boolean;
  reminderTime: number;
  
  // 其他设置
  isFirstSession: boolean;
}

export const EditAppointmentModal: React.FC<EditAppointmentModalProps> = ({
  isOpen,
  appointment,
  onClose,
  onUpdate
}) => {
  const [formData, setFormData] = useState<EditFormData>({
    visitorName: appointment.visitorName,
    visitorPhone: appointment.visitorPhone || '',
    visitorAge: appointment.visitorAge?.toString() || '',
    visitorGender: appointment.visitorGender || '',
    date: appointment.date,
    startTime: appointment.startTime,
    duration: appointment.duration.toString(),
    type: appointment.type,
    urgency: appointment.urgency,
    room: appointment.room,
    therapistName: appointment.therapistName,
    subject: appointment.subject,
    description: appointment.description || '',
    notes: appointment.notes || '',
    reminderEnabled: appointment.reminderEnabled,
    reminderTime: appointment.reminderTime,
    isFirstSession: appointment.isFirstSession,
  });

  const [errors, setErrors] = useState<Partial<Record<keyof EditFormData, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof EditFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof EditFormData, string>> = {};

    if (!formData.visitorName.trim()) {
      newErrors.visitorName = '请输入来访者姓名';
    }

    if (!formData.date) {
      newErrors.date = '请选择预约日期';
    }

    if (!formData.startTime) {
      newErrors.startTime = '请选择开始时间';
    }

    if (!formData.duration || parseInt(formData.duration) < 15) {
      newErrors.duration = '请选择有效的预约时长';
    }

    if (!formData.type) {
      newErrors.type = '请选择预约类型';
    }

    if (!formData.room) {
      newErrors.room = '请选择咨询室';
    }

    if (!formData.therapistName.trim()) {
      newErrors.therapistName = '请输入咨询师姓名';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = '请输入咨询主题';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateEndTime = (): string => {
    const [hours, minutes] = formData.startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const endMinutes = startMinutes + parseInt(formData.duration);
    const endHours = Math.floor(endMinutes / 60);
    const remainingMinutes = endMinutes % 60;
    return `${endHours.toString().padStart(2, '0')}:${remainingMinutes.toString().padStart(2, '0')}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const endTime = calculateEndTime();

      const updatedAppointment: Appointment = {
        ...appointment,
        visitorName: formData.visitorName.trim(),
        visitorPhone: formData.visitorPhone.trim() || undefined,
        visitorAge: formData.visitorAge ? parseInt(formData.visitorAge) : undefined,
        visitorGender: formData.visitorGender || undefined,
        date: formData.date,
        startTime: formData.startTime,
        endTime,
        duration: parseInt(formData.duration),
        type: formData.type,
        urgency: formData.urgency,
        room: formData.room,
        therapistName: formData.therapistName.trim(),
        subject: formData.subject.trim(),
        description: formData.description.trim() || undefined,
        notes: formData.notes.trim() || undefined,
        reminderEnabled: formData.reminderEnabled,
        reminderTime: formData.reminderTime,
        isFirstSession: formData.isFirstSession,
        updatedAt: new Date().toISOString()
      };

      onUpdate(updatedAppointment);
    } catch (error) {
      console.error('更新预约失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title="编辑预约"
      subtitle={`${appointment.visitorName} - ${appointment.subject}`}
      size="xl"
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      submitText="保存修改"
      cancelText="取消"
    >
      {/* 来访者信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <User size={16} />
          来访者信息
        </h4>
        
        <div className="form-grid form-grid-2">
          <Input
            label="姓名"
            value={formData.visitorName}
            onChange={(e) => handleInputChange('visitorName', e.target.value)}
            placeholder="请输入来访者姓名"
            required
            error={errors.visitorName}
          />
          
          <Input
            label="手机号码"
            value={formData.visitorPhone}
            onChange={(e) => handleInputChange('visitorPhone', e.target.value)}
            placeholder="请输入手机号码"
          />
        </div>

        <div className="form-grid form-grid-2">
          <Input
            label="年龄"
            type="number"
            value={formData.visitorAge}
            onChange={(e) => handleInputChange('visitorAge', e.target.value)}
            placeholder="请输入年龄"
            min="1"
            max="120"
          />
          
          <Select
            label="性别"
            value={formData.visitorGender}
            onChange={(e) => handleInputChange('visitorGender', e.target.value)}
            options={[
              { value: '', label: '请选择性别' },
              { value: '男', label: '男' },
              { value: '女', label: '女' }
            ]}
          />
        </div>
      </div>

      {/* 预约信息 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Calendar size={16} />
          预约信息
        </h4>
        
        <div className="form-grid form-grid-3">
          <Input
            label="日期"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
            required
            error={errors.date}
          />
          
          <Input
            label="开始时间"
            type="time"
            value={formData.startTime}
            onChange={(e) => handleInputChange('startTime', e.target.value)}
            required
            error={errors.startTime}
          />
          
          <Select
            label="时长（分钟）"
            value={formData.duration}
            onChange={(e) => handleInputChange('duration', e.target.value)}
            required
            error={errors.duration}
            options={[
              { value: '30', label: '30分钟' },
              { value: '45', label: '45分钟' },
              { value: '60', label: '60分钟' },
              { value: '90', label: '90分钟' },
              { value: '120', label: '120分钟' }
            ]}
          />
        </div>

        <div className="form-grid form-grid-2">
          <Select
            label="预约类型"
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
            required
            error={errors.type}
            options={[
              { value: '', label: '请选择预约类型' },
              { value: '个体咨询', label: '个体咨询' },
              { value: '团体咨询', label: '团体咨询' },
              { value: '家庭咨询', label: '家庭咨询' },
              { value: '沙盘疗法', label: '沙盘疗法' },
              { value: '评估', label: '评估' },
              { value: '督导', label: '督导' },
              { value: '其他', label: '其他' }
            ]}
          />
          
          <Select
            label="紧急程度"
            value={formData.urgency}
            onChange={(e) => handleInputChange('urgency', e.target.value)}
            options={[
              { value: '普通', label: '普通' },
              { value: '紧急', label: '紧急' },
              { value: '危机干预', label: '危机干预' }
            ]}
          />
        </div>
      </div>

      {/* 咨询详情 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <MapPin size={16} />
          咨询详情
        </h4>
        
        <div className="form-grid form-grid-2">
          <Select
            label="咨询室"
            value={formData.room}
            onChange={(e) => handleInputChange('room', e.target.value)}
            required
            error={errors.room}
            options={[
              { value: '', label: '请选择咨询室' },
              ...mockRooms.map(room => ({
                value: room.name,
                label: `${room.name} (${room.type})`
              }))
            ]}
          />
          
          <Input
            label="咨询师姓名"
            value={formData.therapistName}
            onChange={(e) => handleInputChange('therapistName', e.target.value)}
            placeholder="请输入咨询师姓名"
            required
            error={errors.therapistName}
          />
        </div>

        <div className="form-grid form-grid-1">
          <Input
            label="主题"
            value={formData.subject}
            onChange={(e) => handleInputChange('subject', e.target.value)}
            placeholder="请输入咨询主题"
            required
            error={errors.subject}
          />
        </div>

        <div className="form-grid form-grid-1">
          <Textarea
            label="详细描述"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="请详细描述咨询的具体内容或问题"
            rows={3}
          />
        </div>

        <div className="form-grid form-grid-1">
          <Textarea
            label="备注"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="其他需要说明的信息"
            rows={3}
          />
        </div>
      </div>

      {/* 设置选项 */}
      <div className="form-section">
        <h4 className="form-section-title">
          <Clock size={16} />
          设置选项
        </h4>
        
        <div className="form-grid form-grid-1">
          <div className="form-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={formData.reminderEnabled}
                onChange={(e) => handleInputChange('reminderEnabled', e.target.checked)}
              />
              <span className="checkmark"></span>
              启用提醒
            </label>
          </div>
        </div>

        {formData.reminderEnabled && (
          <div className="form-grid form-grid-2">
            <Select
              label="提醒时间"
              value={formData.reminderTime.toString()}
              onChange={(e) => handleInputChange('reminderTime', parseInt(e.target.value))}
              options={[
                { value: '5', label: '5分钟前' },
                { value: '10', label: '10分钟前' },
                { value: '15', label: '15分钟前' },
                { value: '30', label: '30分钟前' },
                { value: '60', label: '1小时前' },
                { value: '120', label: '2小时前' },
                { value: '1440', label: '1天前' }
              ]}
            />
          </div>
        )}
        
        <div className="form-grid form-grid-1">
          <div className="form-group">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={formData.isFirstSession}
                onChange={(e) => handleInputChange('isFirstSession', e.target.checked)}
              />
              <span className="checkmark"></span>
              首次咨询
            </label>
          </div>
        </div>
      </div>
    </FormModal>
  );
};

export default EditAppointmentModal;
