# Copilot Instructions for 沙盘管理软件

<!-- Use this file to provide workspace-specific custom instructions to Copilot. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## 项目简介
这是一个沙盘管理软件，用于心理健康服务的沙盘疗法管理。

**⚠️ 重要：这是桌面端应用（Electron），不是Web应用**
- 数据存储：使用SQLite数据库，存储在用户数据目录
- 跨平台：支持Windows、macOS、Linux
- 本地存储：所有数据保存在本地，无需网络连接

## 技术栈
- **前端**: React 19 + TypeScript
- **桌面框架**: Electron (主要部署方式)
- **数据库**: SQLite3 (sqlite3库，桌面端)
- **构建工具**: Vite 7
- **样式**: CSS3 (模块化)
- **图标库**: Lucide React
- **数据持久化**: sqliteDataManager (直接SQLite连接)

## 主要功能模块
1. 来访者管理
2. 个案管理
3. 日程安排
4. 沙具管理
5. 团沙管理
6. 统计数据

## 编码规范
- 使用TypeScript严格模式
- 组件采用函数式组件 + hooks
- CSS使用模块化命名
- 界面设计要现代简洁，参考Material Design风格
- 响应式设计，支持移动端

## 数据持久化架构
- **环境检测**: 纯桌面端应用，无需环境检测
- **桌面端**: 使用sqlite3库直接连接SQLite数据库
- **数据位置**: 桌面端数据存储在 `%APPDATA%/沙盘管理系统/xlsp.db`
- **迁移支持**: 数据库表结构自动升级和向后兼容
- **IPC通信**: 主进程提供db-query和db-run处理器

## 重要提醒
⚠️ **开发时请注意**:
1. 数据操作必须通过sqliteDataManager统一接口
2. 桌面端环境检测依赖 `window.electronAPI?.isElectron`
3. 数据库字段映射要与数据类型定义严格一致
4. 新增字段时需要同时更新表结构和迁移逻辑
5. 主进程使用sqlite3库，IPC处理器提供异步数据库操作
6. 在代码有重大变动的情况下，这份文件需要实时更新
7. 统一代码风格

# Development Guidelines

## Philosophy

### Core Beliefs

- **Incremental progress over big bangs** - Small changes that compile and pass tests
- **Learning from existing code** - Study and plan before implementing
- **Pragmatic over dogmatic** - Adapt to project reality
- **Clear intent over clever code** - Be boring and obvious

### Simplicity Means

- Single responsibility per function/class
- Avoid premature abstractions
- No clever tricks - choose the boring solution
- If you need to explain it, it's too complex

## Process

### 1. Planning & Staging

Break complex work into 3-5 stages. Document in `IMPLEMENTATION_PLAN.md`:

\`\`\`markdown
## Stage N: [Name]
**Goal**: [Specific deliverable]
**Success Criteria**: [Testable outcomes]
**Tests**: [Specific test cases]
**Status**: [Not Started|In Progress|Complete]
\`\`\`
- Update status as you progress
- Remove file when all stages are done

### 2. Implementation Flow

1. **Understand** - Study existing patterns in codebase
2. **Test** - Write test first (red)
3. **Implement** - Minimal code to pass (green)
4. **Refactor** - Clean up with tests passing
5. **Commit** - With clear message linking to plan

### 3. When Stuck (After 3 Attempts)

**CRITICAL**: Maximum 3 attempts per issue, then STOP.

1. **Document what failed**:
   - What you tried
   - Specific error messages
   - Why you think it failed

2. **Research alternatives**:
   - Find 2-3 similar implementations
   - Note different approaches used

3. **Question fundamentals**:
   - Is this the right abstraction level?
   - Can this be split into smaller problems?
   - Is there a simpler approach entirely?

4. **Try different angle**:
   - Different library/framework feature?
   - Different architectural pattern?
   - Remove abstraction instead of adding?

## Technical Standards

### Architecture Principles

- **Composition over inheritance** - Use dependency injection
- **Interfaces over singletons** - Enable testing and flexibility
- **Explicit over implicit** - Clear data flow and dependencies
- **Test-driven when possible** - Never disable tests, fix them

### Code Quality

- **Every commit must**:
  - Compile successfully
  - Pass all existing tests
  - Include tests for new functionality
  - Follow project formatting/linting

- **Before committing**:
  - Run formatters/linters
  - Self-review changes
  - Ensure commit message explains "why"

### Error Handling

- Fail fast with descriptive messages
- Include context for debugging
- Handle errors at appropriate level
- Never silently swallow exceptions

## Decision Framework

When multiple valid approaches exist, choose based on:

1. **Testability** - Can I easily test this?
2. **Readability** - Will someone understand this in 6 months?
3. **Consistency** - Does this match project patterns?
4. **Simplicity** - Is this the simplest solution that works?
5. **Reversibility** - How hard to change later?

## Project Integration

### Learning the Codebase

- Find 3 similar features/components
- Identify common patterns and conventions
- Use same libraries/utilities when possible
- Follow existing test patterns

### Tooling

- Use project's existing build system
- Use project's test framework
- Use project's formatter/linter settings
- Don't introduce new tools without strong justification

## Quality Gates

### Definition of Done

- [ ] Tests written and passing
- [ ] Code follows project conventions
- [ ] No linter/formatter warnings
- [ ] Commit messages are clear
- [ ] Implementation matches plan
- [ ] No TODOs without issue numbers

### Test Guidelines

- Test behavior, not implementation
- One assertion per test when possible
- Clear test names describing scenario
- Use existing test utilities/helpers
- Tests should be deterministic

## Important Reminders

**NEVER**:
- Use `--no-verify` to bypass commit hooks
- Disable tests instead of fixing them
- Commit code that doesn't compile
- Make assumptions - verify with existing code

**ALWAYS**:
- Commit working code incrementally
- Update plan documentation as you go
- Learn from existing implementations
- Stop after 3 failed attempts and reassess
