import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { Button } from '../ui/Button';
import { Badge } from '../ui/DataDisplay';
import { 
  Package, 
  Tag, 
  MapPin, 
  Calendar, 
  Activity,
  AlertTriangle,
  Heart,
  Palette,
  Ruler,
  Edit,
  Trash2
} from 'lucide-react';
import type { SandTool } from '../../types/sandtool';
import './SandToolDetailModal.new.css';

interface SandToolDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  tool: SandTool | null;
  onEdit?: (tool: SandTool) => void;
  onDelete?: (tool: SandTool) => void;
}

export const SandToolDetailModal: React.FC<SandToolDetailModalProps> = ({
  isOpen,
  onClose,
  tool,
  onEdit,
  onDelete
}) => {
  if (!tool) return null;

  const handleEdit = () => {
    if (onEdit) {
      onEdit(tool);
    }
    onClose();
  };

  const handleDelete = () => {
    if (onDelete && window.confirm(`确定要删除沙具"${tool.name}"吗？此操作不可恢复。`)) {
      onDelete(tool);
      onClose();
    }
  };

  // 状况颜色映射
  const getConditionColor = (condition: string): 'primary' | 'success' | 'warning' | 'danger' | 'gray' => {
    switch (condition) {
      case '全新': return 'success';
      case '良好': return 'success';
      case '一般': return 'warning';
      case '损坏': return 'danger';
      case '报废': return 'danger';
      default: return 'gray';
    }
  };

  // 库存状态
  const getStockStatus = (): { text: string; color: 'primary' | 'success' | 'warning' | 'danger' | 'gray' } => {
    const percentage = (tool.available / tool.quantity) * 100;
    if (percentage === 0) return { text: '无库存', color: 'danger' };
    if (percentage <= 20) return { text: '库存不足', color: 'warning' };
    if (percentage <= 50) return { text: '库存偏低', color: 'warning' };
    return { text: '库存充足', color: 'success' };
  };

  const stockStatus = getStockStatus();

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={tool.name}
      subtitle={`${tool.category}${tool.subcategory ? ` - ${tool.subcategory}` : ''}`}
      size="lg"
    >
      <div className="sand-tool-detail-content">
        {/* 主要信息区域 */}
        <div className="detail-main-info">
          <div className="info-grid">
            <div className="info-section">
              <div className="section-header">
                <Package size={16} className="section-icon" />
                <h4 className="section-title">基本信息</h4>
              </div>
              <div className="info-items">
                <div className="info-item">
                  <span className="info-label">名称</span>
                  <span className="info-value">{tool.name}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">类别</span>
                  <span className="info-value">{tool.category}</span>
                </div>
                {tool.subcategory && (
                  <div className="info-item">
                    <span className="info-label">子类别</span>
                    <span className="info-value">{tool.subcategory}</span>
                  </div>
                )}
                <div className="info-item">
                  <span className="info-label">材质</span>
                  <span className="info-value">{tool.material}</span>
                </div>
              </div>
            </div>

            <div className="info-section">
              <div className="section-header">
                <Palette size={16} className="section-icon" />
                <h4 className="section-title">规格信息</h4>
              </div>
              <div className="info-items">
                <div className="info-item">
                  <span className="info-label">
                    <Ruler size={14} />
                    尺寸
                  </span>
                  <span className="info-value">{tool.size}</span>
                </div>
                {tool.color && (
                  <div className="info-item">
                    <span className="info-label">颜色</span>
                    <span className="info-value">{tool.color}</span>
                  </div>
                )}
                <div className="info-item">
                  <span className="info-label">状况</span>
                  <Badge variant={getConditionColor(tool.condition)}>
                    {tool.condition}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="info-section">
              <div className="section-header">
                <Activity size={16} className="section-icon" />
                <h4 className="section-title">库存信息</h4>
              </div>
              <div className="info-items">
                <div className="info-item">
                  <span className="info-label">总数量</span>
                  <span className="info-value">{tool.quantity} 件</span>
                </div>
                <div className="info-item">
                  <span className="info-label">可用数量</span>
                  <span className="info-value">{tool.available} 件</span>
                </div>
                <div className="info-item">
                  <span className="info-label">库存状态</span>
                  <Badge variant={stockStatus.color}>
                    {stockStatus.text}
                  </Badge>
                </div>
                <div className="info-item">
                  <span className="info-label">
                    <MapPin size={14} />
                    存放位置
                  </span>
                  <span className="info-value">{tool.location}</span>
                </div>
              </div>
            </div>

            {/* 使用记录 */}
            {(tool.usageCount || tool.lastUsed) && (
              <div className="info-section">
                <div className="section-header">
                  <Calendar size={16} className="section-icon" />
                  <h4 className="section-title">使用记录</h4>
                </div>
                <div className="info-items">
                  {tool.usageCount !== undefined && (
                    <div className="info-item">
                      <span className="info-label">累计使用次数</span>
                      <span className="info-value">{tool.usageCount} 次</span>
                    </div>
                  )}
                  {tool.lastUsed && (
                    <div className="info-item">
                      <span className="info-label">最后使用时间</span>
                      <span className="info-value">{tool.lastUsed}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 次要信息区域 */}
        <div className="detail-secondary-info">
          {/* 描述 */}
          {tool.description && (
            <div className="info-section full-width">
              <div className="section-header">
                <h4 className="section-title">描述</h4>
              </div>
              <div className="description-content">
                {tool.description}
              </div>
            </div>
          )}

          {/* 标签 */}
          {tool.tags && tool.tags.length > 0 && (
            <div className="info-section">
              <div className="section-header">
                <Tag size={16} className="section-icon" />
                <h4 className="section-title">标签</h4>
              </div>
              <div className="tags-list">
                {tool.tags.map((tag, index) => (
                  <Badge key={index} variant="gray">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* 特殊标记 */}
          {(tool.isFragile || tool.needsCare || tool.replacementNeeded) && (
            <div className="info-section">
              <div className="section-header">
                <AlertTriangle size={16} className="section-icon" />
                <h4 className="section-title">特殊标记</h4>
              </div>
              <div className="special-marks">
                {tool.isFragile && (
                  <Badge variant="warning">
                    <AlertTriangle size={12} />
                    易碎物品
                  </Badge>
                )}
                {tool.needsCare && (
                  <Badge variant="primary">
                    <Heart size={12} />
                    需要特殊保养
                  </Badge>
                )}
                {tool.replacementNeeded && (
                  <Badge variant="danger">
                    <AlertTriangle size={12} />
                    需要更换
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* 图片 */}
          {tool.imageUrl && (
            <div className="info-section">
              <div className="section-header">
                <h4 className="section-title">图片</h4>
              </div>
              <div className="tool-image-container">
                <img 
                  src={tool.imageUrl} 
                  alt={tool.name}
                  className="tool-image"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            </div>
          )}

          {/* 备注 */}
          {tool.notes && (
            <div className="info-section full-width">
              <div className="section-header">
                <h4 className="section-title">备注</h4>
              </div>
              <div className="notes-content">
                {tool.notes}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="modal-actions">
        <div className="actions-left">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            关闭
          </Button>
        </div>
        <div className="actions-right">
          {onEdit && (
            <Button
              variant="primary"
              leftIcon={<Edit size={14} />}
              onClick={handleEdit}
            >
              编辑
            </Button>
          )}
          {onDelete && (
            <Button
              variant="danger"
              leftIcon={<Trash2 size={14} />}
              onClick={handleDelete}
            >
              删除
            </Button>
          )}
        </div>
      </div>
    </BaseModal>
  );
};

export default SandToolDetailModal;
