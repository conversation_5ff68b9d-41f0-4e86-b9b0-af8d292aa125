# 开发期间浏览器权限使用指南

## 🚀 快速开始

已经为您开放了浏览器使用权限，您现在可以：

### 1. 访问浏览器测试页面
- 启动应用后，在左侧菜单找到 **"开发工具"** 区域
- 点击 **"浏览器测试"** 菜单项
- 进入测试页面开始使用

### 2. 主要功能

#### 🌐 外部链接打开
- 在系统默认浏览器中打开任意URL
- 快捷按钮：百度、GitHub
- URL格式自动验证

#### 🔍 搜索功能
- 百度搜索
- Google搜索
- 自动编码处理中文搜索

#### 📁 文件管理器集成 (仅桌面版)
- 在文件管理器中显示指定文件
- 支持文件和文件夹路径

#### 🧪 批量测试
- 一键测试多个常用网站
- 方便调试和验证功能

### 3. 使用方式

#### 菜单快捷键
- `Ctrl+B` (或 `Cmd+B`): 快速打开百度

#### 代码中使用
```typescript
import { browserService, openExternal } from '../../services/browserService';

// 打开外部链接
await openExternal('https://www.example.com');

// 快捷方法
await browserService.openBaidu();
await browserService.search('沙盘疗法');

// 在文件管理器中显示文件 (仅Electron)
await browserService.showItemInFolder('C:\\path\\to\\file.txt');
```

### 4. 安全说明

#### 开发环境安全设置
- `webSecurity: false` - 仅在开发环境关闭
- `allowRunningInsecureContent: true` - 允许混合内容
- `experimentalFeatures: true` - 启用实验性功能

#### 外部链接处理
- 所有外部链接都在系统默认浏览器中打开
- 不会在应用内创建新窗口
- 自动阻止非授权的窗口打开

### 5. 现在可以做什么

✅ **调试和测试**
- 快速访问在线文档
- 测试第三方API
- 验证外部资源

✅ **开发辅助**
- 查看GitHub仓库
- 搜索技术问题
- 访问开发工具网站

✅ **文件操作**
- 快速定位项目文件
- 打开配置文件位置
- 查看日志文件

### 6. 封版时需要清理的文件

```
📂 需要删除的文件:
├── src/components/dev/DevBrowserTest.tsx
├── src/components/dev/DevBrowserTest.css
├── src/services/browserService.ts
└── docs/BROWSER_PERMISSIONS_GUIDE.md (本文件)

📂 需要修改的文件:
├── electron/main.js (移除浏览器相关代码)
├── electron/preload.js (移除openExternal等API)
├── src/components/Layout.tsx (移除dev-browser页面)
├── src/components/Sidebar.tsx (移除开发工具菜单)
├── src/components/Sidebar.css (移除dev-tools样式)
├── src/components/Dashboard.tsx (移除页面类型)
└── src/services/electronDataManager.ts (移除浏览器API类型)
```

### 7. 快速清理脚本

封版时可以运行以下命令快速清理：

```bash
# 删除开发文件
rm -rf src/components/dev
rm src/services/browserService.ts
rm docs/BROWSER_PERMISSIONS_GUIDE.md

# 或在Windows PowerShell中:
Remove-Item -Recurse src\\components\\dev
Remove-Item src\\services\\browserService.ts
Remove-Item docs\\BROWSER_PERMISSIONS_GUIDE.md
```

### 8. 注意事项

⚠️ **重要提醒**
- 这是临时开发功能，正式发布前必须移除
- 开放的权限仅在开发环境生效
- 生产环境会自动恢复安全限制

🔒 **安全考虑**
- 外部链接都经过验证
- 文件路径受系统权限限制
- 恶意URL会被自动拦截

---

现在您可以愉快地使用浏览器功能进行开发了！🎉
