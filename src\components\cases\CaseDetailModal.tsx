import React, { useState, useEffect } from 'react';
import { BaseModal } from '../ui/BaseModal';
import { <PERSON><PERSON>, Badge } from '../ui';
import { 
  User, Phone, Briefcase, FileText, Calendar, Clock, Target, 
  Heart, AlertTriangle, BookOpen, Tag, MessageSquare, Star,
  Edit, Eye, ChevronRight, Info, Activity, TrendingUp, CheckCircle, 
  AlertCircle, XCircle, UserCheck, Home, Baby, GraduationCap
} from 'lucide-react';
import type { SimpleCase } from '../../types/case';
import { visitorService } from '../../services/visitorService';
import type { Visitor } from '../../types/visitor';
import './CaseDetailModal.css';

interface CaseDetailModalProps {
  isOpen: boolean;
  case: SimpleCase;
  onClose: () => void;
  onEdit: (caseData: SimpleCase) => void;
}

const CaseDetailModal: React.FC<CaseDetailModalProps> = ({ 
  isOpen, 
  case: caseData, 
  onClose, 
  onEdit 
}) => {
  const [visitor, setVisitor] = useState<Visitor | null>(null);
  const [loading, setLoading] = useState(true);
  
  const notes = getNotesForCase(caseData.id);
  const daysSinceLastSession = getDaysSinceLastSession(caseData.lastDate);

  useEffect(() => {
    if (isOpen && caseData.visitorId) {
      setLoading(true);
      visitorService.getVisitor(caseData.visitorId)
        .then(setVisitor)
        .catch(console.error)
        .finally(() => setLoading(false));
    }
  }, [isOpen, caseData.visitorId]);

  // 格式化日期
  const formatDate = (dateStr?: string): string => {
    if (!dateStr) return '未设置';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'short'
    });
  };

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    return timeStr.slice(0, 5);
  };

  // 获取治疗方法图标
  const getTherapyIcon = (method: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      '箱庭疗法': <Target size={16} className="therapy-icon" />,
      '认知行为疗法': <Brain size={16} className="therapy-icon" />,
      '精神动力学疗法': <Stethoscope size={16} className="therapy-icon" />,
      '人本主义疗法': <Heart size={16} className="therapy-icon" />,
      '家庭治疗': <Home size={16} className="therapy-icon" />,
      '团体治疗': <Users size={16} className="therapy-icon" />,
      '行为疗法': <Activity size={16} className="therapy-icon" />,
      '绘画治疗': <Palette size={16} className="therapy-icon" />,
      '音乐治疗': <Music size={16} className="therapy-icon" />,
      '综合疗法': <Zap size={16} className="therapy-icon" />
    };
    return iconMap[method] || <Target size={16} className="therapy-icon" />;
  };

  // 获取治疗方法描述
  const getTherapyDescription = (method: string): string => {
    const descriptions: Record<string, string> = {
      '箱庭疗法': '沙盘游戏治疗，通过沙具摆放表达内心世界，适用于各年龄层的心理创伤、情绪困扰等',
      '认知行为疗法': 'CBT，通过识别和改变不良思维与行为模式，广泛用于焦虑、抑郁等心理问题',
      '精神动力学疗法': '探索无意识冲突和早期经历对当前行为的影响，深入理解心理问题根源',
      '人本主义疗法': '以来访者为中心，相信人的自我实现潜能，营造温暖、真诚、共情的治疗环境',
      '家庭治疗': '系统性地改善家庭关系和沟通模式，解决家庭系统中的问题',
      '团体治疗': '在群体互动中获得支持和成长，适合人际交往、社交焦虑等问题',
      '行为疗法': '通过系统脱敏、暴露疗法等技术改变问题行为，特别适用于恐惧症、强迫症',
      '绘画治疗': '通过艺术创作表达和处理情感，适合难以言语表达的来访者',
      '音乐治疗': '运用音乐元素促进情感表达和心理康复，适合情绪障碍和发展问题',
      '综合疗法': '整合多种治疗技术，根据来访者具体情况制定个性化治疗方案'
    };
    return descriptions[method] || '暂无描述';
  };

  // 获取危机等级状态
  const getCrisisStatus = (crisis?: string) => {
    switch(crisis) {
      case '✅': 
        return { 
          text: '状态稳定', 
          variant: 'success' as const,
          icon: <CheckCircle size={14} />,
          description: '心理状态稳定，无自伤自杀风险，可按常规频率咨询'
        };
      case '⚠️': 
        return { 
          text: '需要关注', 
          variant: 'warning' as const,
          icon: <AlertCircle size={14} />,
          description: '存在一定心理压力，需要密切关注，可能需要增加咨询频率'
        };
      case '⚡': 
        return { 
          text: '高危状态', 
          variant: 'danger' as const,
          icon: <XCircle size={14} />,
          description: '存在自伤自杀风险，需要立即干预，考虑转介或住院治疗'
        };
      default: 
        return { 
          text: '未评估', 
          variant: 'gray' as const,
          icon: <AlertTriangle size={14} />,
          description: '尚未进行危机评估，建议在下次咨询中完成评估'
        };
    }
  };

  // 获取作业状态
  const getHomeworkStatus = (homework?: string) => {
    switch(homework) {
      case '📋': 
        return { 
          text: '进行中', 
          variant: 'warning' as const,
          icon: <Clock size={14} />,
          description: '治疗作业正在进行中，下次咨询时检查完成情况'
        };
      case '✅': 
        return { 
          text: '已完成', 
          variant: 'success' as const,
          icon: <CheckCircle size={14} />,
          description: '治疗作业已按要求完成，可以讨论效果和感受'
        };
      case '❌': 
        return { 
          text: '未完成', 
          variant: 'danger' as const,
          icon: <XCircle size={14} />,
          description: '治疗作业未能完成，需要了解原因并调整方案'
        };
      default: 
        return { 
          text: '无作业', 
          variant: 'gray' as const,
          icon: <FileText size={14} />,
          description: '本阶段暂无治疗作业安排'
        };
    }
  };

  // 获取进展状态
  const getProgressStatus = (progress?: string) => {
    switch(progress) {
      case '⬆️': 
        return { 
          text: '明显进步', 
          variant: 'success' as const,
          icon: <TrendingUp size={14} />,
          description: '症状明显改善，治疗目标达成良好，可考虑调整治疗计划'
        };
      case '➡️': 
        return { 
          text: '状态维持', 
          variant: 'warning' as const,
          icon: <Activity size={14} />,
          description: '症状保持稳定，需要继续当前治疗方案'
        };
      case '⬇️': 
        return { 
          text: '有所退步', 
          variant: 'danger' as const,
          icon: <AlertTriangle size={14} />,
          description: '症状有所加重，需要重新评估治疗方案'
        };
      default: 
        return { 
          text: '待评估', 
          variant: 'gray' as const,
          icon: <Shield size={14} />,
          description: '尚未进行进展评估，建议在下次咨询中完成'
        };
    }
  };

  // 获取年龄阶段描述
  const getAgeStageDescription = (age?: number): string => {
    if (!age) return '';
    if (age < 6) return '学龄前儿童';
    if (age < 12) return '学龄期儿童';
    if (age < 18) return '青少年';
    if (age < 25) return '青年早期';
    if (age < 35) return '青年期';
    if (age < 50) return '中年早期';
    if (age < 65) return '中年期';
    return '老年期';
  };

  if (loading) {
    return (
      <BaseModal
        isOpen={isOpen}
        onClose={onClose}
        title="个案详情"
        size="xl"
      >
        <div className="case-detail-loading">
          <div className="loading-spinner"></div>
          <p>正在加载个案信息...</p>
        </div>
      </BaseModal>
    );
  }

  const crisisStatus = getCrisisStatus(caseData.crisis);
  const homeworkStatus = getHomeworkStatus(caseData.homework);
  const progressStatus = getProgressStatus(caseData.progress);

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="个案详情"
      subtitle={`${caseData.name} | 第 ${caseData.total} 次咨询`}
      size="xl"
    >
      <div className="case-detail-content">
        
        {/* 个案状态概览 */}
        <div className="case-status-overview">
          <div className="status-cards">
            <div className="status-card crisis">
              <div className="status-icon">
                {crisisStatus.icon}
              </div>
              <div className="status-info">
                <h4>危机评估</h4>
                <Badge variant={crisisStatus.variant}>
                  {crisisStatus.text}
                </Badge>
              </div>
            </div>
            
            <div className="status-card progress">
              <div className="status-icon">
                {progressStatus.icon}
              </div>
              <div className="status-info">
                <h4>治疗进展</h4>
                <Badge variant={progressStatus.variant}>
                  {progressStatus.text}
                </Badge>
              </div>
            </div>
            
            <div className="status-card homework">
              <div className="status-icon">
                {homeworkStatus.icon}
              </div>
              <div className="status-info">
                <h4>作业状态</h4>
                <Badge variant={homeworkStatus.variant}>
                  {homeworkStatus.text}
                </Badge>
              </div>
            </div>

            {caseData.star && (
              <div className="status-card star">
                <div className="status-icon">
                  <Star size={14} fill="currentColor" />
                </div>
                <div className="status-info">
                  <h4>重点关注</h4>
                  <Badge variant="warning">
                    ⭐ 重点个案
                  </Badge>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 来访者基本信息 */}
        <div className="detail-section">
          <div className="section-header">
            <User size={18} />
            <h3>来访者基本信息</h3>
          </div>
          
          <div className="info-grid">
            <div className="info-item">
              <label>姓名</label>
              <div className="info-value">
                <UserCheck size={16} />
                {caseData.name}
              </div>
            </div>
            
            <div className="info-item">
              <label>性别</label>
              <div className="info-value">
                {visitor?.gender || '未填写'}
              </div>
            </div>
            
            <div className="info-item">
              <label>年龄</label>
              <div className="info-value">
                {visitor?.age ? (
                  <span>
                    <Baby size={16} />
                    {visitor.age}岁 ({getAgeStageDescription(visitor.age)})
                  </span>
                ) : '未填写'}
              </div>
            </div>
            
            <div className="info-item">
              <label>联系电话</label>
              <div className="info-value">
                <Phone size={16} />
                {visitor?.phone || '未填写'}
              </div>
            </div>
            
            <div className="info-item">
              <label>职业</label>
              <div className="info-value">
                <Briefcase size={16} />
                {visitor?.occupation || '未填写'}
              </div>
            </div>
            
            <div className="info-item">
              <label>教育程度</label>
              <div className="info-value">
                <GraduationCap size={16} />
                {visitor?.education || '未填写'}
              </div>
            </div>
          </div>
        </div>

        {/* 治疗信息 */}
        <div className="detail-section">
          <div className="section-header">
            <Target size={18} />
            <h3>治疗方案</h3>
          </div>
          
          <div className="therapy-info">
            <div className="therapy-method">
              <div className="method-header">
                {getTherapyIcon(caseData.therapyMethod)}
                <h4>{caseData.therapyMethod}</h4>
                <Badge variant="primary">
                  <Clock size={12} />
                  {caseData.duration}分钟/次
                </Badge>
              </div>
              <p className="method-description">
                {getTherapyDescription(caseData.therapyMethod)}
              </p>
            </div>
            
            <div className="problem-summary">
              <h5>
                <FileText size={16} />
                问题概要
              </h5>
              <div className="summary-content">
                {caseData.summary}
              </div>
            </div>
          </div>
        </div>

        {/* 咨询安排 */}
        <div className="detail-section">
          <div className="section-header">
            <Calendar size={18} />
            <h3>咨询安排</h3>
          </div>
          
          <div className="schedule-info">
            <div className="schedule-item">
              <div className="schedule-label">
                <Calendar size={16} />
                上次咨询
              </div>
              <div className="schedule-value">
                {formatDate(caseData.lastDate)}
                {daysSinceLastSession > 0 && (
                  <span className="days-ago">
                    ({daysSinceLastSession}天前)
                  </span>
                )}
              </div>
            </div>
            
            <div className="schedule-item">
              <div className="schedule-label">
                <Calendar size={16} />
                下次预约
              </div>
              <div className="schedule-value">
                {formatDate(caseData.nextDate)}
              </div>
            </div>
            
            <div className="schedule-item">
              <div className="schedule-label">
                <BookOpen size={16} />
                总计次数
              </div>
              <div className="schedule-value">
                第 {caseData.total} 次咨询
              </div>
            </div>
          </div>
        </div>

        {/* 临床评估详情 */}
        <div className="detail-section">
          <div className="section-header">
            <Heart size={18} />
            <h3>临床评估</h3>
          </div>
          
          <div className="assessment-grid">
            <div className="assessment-item">
              <div className="assessment-header">
                <Shield size={16} />
                <h5>危机评估</h5>
              </div>
              <div className="assessment-content">
                <Badge variant={crisisStatus.variant} className="assessment-badge">
                  {crisisStatus.icon}
                  {crisisStatus.text}
                </Badge>
                <p className="assessment-description">
                  {crisisStatus.description}
                </p>
              </div>
            </div>
            
            <div className="assessment-item">
              <div className="assessment-header">
                <TrendingUp size={16} />
                <h5>治疗进展</h5>
              </div>
              <div className="assessment-content">
                <Badge variant={progressStatus.variant} className="assessment-badge">
                  {progressStatus.icon}
                  {progressStatus.text}
                </Badge>
                <p className="assessment-description">
                  {progressStatus.description}
                </p>
              </div>
            </div>
            
            <div className="assessment-item">
              <div className="assessment-header">
                <BookOpen size={16} />
                <h5>作业完成</h5>
              </div>
              <div className="assessment-content">
                <Badge variant={homeworkStatus.variant} className="assessment-badge">
                  {homeworkStatus.icon}
                  {homeworkStatus.text}
                </Badge>
                <p className="assessment-description">
                  {homeworkStatus.description}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 临床关键词 */}
        {caseData.keywords && caseData.keywords.length > 0 && (
          <div className="detail-section">
            <div className="section-header">
              <Tag size={18} />
              <h3>临床关键词</h3>
            </div>
            
            <div className="keywords-display">
              {caseData.keywords.map((keyword, index) => (
                <span key={index} className="keyword-badge">
                  {keyword}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* 督导记录 */}
        {caseData.supervision && (
          <div className="detail-section">
            <div className="section-header">
              <MessageSquare size={18} />
              <h3>督导记录</h3>
            </div>
            
            <div className="supervision-content">
              {caseData.supervision}
            </div>
          </div>
        )}

        {/* 咨询历史记录 */}
        {notes && notes.length > 0 && (
          <div className="detail-section">
            <div className="section-header">
              <BookOpen size={18} />
              <h3>咨询历史记录</h3>
              <Badge variant="gray">共 {notes.length} 次记录</Badge>
            </div>
            
            <div className="notes-timeline">
              {notes.map((note, index) => (
                <div key={note.id} className="note-timeline-item">
                  <div className="note-timeline-marker">
                    <div className="timeline-dot"></div>
                    {index < notes.length - 1 && <div className="timeline-line"></div>}
                  </div>
                  
                  <div className="note-timeline-content">
                    <div className="note-header">
                      <div className="note-session-info">
                        <span className="session-number">
                          第{caseData.total - notes.length + index + 1}次
                        </span>
                        <span className="session-date">
                          {formatDate(note.date)}
                        </span>
                        <Badge variant="gray" className="session-time">
                          {formatTime(note.time)}
                        </Badge>
                      </div>
                      
                      <div className="note-meta">
                        <span className="note-mood">{note.mood}</span>
                        <Badge variant="primary" className="note-topic">
                          {note.topic}
                        </Badge>
                        <Badge variant="gray" className="note-duration">
                          {note.duration}分钟
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="note-content">
                      {note.note}
                    </div>
                    
                    {note.keyQuote && (
                      <div className="note-quote">
                        <ChevronRight size={14} />
                        <em>"{note.keyQuote}"</em>
                      </div>
                    )}
                    
                    {note.keywords && note.keywords.length > 0 && (
                      <div className="note-keywords">
                        {note.keywords.map((keyword, idx) => (
                          <span key={idx} className="note-keyword">
                            {keyword}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="case-detail-footer">
        <div className="footer-actions">
          <Button variant="secondary" onClick={onClose}>
            关闭
          </Button>
          <Button onClick={() => onEdit(caseData)}>
            编辑个案
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export default CaseDetailModal;
