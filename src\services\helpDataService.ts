// 帮助数据服务 - 管理搜索历史和最近查看记录

interface SearchHistory {
  id: string;
  query: string;
  timestamp: string;
}

interface RecentViewed {
  id: string;
  title: string;
  category: string;
  timestamp: string;
}

class HelpDataService {
  
  // 获取搜索历史
  async getSearchHistory(): Promise<SearchHistory[]> {
    try {
      // 暂时返回空数组，可以后续扩展到数据库存储
      return [];
    } catch (error) {
      console.error('获取搜索历史失败:', error);
      return [];
    }
  }

  // 添加搜索历史
  async addSearchHistory(query: string): Promise<void> {
    try {
      // 暂时不实际存储，可以后续扩展
      console.log('添加搜索历史:', query);
    } catch (error) {
      console.error('添加搜索历史失败:', error);
    }
  }

  // 获取最近查看
  async getRecentViewed(): Promise<RecentViewed[]> {
    try {
      // 暂时返回空数组，可以后续扩展到数据库存储
      return [];
    } catch (error) {
      console.error('获取最近查看失败:', error);
      return [];
    }
  }

  // 添加最近查看
  async addRecentViewed(title: string, category: string): Promise<void> {
    try {
      // 暂时不实际存储，可以后续扩展
      console.log('添加最近查看:', title, category);
    } catch (error) {
      console.error('添加最近查看失败:', error);
    }
  }

  // 清除搜索历史
  async clearSearchHistory(): Promise<void> {
    try {
      console.log('清除搜索历史');
    } catch (error) {
      console.error('清除搜索历史失败:', error);
    }
  }

  // 清除最近查看
  async clearRecentViewed(): Promise<void> {
    try {
      console.log('清除最近查看');
    } catch (error) {
      console.error('清除最近查看失败:', error);
    }
  }
}

// 导出单例
export const helpDataService = new HelpDataService();
