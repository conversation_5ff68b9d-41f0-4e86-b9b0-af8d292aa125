# 桌面应用构建指南

## 🔧 开发环境

### 环境要求
- Node.js 18+
- npm 9+
- Windows 10/11 (推荐用于Windows版本构建)

### 开发模式启动
```bash
# 方式1: 使用npm脚本
npm start

# 方式2: 使用批处理文件 (Windows)
.\start-app.bat

# 方式3: 手动启动
npm run electron-dev
```

## 📦 构建和打包

### 构建前端代码
```bash
npm run build
```

### 打包桌面应用
```bash
# 构建可分发版本
npm run dist

# 仅构建，不打包
npm run pack
```

### 输出文件位置
- **Windows**: `dist-electron/沙盘管理系统 Setup 1.0.0.exe`
- **构建目录**: `dist-electron/win-unpacked/`

## 🗄️ 数据存储

### SQLite数据库
- **Windows位置**: `%APPDATA%\沙盘管理系统\xlsp.db`
- **macOS位置**: `~/Library/Application Support/沙盘管理系统/xlsp.db`
- **Linux位置**: `~/.config/沙盘管理系统/xlsp.db`

### 数据备份
- 使用应用内的"文件 > 导出数据"功能
- 导出格式: JSON
- 可通过"文件 > 导入数据"恢复

## 🚀 部署说明

### Windows安装包
1. 运行 `npm run dist`
2. 在 `dist-electron/` 目录找到安装程序
3. 双击安装程序进行安装

### 用户数据迁移
- 数据库文件位于用户目录，卸载应用不会删除数据
- 升级版本时数据会自动保留

## 🔍 故障排除

### 常见问题
1. **应用无法启动**
   - 检查Node.js版本是否为18+
   - 运行 `npm install` 重新安装依赖

2. **数据库错误**
   - 检查用户目录是否有写入权限
   - 删除现有数据库文件让应用重新创建

3. **构建失败**
   - 清理缓存: `npm run clean`
   - 重新安装依赖: `rm -rf node_modules && npm install`

### 开发者工具
- 开发模式下按 `F12` 打开开发者工具
- 生产模式下通过菜单 "视图 > 开发者工具" 打开

## 📋 版本发布清单

- [ ] 更新 `package.json` 中的版本号
- [ ] 运行完整测试套件
- [ ] 构建所有平台版本
- [ ] 测试安装程序
- [ ] 准备发布说明
- [ ] 上传到发布平台

## 🔐 代码签名 (可选)

为了避免Windows Defender警告，建议对应用进行代码签名：

1. 获取代码签名证书
2. 在 `package.json` 的 `build.win` 中添加签名配置
3. 重新构建应用

```json
"win": {
  "certificateFile": "path/to/certificate.p12",
  "certificatePassword": "password",
  "sign": "path/to/signtool.exe"
}
```
