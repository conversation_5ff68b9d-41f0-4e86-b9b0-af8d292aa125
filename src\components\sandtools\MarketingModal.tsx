import React from 'react';
import { BaseModal } from '../ui/BaseModal';
import { Button } from '../ui';
import { Package, Phone } from 'lucide-react';
import './MarketingModal.css';

interface MarketingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MarketingModal: React.FC<MarketingModalProps> = ({
  isOpen,
  onClose
}) => {
  const handleContactService = () => {
    window.open('https://work.weixin.qq.com/kfid/kfcd145b2cfdee55d77', '_blank');
  };

  return (
    <BaseModal 
      isOpen={isOpen} 
      onClose={onClose}
      size="sm"
    >
      <div className="marketing-modal">
        <div className="marketing-header">
          <Package size={32} />
          <h3>沙具购买咨询</h3>
        </div>

        <div className="marketing-content">
          <p className="marketing-text">
            如果您需要<strong>购置新沙具</strong>或<strong>补充库存</strong>，
            我们提供全套专业沙盘治疗工具，品质可靠，种类齐全。
          </p>
          
          <div className="marketing-buttons">
            <Button
              onClick={handleContactService}
              leftIcon={<Phone size={16} />}
              className="contact-btn"
            >
              联系客服购买
            </Button>
            <Button
              variant="secondary"
              onClick={onClose}
            >
              暂不需要
            </Button>
          </div>
        </div>
      </div>
    </BaseModal>
  );
};
