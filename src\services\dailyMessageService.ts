export interface DailyMessage {
  id?: number;
  type: 'quote' | 'selfcare';
  content: string;
  author?: string;
  locale: string;
  weight: number;
  createdAt: string;
  updatedAt: string;
}

export interface DailyMessageOverride {
  date: string; // YYYY-MM-DD
  messageId: number;
}

export class DailyMessageService {
  
  // 检查环境
  private isElectronEnvironment(): boolean {
    return typeof window !== 'undefined' && !!window.electronAPI?.isElectron;
  }

  // 数据库查询封装
  private async query(sql: string, params: any[] = []): Promise<any[]> {
    if (!this.isElectronEnvironment()) {
      throw new Error('此功能只在桌面环境下可用');
    }
    try {
      return await window.electronAPI!.dbQuery(sql, params);
    } catch (error) {
      console.error('数据库查询失败:', error);
      return [];
    }
  }

  private async run(sql: string, params: any[] = []): Promise<any> {
    if (!this.isElectronEnvironment()) {
      throw new Error('此功能只在桌面环境下可用');
    }
    try {
      return await window.electronAPI!.dbRun(sql, params);
    } catch (error) {
      console.error('数据库执行失败:', error);
      return { changes: 0, lastInsertRowid: null };
    }
  }

  // 获取所有每日心语
  async getAllMessages(): Promise<DailyMessage[]> {
    try {
      const rows = await this.query('SELECT * FROM daily_messages ORDER BY id');
      return rows.map(this.mapRowToMessage);
    } catch (error) {
      console.error('获取每日心语失败:', error);
      return [];
    }
  }

  // 根据类型获取消息
  async getMessagesByType(type: 'quote' | 'selfcare'): Promise<DailyMessage[]> {
    try {
      const rows = await this.query(
        'SELECT * FROM daily_messages WHERE type = ? ORDER BY weight DESC, id',
        [type]
      );
      return rows.map(this.mapRowToMessage);
    } catch (error) {
      console.error('获取指定类型每日心语失败:', error);
      return [];
    }
  }

  // 获取今日推荐消息（基于日期seed的稳定随机）
  async getTodayMessage(date: string): Promise<{ quote: DailyMessage | null; selfcare: DailyMessage | null }> {
    try {
      // 首先检查是否有用户设置的今日override
      const override = await this.getTodayOverride(date);
      if (override) {
        const overrideMessage = await this.getMessage(override.messageId);
        if (overrideMessage) {
          if (overrideMessage.type === 'quote') {
            const selfcareMessage = await this.getRandomMessageByType('selfcare', date);
            return { quote: overrideMessage, selfcare: selfcareMessage };
          } else {
            const quoteMessage = await this.getRandomMessageByType('quote', date);
            return { quote: quoteMessage, selfcare: overrideMessage };
          }
        }
      }

      // 没有override，使用基于日期的稳定随机
      const quote = await this.getRandomMessageByType('quote', date);
      const selfcare = await this.getRandomMessageByType('selfcare', date);

      return { quote, selfcare };
    } catch (error) {
      console.error('获取今日消息失败:', error);
      return { quote: null, selfcare: null };
    }
  }

  // 刷新今日消息（设置新的override）
  async refreshTodayMessage(date: string, type?: 'quote' | 'selfcare'): Promise<DailyMessage | null> {
    try {
      let messagePool: DailyMessage[];
      
      if (type) {
        messagePool = await this.getMessagesByType(type);
      } else {
        // 随机选择类型
        const randomType = Math.random() > 0.5 ? 'quote' : 'selfcare';
        messagePool = await this.getMessagesByType(randomType);
      }

      if (messagePool.length === 0) return null;

      // 随机选择一条消息
      const randomMessage = messagePool[Math.floor(Math.random() * messagePool.length)];
      
      // 保存为今日override
      await this.setTodayOverride(date, randomMessage.id!);
      
      return randomMessage;
    } catch (error) {
      console.error('刷新今日消息失败:', error);
      return null;
    }
  }

  // 获取单个消息
  async getMessage(id: number): Promise<DailyMessage | null> {
    try {
      const rows = await this.query('SELECT * FROM daily_messages WHERE id = ?', [id]);
      return rows.length > 0 ? this.mapRowToMessage(rows[0]) : null;
    } catch (error) {
      console.error('获取消息失败:', error);
      return null;
    }
  }

  // 添加新消息
  async addMessage(messageData: Omit<DailyMessage, 'id' | 'createdAt' | 'updatedAt'>): Promise<DailyMessage | null> {
    try {
      const now = new Date().toISOString();
      const sql = `
        INSERT INTO daily_messages (type, content, author, locale, weight, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      
      const params = [
        messageData.type,
        messageData.content,
        messageData.author || null,
        messageData.locale,
        messageData.weight,
        now,
        now
      ];

      const result = await this.run(sql, params);
      
      if (result.lastInsertRowid) {
        return await this.getMessage(result.lastInsertRowid);
      }
      
      return null;
    } catch (error) {
      console.error('添加消息失败:', error);
      return null;
    }
  }

  // 更新消息
  async updateMessage(id: number, updates: Partial<DailyMessage>): Promise<DailyMessage | null> {
    try {
      const existingMessage = await this.getMessage(id);
      if (!existingMessage) return null;

      const updatedAt = new Date().toISOString();
      const sql = `
        UPDATE daily_messages 
        SET type = ?, content = ?, author = ?, locale = ?, weight = ?, updated_at = ?
        WHERE id = ?
      `;
      
      const params = [
        updates.type !== undefined ? updates.type : existingMessage.type,
        updates.content !== undefined ? updates.content : existingMessage.content,
        updates.author !== undefined ? updates.author : existingMessage.author,
        updates.locale !== undefined ? updates.locale : existingMessage.locale,
        updates.weight !== undefined ? updates.weight : existingMessage.weight,
        updatedAt,
        id
      ];

      await this.run(sql, params);
      return await this.getMessage(id);
    } catch (error) {
      console.error('更新消息失败:', error);
      return null;
    }
  }

  // 删除消息
  async deleteMessage(id: number): Promise<boolean> {
    try {
      await this.run('DELETE FROM daily_messages WHERE id = ?', [id]);
      return true;
    } catch (error) {
      console.error('删除消息失败:', error);
      return false;
    }
  }

  // 获取基于日期seed的随机消息
  private async getRandomMessageByType(type: 'quote' | 'selfcare', date: string): Promise<DailyMessage | null> {
    try {
      const messages = await this.getMessagesByType(type);
      if (messages.length === 0) return null;

      // 使用日期作为种子创建稳定的随机数
      const seed = this.createDateSeed(date, type);
      const index = seed % messages.length;
      
      return messages[index];
    } catch (error) {
      console.error('获取随机消息失败:', error);
      return null;
    }
  }

  // 创建基于日期的种子
  private createDateSeed(date: string, type: string): number {
    const dateStr = date + type;
    let hash = 0;
    for (let i = 0; i < dateStr.length; i++) {
      const char = dateStr.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  // 获取今日override设置
  private async getTodayOverride(date: string): Promise<DailyMessageOverride | null> {
    try {
      const key = `dailyMessageOverride_${date}`;
      const rows = await this.query('SELECT value FROM settings WHERE key = ?', [key]);
      
      if (rows.length > 0 && rows[0].value) {
        const data = JSON.parse(rows[0].value);
        return { date: data.date, messageId: data.messageId };
      }
      
      return null;
    } catch (error) {
      console.error('获取今日override失败:', error);
      return null;
    }
  }

  // 设置今日override
  private async setTodayOverride(date: string, messageId: number): Promise<void> {
    try {
      const key = `dailyMessageOverride_${date}`;
      const value = JSON.stringify({ date, messageId });
      const updatedAt = new Date().toISOString();

      const sql = `
        INSERT OR REPLACE INTO settings (key, value, updated_at)
        VALUES (?, ?, ?)
      `;
      
      await this.run(sql, [key, value, updatedAt]);
    } catch (error) {
      console.error('设置今日override失败:', error);
    }
  }

  // 数据映射方法
  private mapRowToMessage(row: any): DailyMessage {
    return {
      id: row.id,
      type: row.type,
      content: row.content,
      author: row.author,
      locale: row.locale,
      weight: row.weight,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }
}

// 导出实例
export const dailyMessageService = new DailyMessageService();
