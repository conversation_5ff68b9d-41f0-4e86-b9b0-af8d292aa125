// 模拟数据 - 用于浏览器环境测试
import type { Appointment, AppointmentStatus, AppointmentType } from '../types/schedule';

export interface MockQuickNote {
  id: number;
  content: string;
  createdAt: string;
}

export interface MockDailyMessage {
  id: number;
  message: string;
  category: string;
}

// 生成今天的模拟预约数据
export const getTodayMockAppointments = (): Appointment[] => {
  const now = new Date();
  const today = now.toISOString().split('T')[0];
  
  // 计算下一个预约时间（从现在开始15分钟后）
  const nextAppointment = new Date(now.getTime() + 15 * 60 * 1000);
  const futureAppointment = new Date(now.getTime() + 120 * 60 * 1000);
  
  return [
    {
      id: '1',
      visitorName: '张小明',
      visitorPhone: '13800138001',
      visitorAge: 28,
      visitorGender: '男',
      date: today,
      startTime: nextAppointment.toTimeString().slice(0, 5),
      endTime: new Date(nextAppointment.getTime() + 50 * 60 * 1000).toTimeString().slice(0, 5),
      duration: 50,
      type: '个体咨询' as AppointmentType,
      status: '已确认' as AppointmentStatus,
      urgency: '普通',
      room: '咨询室A',
      therapistId: 'therapist1',
      therapistName: '心理咨询师',
      subject: '焦虑情绪咨询',
      description: '工作压力引起的焦虑症状',
      reminderEnabled: true,
      reminderTime: 15,
      isFirstSession: false,
      sessionNumber: 3,
      fee: 200,
      paymentStatus: '已支付',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin'
    },
    {
      id: '2',
      visitorName: '李小红',
      visitorPhone: '13800138002',
      visitorAge: 35,
      visitorGender: '女',
      date: today,
      startTime: futureAppointment.toTimeString().slice(0, 5),
      endTime: new Date(futureAppointment.getTime() + 50 * 60 * 1000).toTimeString().slice(0, 5),
      duration: 50,
      type: '沙盘疗法' as AppointmentType,
      status: '已确认' as AppointmentStatus,
      urgency: '普通',
      room: '沙盘室B',
      therapistId: 'therapist1',
      therapistName: '心理咨询师',
      subject: '内心探索',
      description: '通过沙盘疗法进行内心世界探索',
      reminderEnabled: true,
      reminderTime: 15,
      isFirstSession: false,
      sessionNumber: 2,
      fee: 300,
      paymentStatus: '已支付',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin'
    },
    {
      id: '3',
      visitorName: '王小刚',
      visitorPhone: '13800138003',
      visitorAge: 42,
      visitorGender: '男',
      date: today,
      startTime: '16:30',
      endTime: '17:20',
      duration: 50,
      type: '家庭咨询' as AppointmentType,
      status: '已确认' as AppointmentStatus,
      urgency: '普通',
      room: '家庭咨询室',
      therapistId: 'therapist1',
      therapistName: '心理咨询师',
      subject: '家庭关系调和',
      description: '夫妻关系和亲子关系改善',
      reminderEnabled: true,
      reminderTime: 15,
      isFirstSession: true,
      sessionNumber: 1,
      fee: 400,
      paymentStatus: '已支付',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'admin'
    }
  ];
};

// 模拟快速笔记数据
export const getMockQuickNotes = (): MockQuickNote[] => {
  const now = new Date();
  return [
    {
      id: 1,
      content: '记得准备下午张小明的咨询材料',
      createdAt: new Date(now.getTime() - 30 * 60 * 1000).toISOString()
    },
    {
      id: 2,
      content: '采购新的沙具 - 动物系列缺货',
      createdAt: new Date(now.getTime() - 60 * 60 * 1000).toISOString()
    }
  ];
};

// 模拟每日消息数据
export const getMockDailyMessages = (): MockDailyMessage[] => {
  return [
    {
      id: 1,
      message: '每一个咨询都是一次心灵的相遇，珍惜与来访者的每一次对话。',
      category: 'motivation'
    },
    {
      id: 2,
      message: '记得关注自己的情绪状态，只有照顾好自己，才能更好地帮助他人。',
      category: 'self-care'
    },
    {
      id: 3,
      message: '今天是美好的一天，让我们用专业和温暖面对每一位来访者。',
      category: 'motivation'
    },
    {
      id: 4,
      message: '工作间隙记得休息一下，深呼吸，让心灵得到片刻的宁静。',
      category: 'self-care'
    }
  ];
};

// 浏览器环境模拟服务
export class MockBrowserServices {
  private static appointments = getTodayMockAppointments();
  private static quickNotes = getMockQuickNotes();
  private static dailyMessages = getMockDailyMessages();
  private static currentMessageIndex = 0;

  // 模拟今日预约服务
  static async getTodayAppointments(): Promise<Appointment[]> {
    return this.appointments;
  }

  static async getNextUpcomingAppointment(): Promise<Appointment | null> {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    for (const appointment of this.appointments) {
      const [hours, minutes] = appointment.startTime.split(':').map(Number);
      const appointmentTime = hours * 60 + minutes;
      
      if (appointmentTime > currentTime) {
        return appointment;
      }
    }
    return null;
  }

  static async getTimeToNextAppointment(): Promise<number | null> {
    const nextAppointment = await this.getNextUpcomingAppointment();
    if (!nextAppointment) return null;

    const now = new Date();
    const [hours, minutes] = nextAppointment.startTime.split(':').map(Number);
    const appointmentDate = new Date();
    appointmentDate.setHours(hours, minutes, 0, 0);

    const timeDiff = appointmentDate.getTime() - now.getTime();
    return Math.max(0, Math.floor(timeDiff / 1000)); // 返回秒数
  }

  // 模拟快速笔记服务
  static async getTodayQuickNotes(): Promise<MockQuickNote[]> {
    return this.quickNotes;
  }

  static async addQuickNote(content: string): Promise<MockQuickNote> {
    const newNote: MockQuickNote = {
      id: Date.now(),
      content,
      createdAt: new Date().toISOString()
    };
    this.quickNotes.unshift(newNote);
    return newNote;
  }

  static async getLatestQuickNote(): Promise<MockQuickNote | null> {
    return this.quickNotes.length > 0 ? this.quickNotes[0] : null;
  }

  // 模拟每日消息服务
  static async getTodayMessage(): Promise<MockDailyMessage> {
    // 使用日期作为种子，确保同一天返回相同消息
    const today = new Date().toDateString();
    const seed = today.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const index = seed % this.dailyMessages.length;
    return this.dailyMessages[index];
  }

  static async refreshTodayMessage(): Promise<MockDailyMessage> {
    // 刷新时使用随机索引
    this.currentMessageIndex = Math.floor(Math.random() * this.dailyMessages.length);
    return this.dailyMessages[this.currentMessageIndex];
  }
}
