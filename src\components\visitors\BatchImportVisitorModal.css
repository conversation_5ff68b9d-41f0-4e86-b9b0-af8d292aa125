/* 批量导入来访者模态框样式 */

.batch-import-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 75vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 工具栏样式 */
.batch-import-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

.toolbar-left {
  display: flex;
  gap: 8px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.row-count {
  font-size: 14px;
  color: var(--text-secondary, #6b7280);
  font-weight: 500;
}

/* 表格容器样式 */
.batch-import-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  max-height: 450px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
}

/* 表格样式 */
.batch-import-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 13px;
  min-width: 1200px; /* 确保表格有足够宽度 */
}

.batch-import-table thead {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(8px);
}

.batch-import-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #1e293b;
  border-bottom: 2px solid rgba(148, 163, 184, 0.3);
  border-right: 1px solid rgba(148, 163, 184, 0.2);
  white-space: nowrap;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.batch-import-table th:last-child {
  border-right: none;
}

.batch-import-table th.required::after {
  content: '';
  display: inline-block;
  width: 4px;
  height: 4px;
  background: var(--error, #ef4444);
  border-radius: 50%;
  margin-left: 4px;
  vertical-align: middle;
}

.batch-import-table td {
  padding: 10px 8px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.15);
  border-right: 1px solid rgba(148, 163, 184, 0.15);
  vertical-align: middle;
  position: relative;
  transition: background-color 0.2s ease;
}

.batch-import-table tbody tr:hover td {
  background-color: rgba(59, 130, 246, 0.05);
}

.batch-import-table td:last-child {
  border-right: none;
}

.batch-import-table td:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.batch-import-table td:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.batch-import-table tbody tr {
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.6);
}

.batch-import-table tbody tr:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.batch-import-table tbody tr:nth-child(even) {
  background: rgba(248, 250, 252, 0.8);
}

.batch-import-table tbody tr:nth-child(even):hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 100%);
}

.batch-import-table tbody tr.has-errors {
  background: rgba(254, 242, 242, 0.5);
}

.batch-import-table tbody tr.has-errors:hover {
  background: rgba(254, 242, 242, 0.8);
}

/* 列宽设置 */
.col-name { width: 100px; min-width: 100px; }
.col-gender { width: 70px; min-width: 70px; }
.col-age { width: 70px; min-width: 70px; }
.col-phone { width: 130px; min-width: 130px; }
.col-email { width: 160px; min-width: 160px; }
.col-emergency-contact { width: 110px; min-width: 110px; }
.col-emergency-phone { width: 130px; min-width: 130px; }
.col-occupation { width: 100px; min-width: 100px; }
.col-education { width: 100px; min-width: 100px; }
.col-address { width: 150px; min-width: 150px; }
.col-notes { width: 150px; min-width: 150px; }
.col-actions { width: 60px; min-width: 60px; text-align: center; }

/* 表格内的表单控件样式 */
.batch-import-table .form-group {
  margin: 0;
}

.batch-import-table .form-input,
.batch-import-table .form-select {
  width: 100%;
  min-height: 36px;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.batch-import-table .form-input:focus,
.batch-import-table .form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.batch-import-table .form-input.error,
.batch-import-table .form-select.error {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.batch-import-table .form-input.error:focus,
.batch-import-table .form-select.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 删除按钮样式 */
.delete-row-btn {
  padding: 6px 10px !important;
  min-height: 32px !important;
  color: #ef4444 !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  cursor: pointer !important;
}

.delete-row-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
  border-color: #ef4444 !important;
  color: #dc2626 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2) !important;
}

.delete-row-btn:active:not(:disabled) {
  transform: translateY(0) !important;
  box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3) !important;
}

.delete-row-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
}

/* 提示信息样式 */
.batch-import-tips {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-top: 12px;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

.tips-content h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.tips-content ul {
  margin: 0;
  padding-left: 24px;
  list-style-type: none;
}

.tips-content li {
  font-size: 14px;
  color: #475569;
  line-height: 1.6;
  margin-bottom: 8px;
  position: relative;
}

.tips-content li::before {
  content: '•';
  color: #3b82f6;
  font-weight: bold;
  position: absolute;
  left: -16px;
}

.tips-content li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .batch-import-table {
    min-width: 1000px;
  }
  
  .col-email { width: 140px; min-width: 140px; }
  .col-address { width: 120px; min-width: 120px; }
  .col-notes { width: 100px; min-width: 100px; }
}

@media (max-width: 768px) {
  .batch-import-container {
    max-height: 60vh;
  }
  
  .batch-import-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left {
    justify-content: center;
  }
  
  .toolbar-right {
    justify-content: center;
  }
  
  .batch-import-table-container {
    max-height: 300px;
  }
  
  .batch-import-table {
    min-width: 800px;
    font-size: 12px;
  }
  
  .batch-import-table th,
  .batch-import-table td {
    padding: 6px 4px;
  }
  
  .batch-import-table .form-input,
  .batch-import-table .form-select {
    min-height: 28px;
    padding: 4px 6px;
    font-size: 12px;
  }
}

/* 滚动条样式 */
.batch-import-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.batch-import-table-container::-webkit-scrollbar-track {
  background: var(--bg-secondary, #f9fafb);
  border-radius: 4px;
}

.batch-import-table-container::-webkit-scrollbar-thumb {
  background: var(--border-medium, #d1d5db);
  border-radius: 4px;
}

.batch-import-table-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary, #6b7280);
}

/* 加载状态 */
.batch-import-container.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 错误提示增强 */
.batch-import-table .form-error {
  display: none; /* 在表格中隐藏错误文本，通过边框颜色显示 */
}

/* 必填字段标识 */
.batch-import-table th.required {
  position: relative;
}

.batch-import-table th.required::before {
  content: '*';
  color: var(--error, #ef4444);
  font-weight: bold;
  margin-right: 2px;
}

/* 表格行号 */
.batch-import-table tbody tr::before {
  content: counter(row-number);
  counter-increment: row-number;
  position: absolute;
  left: -30px;
  width: 20px;
  text-align: center;
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  font-weight: 500;
}

.batch-import-table tbody {
  counter-reset: row-number;
}

.batch-import-table-container {
  position: relative;
  padding-left: 30px;
}

.batch-import-table-container::before {
  content: '#';
  position: absolute;
  left: 5px;
  top: 45px;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary, #6b7280);
}