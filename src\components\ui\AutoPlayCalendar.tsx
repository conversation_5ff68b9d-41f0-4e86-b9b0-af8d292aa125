import React, { useState, useEffect, useRef } from 'react';
import { Calendar as CalendarIcon, Clock, ChevronLeft, ChevronRight, Home } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import type { Appointment } from '../../types/schedule';
import './Calendar.css';
import './AutoPlayCalendar.css';

interface AutoPlayCalendarProps {
  appointments?: Appointment[];
  onDateSelect?: (date: Date) => void;
  onAppointmentClick?: (appointment: Appointment) => void;
  className?: string;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  appointments: Appointment[];
}

const AutoPlayCalendar: React.FC<AutoPlayCalendarProps> = ({
  appointments = [],
  onDateSelect,
  onAppointmentClick,
  className = ''
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [animationDirection, setAnimationDirection] = useState<'forward' | 'backward'>('forward');
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // 处理用户交互状态
  const handleUserInteraction = (interacting: boolean) => {
    setIsUserInteracting(interacting);
    setIsPaused(interacting);
  };

  // 处理日期点击
  const handleDateClick = (day: CalendarDay) => {
    handleUserInteraction(true);
    setSelectedDate(day.date);
    onDateSelect?.(day.date);
    // 2秒后恢复动画
    setTimeout(() => handleUserInteraction(false), 2000);
  };

  // 处理预约点击
  const handleAppointmentClick = (appointment: Appointment, e: React.MouseEvent) => {
    e.stopPropagation();
    handleUserInteraction(true);
    onAppointmentClick?.(appointment);
    // 2秒后恢复动画
    setTimeout(() => handleUserInteraction(false), 2000);
  };

  // 手动导航函数
  const navigateMonth = (direction: 'prev' | 'next') => {
    handleUserInteraction(true);
    setAnimationDirection(direction === 'next' ? 'forward' : 'backward');
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'next') {
        newDate.setMonth(prev.getMonth() + 1);
      } else {
        newDate.setMonth(prev.getMonth() - 1);
      }
      return newDate;
    });
    // 3秒后恢复动画
    setTimeout(() => handleUserInteraction(false), 3000);
  };

  // 回到今天
  const goToToday = () => {
    handleUserInteraction(true);
    const today = new Date();
    setCurrentDate(today);
    setSelectedDate(today);
    // 2秒后恢复动画
    setTimeout(() => handleUserInteraction(false), 2000);
  };

  // 获取当前月份的所有日期
  const getCalendarDays = (): CalendarDay[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // 获取第一周的开始日期（周一开始）
    const startDate = new Date(firstDay);
    const dayOfWeek = firstDay.getDay();
    startDate.setDate(firstDay.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
    
    // 生成42天的日历网格（6周 × 7天）
    const days: CalendarDay[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const dateStr = date.toISOString().split('T')[0];
      const dayAppointments = appointments.filter(apt => apt.date === dateStr);
      
      days.push({
        date: new Date(date),
        isCurrentMonth: date.getMonth() === month,
        isToday: date.getTime() === today.getTime(),
        appointments: dayAppointments
      });
    }
    
    return days;
  };

  // 格式化月份年份
  const formatMonthYear = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月`;
  };

  // 获取预约状态的颜色类名
  const getAppointmentStatusClass = (status: string) => {
    switch (status) {
      case '已确认': return 'status-confirmed';
      case '待确认': return 'status-pending';
      case '进行中': return 'status-ongoing';
      case '已完成': return 'status-completed';
      case '已取消': return 'status-cancelled';
      case '缺席': return 'status-absent';
      default: return 'status-default';
    }
  };

  const calendarDays = getCalendarDays();
  const weekDays = ['一', '二', '三', '四', '五', '六', '日'];

  return (
    <motion.div 
      ref={containerRef}
      className={`liquid-calendar auto-play-calendar ${className} ${isPaused ? 'paused' : ''}`}
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ 
        opacity: 1, 
        y: 0, 
        scale: 1
      }}
      transition={{ 
        duration: 0.6, 
        ease: [0.4, 0, 0.2, 1],
        staggerChildren: 0.1
      }}
      onMouseEnter={() => handleUserInteraction(true)}
      onMouseLeave={() => setTimeout(() => handleUserInteraction(false), 1000)}
    >
      {/* 日历头部 */}
      <motion.div 
        className="calendar-header"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="calendar-title">
          <CalendarIcon className="calendar-icon" />
          <h3>日程安排</h3>
        </div>
        <div className="calendar-navigation">
          <button 
            className="nav-btn"
            onClick={() => navigateMonth('prev')}
            title="上一月"
          >
            <ChevronLeft size={16} />
          </button>
          <motion.span 
            className="current-month"
            key={`${currentDate.getFullYear()}-${currentDate.getMonth()}`}
            initial={{ 
              opacity: 0, 
              x: animationDirection === 'forward' ? 20 : -20
            }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {formatMonthYear(currentDate)}
          </motion.span>
          <button 
            className="nav-btn"
            onClick={() => navigateMonth('next')}
            title="下一月"
          >
            <ChevronRight size={16} />
          </button>
          <button 
            className="nav-btn today-btn"
            onClick={goToToday}
            title="回到今天"
          >
            <Home size={14} />
          </button>
        </div>
      </motion.div>

      {/* 星期标题 */}
      <div className="calendar-weekdays">
        {weekDays.map((day) => (
          <div key={day} className="weekday">
            {day}
          </div>
        ))}
      </div>

      {/* 日历网格 */}
      <motion.div 
        className="calendar-grid"
        key={`${currentDate.getFullYear()}-${currentDate.getMonth()}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      >
        <AnimatePresence mode="wait">
          {calendarDays.map((day, index) => {
            return (
              <motion.div
                key={`${day.date.getTime()}-${currentDate.getMonth()}`}
                className={`calendar-day auto-play-day ${
                  day.isCurrentMonth ? 'current-month' : 'other-month'
                } ${
                  day.isToday ? 'today' : ''
                } ${
                  day.appointments.length > 0 ? 'has-appointments' : ''
                } ${
                  selectedDate && selectedDate.getTime() === day.date.getTime() ? 'selected' : ''
                } ${
                  isPaused ? 'animation-paused' : ''
                }`}
                onClick={() => handleDateClick(day)}
                initial={{ opacity: 0 }}
                animate={{ opacity: day.isCurrentMonth ? 1 : 0.4 }}
                transition={{ 
                  delay: index * 0.01,
                  duration: 0.2
                }}
              >
                <motion.span 
                  className="day-number"
                  style={{
                    color: day.isToday ? '#6366f1' : (day.isCurrentMonth ? '#475569' : '#94a3b8')
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {day.date.getDate()}
                </motion.span>
                
                {/* 预约指示器 - 自动动画 */}
                <AnimatePresence>
                  {day.appointments.length > 0 && (
                    <motion.div 
                      className="appointments-container"
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ 
                        scale: 1, 
                        opacity: 1
                      }}
                      exit={{ scale: 0, opacity: 0 }}
                      transition={{ 
                        type: "spring", 
                        stiffness: 300, 
                        damping: 25
                      }}
                    >
                      {day.appointments.slice(0, 3).map((appointment, aptIndex) => (
                        <motion.div
                          key={appointment.id}
                          className={`appointment-indicator ${getAppointmentStatusClass(appointment.status)}`}
                          title={`${appointment.startTime} - ${appointment.subject}`}
                          onClick={(e) => handleAppointmentClick(appointment, e)}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ 
                            delay: aptIndex * 0.05,
                            duration: 0.2
                          }}
                        >
                          <Clock size={10} />
                          <span className="appointment-time">{appointment.startTime}</span>
                        </motion.div>
                      ))}
                      {day.appointments.length > 3 && (
                        <motion.div 
                          className="more-appointments"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ 
                            delay: 0.15,
                            duration: 0.2
                          }}
                        >
                          +{day.appointments.length - 3}
                        </motion.div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </motion.div>


    </motion.div>
  );
};

export default AutoPlayCalendar;