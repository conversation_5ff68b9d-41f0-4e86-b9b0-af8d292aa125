# 设计系统使用指南

这个设计系统是为了确保整个应用的视觉一致性和开发效率而建立的。

## 📁 文件结构

```
src/
├── styles/
│   ├── variables.css     # 设计令牌变量
│   └── components.css    # 通用组件样式
├── components/
│   └── ui/              # 可复用UI组件库
│       ├── Button.tsx
│       ├── Card.tsx
│       ├── Form.tsx
│       ├── DataDisplay.tsx
│       ├── Layout.tsx
│       └── index.ts
├── config/
│   ├── routes.ts        # 路由配置
│   └── themes.ts        # 主题配置
└── types/
    └── visitor.ts       # 类型定义
```

## 🎨 设计令牌

设计令牌定义在 `src/styles/variables.css` 中，包括：

### 颜色系统
- **主色调**: `--primary-blue` 系列
- **辅助色**: `--secondary-teal`, `--accent-amber`, `--accent-purple`
- **中性色**: `--gray-50` 到 `--gray-900`
- **语义色**: `--success`, `--warning`, `--error`, `--info`

### 间距系统
- `--spacing-xs` (0.25rem) 到 `--spacing-6xl` (4rem)

### 字体系统
- 大小: `--text-xs` (0.75rem) 到 `--text-5xl` (3rem)
- 粗细: `--font-light` 到 `--font-bold`

### 圆角和阴影
- 圆角: `--radius-xs` 到 `--radius-full`
- 阴影: `--shadow-xs` 到 `--shadow-2xl`

## 🧩 组件库使用

### 导入组件
```tsx
import { Button, Card, Input, Badge } from '../ui';
```

### 按钮组件
```tsx
<Button variant="primary" size="lg" leftIcon={<Plus />}>
  创建新项目
</Button>
```

**变体**: `primary`, `secondary`, `success`, `warning`, `danger`, `ghost`
**尺寸**: `sm`, `md`, `lg`

### 卡片组件
```tsx
<Card>
  <CardHeader title="标题" subtitle="副标题" />
  <CardContent>
    内容区域
  </CardContent>
  <CardFooter>
    操作按钮
  </CardFooter>
</Card>
```

### 表单组件
```tsx
<Input
  label="用户名"
  required
  placeholder="请输入用户名"
  error="用户名不能为空"
  leftIcon={<User />}
/>

<Select
  label="性别"
  options={[
    { value: 'male', label: '男' },
    { value: 'female', label: '女' }
  ]}
/>
```

### 页面布局
```tsx
<PageContainer>
  <PageHeader
    title="页面标题"
    subtitle="页面描述"
    actions={<Button>操作按钮</Button>}
  />
  
  <Grid cols={3} gap="lg">
    <Card>内容1</Card>
    <Card>内容2</Card>
    <Card>内容3</Card>
  </Grid>
</PageContainer>
```

## 📱 响应式设计

所有组件都支持响应式设计：

- **移动端** (< 768px): 单列布局，简化操作
- **平板端** (768px - 1024px): 适中的列数
- **桌面端** (> 1024px): 完整功能布局

## 🎯 最佳实践

### 1. 使用设计令牌
```css
/* ✅ 推荐 */
.my-component {
  color: var(--text-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
}

/* ❌ 不推荐 */
.my-component {
  color: #333;
  padding: 16px;
  border-radius: 8px;
}
```

### 2. 组件组合
```tsx
/* ✅ 推荐 - 使用现有组件 */
<Card>
  <CardContent>
    <Button variant="primary">操作</Button>
  </CardContent>
</Card>

/* ❌ 不推荐 - 重新造轮子 */
<div className="custom-card">
  <div className="custom-content">
    <button className="custom-button-primary">操作</button>
  </div>
</div>
```

### 3. 一致的命名
- 组件名使用 PascalCase
- CSS 类名使用 kebab-case
- 变量名使用 camelCase

### 4. 可访问性
- 使用语义化 HTML
- 提供适当的 ARIA 标签
- 确保键盘导航支持
- 保持适当的颜色对比度

## 🔧 扩展指南

### 添加新组件
1. 在 `src/components/ui/` 下创建组件文件
2. 使用设计令牌定义样式
3. 添加 TypeScript 类型定义
4. 在 `index.ts` 中导出
5. 编写使用示例

### 添加新主题
1. 在 `src/config/themes.ts` 中定义主题
2. 确保所有颜色都有对应的变体
3. 测试在不同主题下的表现

### 添加新页面
1. 使用 `PageContainer` 作为根容器
2. 使用 `PageHeader` 定义页面标题
3. 使用现有的 UI 组件构建界面
4. 遵循既定的布局模式

## 📊 性能优化

- 使用 CSS 变量减少重复样式
- 组件懒加载
- 图标按需导入
- 避免内联样式

## 🤝 开发约定

1. **组件职责单一**: 每个组件只负责一个功能
2. **样式隔离**: 使用 CSS Modules 或组件级样式
3. **类型安全**: 为所有 props 定义 TypeScript 类型
4. **文档齐全**: 为复杂组件提供使用示例
5. **测试覆盖**: 为关键组件编写测试用例

通过遵循这些指南，您可以：
- 🎯 确保视觉一致性
- ⚡ 提高开发效率
- 🛡️ 减少维护成本
- 📈 提升用户体验
