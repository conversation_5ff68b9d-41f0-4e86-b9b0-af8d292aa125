/* 表单弹窗组件样式 */

.form-modal-form {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

/* 表单主体 */
.form-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
  min-height: 0;
}

/* 表单底部操作栏 */
.form-modal-footer {
  flex-shrink: 0;
  padding: 16px 24px 20px 24px;
  border-top: 1px solid var(--border-light, #e5e7eb);
  background: var(--bg-secondary, #FAFBFC);
}

.form-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 表单布局组件 */
.form-section {
  margin-bottom: 28px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary, #111827);
  margin: 0 0 14px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid var(--border-light, #e5e7eb);
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-section-subtitle {
  font-size: 14px;
  color: var(--text-secondary, #6b7280);
  margin: -8px 0 16px 0;
  line-height: 1.4;
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  gap: 16px;
}

.form-grid-1 {
  grid-template-columns: 1fr;
}

.form-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.form-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.form-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 表单行布局 */
.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

/* 表单字段样式 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #111827);
  line-height: 1.4;
}

.form-label.required::after {
  content: ' *';
  color: var(--error, #ef4444);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary, #111827);
  background: var(--bg-primary, #ffffff);
  border: 1px solid var(--border-medium, #d1d5db);
  border-radius: 6px;
  transition: all 0.15s ease;
  box-sizing: border-box;
  min-height: 40px;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-blue, #3B82F6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--error, #ef4444);
  background: rgba(239, 68, 68, 0.05);
}

.form-input.error:focus,
.form-select.error:focus,
.form-textarea.error:focus {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-error {
  font-size: 12px;
  color: var(--error, #ef4444);
  line-height: 1.4;
}

.form-help {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  line-height: 1.4;
}

/* 复选框和单选框样式 */
.form-checkbox-group,
.form-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.form-checkbox-item,
.form-radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.form-checkbox-item input,
.form-radio-item input {
  width: auto;
  min-height: auto;
  margin: 0;
  cursor: pointer;
}

.form-checkbox-item label,
.form-radio-item label {
  font-size: 14px;
  color: var(--text-primary, #111827);
  cursor: pointer;
  margin: 0;
}

/* 特殊字段组合 */
.form-field-group {
  display: flex;
  gap: 8px;
  align-items: end;
}

.form-field-group .form-group {
  flex: 1;
}

.form-field-addon {
  padding: 10px 12px;
  background: var(--bg-secondary, #f9fafb);
  border: 1px solid var(--border-medium, #d1d5db);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text-secondary, #6b7280);
  min-height: 40px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-modal-body {
    padding: 20px 16px;
  }
  
  .form-modal-footer {
    padding: 12px 16px 20px 16px;
  }
  
  .form-modal-actions {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .form-modal-actions button {
    width: 100%;
  }
  
  .form-grid-2,
  .form-grid-3,
  .form-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .form-section {
    margin-bottom: 24px;
  }
  
  .form-field-group {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .form-modal-body {
    padding: 16px 12px;
  }
  
  .form-modal-footer {
    padding: 12px;
  }
  
  .form-section {
    margin-bottom: 20px;
  }
  
  .form-row {
    gap: 12px;
  }
}
