/* EditSandToolModal 专用样式 */

/* 标签输入控件样式 */
.tag-input-container {
  margin-bottom: 8px;
}

.tag-input-row {
  display: flex;
  gap: 8px;
  align-items: flex-end;
  margin-bottom: 12px;
}

.tag-input-field {
  flex: 1;
  min-width: 0;
}

.tag-add-button {
  flex-shrink: 0;
  height: 40px; /* 与输入框高度一致 */
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tag-add-button:hover {
  background-color: #2563eb;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background-color: #e0e7ff;
  color: #3730a3;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  border: 1px solid #c7d2fe;
}

.tag-remove-btn {
  background: transparent;
  border: none;
  color: #3730a3;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  font-weight: bold;
  margin-left: 2px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.tag-remove-btn:hover {
  background-color: rgba(55, 48, 163, 0.2);
  color: #1e1b4b;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .tag-input-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .tag-add-button {
    width: 100%;
    height: 36px;
  }
}
